import * as React from 'react';
import { useEffect, useState } from "react";
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableContainer from '@mui/material/TableContainer';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import Paper from '@mui/material/Paper';
import { Box, Button, CircularProgress, FormControl, IconButton, MenuItem, Select, Snackbar, Tooltip } from '@mui/material';
import { ArrowBack, Check, Clear, Done, Edit } from '@mui/icons-material';
import moment from 'moment';
import httpclient from '../../Utils';
import MuiAlert from "@mui/material/Alert";
import useTokenRefresh from '../../Hooks/useTokenRefresh';


const Alert = React.forwardRef(function Alert(props, ref) {
    return <MuiAlert elevation={6} ref={ref} variant="filled" {...props} />;
});


export default function BasicTableShopify(props) {
    const [showBranchEdit, setShowBranchEdit] = useState("");
    const [lineBranchName, setLineBranchName] = useState("");
    const [branchLoading, setBranchLoading] = useState(false);
    const [currentBranchID, setCurrentBranchID] = useState("");
    const [productDetails, setProductDetails] = useState("");
    const [open, setOpen] = useState(false);
    const [message, setMessage] = useState("");
    const [messageState, setMessageState] = useState("");

    const { getTokenRefreshed: refresh, open: tokenOpen, overlay: overlay, setOpen: setTokenOpen, message: tokenMessage, messageState: tokenMessageState } = useTokenRefresh();

    const handleChangeLineBranch = (row) => {
        setProductDetails(row);
        setBranchLoading(true);
        httpclient
            .post(`shopify/change-order-line-location`, {
                order_id: row.shopify_order_id,
                order_line_item_id: row.shopify_lineItem_id,
                branchId: lineBranchName,
            })
            .then(({ data }) => {
                if (data.status === 200) {
                    setBranchLoading(false);
                    setOpen(true);
                    setMessageState("success");
                    setMessage(data.message);
                    setProductDetails("");
                    setCurrentBranchID("");
                    setShowBranchEdit(false);
                    setTimeout(() => {
                        props.handleRefetch();
                    }, 1000);
                } else {
                    setCurrentBranchID("");
                    setProductDetails("");
                    setBranchLoading(false);
                    setOpen(true);
                    setMessageState("error");
                    setMessage(data.message);
                }
            })
            .catch((err) => {
                setCurrentBranchID("");
                setProductDetails("");
                if (err.response.status === 401) {
                    refresh();
                    setOpen(tokenOpen);
                    setMessage(tokenMessage);
                    setMessageState("error");
                } else if (err.response.status === 422) {
                    const errorMessages = Object.values(err.response.data.errors).flat();
                    setOpen(true);
                    setMessage(errorMessages);
                    setMessageState("error");
                    setBranchLoading(false);
                } else if (err.response.status === 400) {
                    const errorMessages = Object.values(err.response.data.errors).flat();
                    setOpen(true);
                    setMessage(errorMessages);
                    setMessageState("error");
                    setBranchLoading(false);

                } else {
                    setOpen(true);
                    setMessage(err.response.data.message);
                    setMessageState("error");
                    setBranchLoading(false);
                }
            });

    };


    const handleCloseSnack = (event, reason) => {
        if (reason === "clickaway") {
            return;
        }
        setOpen(false);
        setTokenOpen(false);
    };

    return (
        <TableContainer component={Paper} style={{ overflowY: "auto", height: "380px" }}>
            <Table sx={{ minWidth: 650 }} aria-label="simple table">
                <TableHead>
                    <TableRow>
                        {props.columns.map(col => (
                            <TableCell style={{ fontWeight: "700" }}>{col.name}</TableCell>
                        ))}
                    </TableRow>
                </TableHead>
                {props.rows?.length > 0 ?
                    <TableBody>
                        {props.rows.map((row, index1) => {
                            var branchNames = "";
                            branchNames =
                                row.assign_to_branch?.length > 0 &&
                                row.assign_to_branch?.map(
                                    (branch, index) =>
                                        branch?.branch?.name +
                                        (row.assign_to_branch?.length === index + 1 ? "" : ", ")
                                );
                            return (
                                <TableRow
                                    key={row.name}
                                //onClick={() => (props.handleView && row.shopify_order_id) ? props.handleView(row) : ""}
                                //sx={{ cursor: (props.handleView && row.shopify_order_id) ? "pointer" : "default", '&:last-child td, &:last-child th': { border: 0 } }}
                                >
                                    {props.columns.map((col, index) => (
                                        col.id === "reason" ?
                                            <TableCell component="th" scope="row" style={{ width: "100%", display: "flex" }}>
                                                {row.reportType !== "exceptionalOrder" ? <Check color='primary' fontSize='small' /> : <Clear color='error' fontSize='small' />}
                                                {row[col.id]}
                                            </TableCell>

                                            :
                                            col.id === "image" ? (

                                                <TableCell component="th" scope="row">
                                                    <img
                                                        src={row.image}
                                                        style={{
                                                            width: "100px",
                                                            height: "100px",
                                                            objectFit: "cover",
                                                        }}
                                                        alt={row.firstname}
                                                    />
                                                </TableCell>
                                            ) :
                                                col.id === "assign_to_branch" ? (
                                                    <TableCell component="th" scope="row">
                                                        {!showBranchEdit ? (
                                                            <span>
                                                                {branchNames}
                                                                {row.assign_to_branch?.length > 0 && row.orderLocationChangeEnabled === 1 &&
                                                                    <IconButton

                                                                        onClick={() => {
                                                                            setShowBranchEdit(true);
                                                                            setCurrentBranchID(row.shopify_lineItem_id);

                                                                            setLineBranchName(
                                                                                row.assign_to_branch[0]?.branch?.id
                                                                            );
                                                                        }}
                                                                    >
                                                                        <Edit fontSize="small" color="primary" />
                                                                    </IconButton>
                                                                }

                                                            </span>
                                                        ) : row.shopify_lineItem_id !== currentBranchID ? (
                                                            <span>
                                                                {branchNames}
                                                                <IconButton

                                                                    onClick={() => {
                                                                        setShowBranchEdit(true);
                                                                        setCurrentBranchID(row.shopify_lineItem_id);
                                                                        setLineBranchName(
                                                                            row.assign_to_branch[0]?.branch?.id
                                                                        );
                                                                    }}
                                                                >
                                                                    <Edit fontSize="small" color="primary" />
                                                                </IconButton>
                                                            </span>
                                                        ) : (
                                                            <Box display={"flex"} flexDirection="row">
                                                                <FormControl
                                                                    style={{ width: "200px", marginBottom: "10px" }}
                                                                >
                                                                    <Select
                                                                        value={lineBranchName}
                                                                        onChange={(e) =>
                                                                            setLineBranchName(e.target.value)
                                                                        }
                                                                        name="lineBranchName"
                                                                    >
                                                                        <MenuItem value={""}>
                                                                            <em>Select Branch</em>
                                                                        </MenuItem>
                                                                        {row.stock_on_hand?.soh?.length > 0 &&
                                                                            row.stock_on_hand?.soh?.map((stock) => (
                                                                                <MenuItem
                                                                                    value={stock?.warehouseID}
                                                                                    key={stock?.warehouseID}
                                                                                >
                                                                                    {stock?.name}
                                                                                </MenuItem>
                                                                            ))}
                                                                    </Select>
                                                                </FormControl>

                                                                {branchLoading ? (
                                                                    <CircularProgress
                                                                        style={{
                                                                            height: "25px",
                                                                            width: "25px",
                                                                            marginLeft: "10px",
                                                                            position: "relative",
                                                                            top: "10px",
                                                                        }}
                                                                    />
                                                                ) : (
                                                                    <Box display={"flex"} flexDirection="column">
                                                                        <Tooltip title="Back">
                                                                            <Button
                                                                                variant="contained"
                                                                                size="small"
                                                                                color="primary"
                                                                                onClick={() => setShowBranchEdit(false)}
                                                                                sx={{
                                                                                    marginLeft: "10px",
                                                                                    marginBottom: "5px",
                                                                                }}
                                                                            >
                                                                                <ArrowBack fontSize="small" />
                                                                            </Button>
                                                                        </Tooltip>
                                                                        <Tooltip title="Change Branch">
                                                                            <Button
                                                                                variant="contained"
                                                                                color="primary"
                                                                                size="small"
                                                                                onClick={() => handleChangeLineBranch(row)}
                                                                                sx={{ marginLeft: "10px" }}
                                                                            >
                                                                                <Done fontSize="small" />
                                                                            </Button>
                                                                        </Tooltip>
                                                                    </Box>
                                                                )}
                                                            </Box>
                                                        )}
                                                    </TableCell>
                                                ) :
                                                    <TableCell component="th" scope="row"
                                                        onClick={() => props.handleView(row)}
                                                        sx={{ cursor: "pointer", '&:last-child td, &:last-child th': { border: 0 } }}>
                                                        {col.id === "sn" ? (index1 + 1) :
                                                            //col.id === "assign_to_branch" ? row[col.id][0]?.branch?.name :
                                                            col.id === "erpBranchName" ? row.branch_details ? row.branch_details?.branchName : row.erpBranch?.branchName :
                                                                col.id === "erpAccountNameNetSuite" ? row.customer?.erpAccountName :
                                                                    col.id === "stockName" ? (row.stock.stockName || row.stock.ciStockName || row.stock.stockNameDefault) :
                                                                        col.id === "actualSOH" ? Math.max(row.actualSOH, 0) :
                                                                            col.id === "LOCNO" ? row.LOCATION.LOCNO :
                                                                                col.id === "LNAME" ? row.LOCATION.LNAME :
                                                                                    col.id === "stockCode" ? (row.stockCode || row.ciStockCode || row.stock.stockCode) :
                                                                                        col.id === "stockNameDefault" ? (row.stockNameDefault || row.ciStockName) :
                                                                                            col.id === "erpAccountCustomerID" ? row.erpAccountCustomerID.erpAccountCustomerID :
                                                                                                col.id === "erpAccountCustomerIDNet" ? row.customer.erpAccountCustomerID :
                                                                                                    col.id === "erpAccountName" ? row.erpAccountCustomerID.erpAccountName :
                                                                                                        col.id === "erpAccountNameNet" ? row.customer.erpAccountName :
                                                                                                            col.id === "warehouse_details" ? row.warehouse_details.name :
                                                                                                                col.id === "shopify_updated_at" ? moment(row.shopify_updated_at).format("ddd, DD MMM YYYY, h:mm A") :
                                                                                                                    col.id === "updated_at" ? moment(row.updated_at).format("ddd, DD MMM YYYY, h:mm A") :
                                                                                                                        col.id === "endDate" ? moment(row.endDate).format("ddd, DD MMM YYYY, h:mm:ss a") :
                                                                                                                            col.id === "amount" ? <span style={{ color: row.reportType !== "exceptionalOrder" ? 'inherit' : 'red' }}>{parseInt(row.amount)}</span> :
                                                                                                                                col.id === "unit_price" ? <span style={{ color: row.reportType !== "exceptionalOrder" ? 'inherit' : 'red' }}>{("$" + row.unit_price)}</span> :
                                                                                                                                    col.id === "total_price" ? <span style={{ color: row.reportType !== "exceptionalOrder" ? 'inherit' : 'red' }}>{("$" + row.total_price)}</span> :
                                                                                                                                        col.id === "finalPriceWithVAT" ? "$" + parseFloat(row.finalPriceWithVAT).toFixed(2) :
                                                                                                                                            col.id === "price" ? ("$" + parseFloat(row.price).toFixed(2)) :
                                                                                                                                                col.id === "price1" ? ("$" + parseFloat(row.price).toFixed(2)) :
                                                                                                                                                    col.id === "latestCost" ? ("$" + (row.latestCost.toFixed(2))) :
                                                                                                                                                        col.id === "averageCost" ? ("$" + (row.averageCost.toFixed(2))) :
                                                                                                                                                            col.id === "supplierCost" ? ("$" + (row.supplierCost.toFixed(2))) :
                                                                                                                                                                col.id === "active" ? (row.stock.Active === "Y" ? <Check fontSize='small' color="primary" /> : <Clear fontSize='small' color='error' />) :
                                                                                                                                                                    col.id === "viewSoh" ? <Button variant="outlined" onClick={() => props.handleView(row)}> View SOH </Button> :
                                                                                                                                                                        row[col.id]
                                                        }
                                                    </TableCell>

                                    ))}
                                </TableRow>
                            )
                        })}
                    </TableBody>
                    :
                    <TableBody>
                        <TableRow style={{ position: "relative", width: "100%" }}>
                            <Box textAlign="center" style={{ position: "absolute", right: "50%" }} mt={3}>
                                No Data Found
                            </Box>
                        </TableRow>
                    </TableBody>
                }
            </Table>
            <Snackbar
                autoHideDuration={3000}
                anchorOrigin={{ vertical: "top", horizontal: "right" }}
                open={open || tokenOpen}
                onClose={handleCloseSnack}
            >
                <Alert
                    onClose={handleCloseSnack}
                    severity={messageState || tokenMessageState}
                    sx={{ width: "100%" }}
                >
                    {message || tokenMessage}
                </Alert>
            </Snackbar>
        </TableContainer>
    );
}
