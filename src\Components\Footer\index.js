import React from 'react';
import { Box, Typography, Backdrop, <PERSON><PERSON>, Card, CardContent } from '@mui/material';
import { useNavigate } from 'react-router-dom';

const Footer = (props) => {
  const navigate = useNavigate();
  const handleLogout = () => {
    localStorage.clear();
    navigate("/login");
  };

  return (
    <>
      <Box
        component="footer"
        sx={{
          py: 0.5,
          px: 0.5,
          mt: 5,
          position: 'fixed',
          bottom: 0,
          width: '100%',
          backgroundColor: (theme) =>
            theme.palette.mode === 'light' ? theme.palette.grey[200] : theme.palette.grey[800],
          textAlign: 'center',
        }}
      >
        <Typography variant="body1" gutterBottom>
          © {new Date().getFullYear()} SyncCare
        </Typography>
      </Box>

      {props.overlay && (
        <Backdrop
          sx={{
            color: '#fff', 
            zIndex: (theme) => theme.zIndex.drawer + 1,
            backgroundColor: 'rgba(0, 0, 0, 0.5)',
            backdropFilter: 'blur(10px)'
          }}
          open={true}

        >
          <Card sx={{ minWidth: 350, p: 2 }}>
            <CardContent sx={{ textAlign: 'center' }}>
              <Typography variant="h6" component="div" gutterBottom>
                {"Session Expired!"}
              </Typography>
              <Button
                variant="contained"
                color="primary"
                sx={{ mt: 4 }}
                onClick={() => {
                  handleLogout();
                }}
              >
                Log In
              </Button>
            </CardContent>
          </Card>
        </Backdrop>
      )}
    </>
  );
};

export default Footer;
