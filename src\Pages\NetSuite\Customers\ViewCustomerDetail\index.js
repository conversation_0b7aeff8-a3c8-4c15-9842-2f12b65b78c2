import { Check, Clear, Close, Download, Sync, Visibility } from "@mui/icons-material";
import {
    AppBar,
    Box,
    Button,
    CircularProgress,
    Dialog,
    DialogActions,
    DialogContent,
    DialogTitle,
    Grid,
    IconButton,
    Skeleton,
    styled,
    useTheme,
    Snackbar
} from "@mui/material";
import moment from "moment";
import React, { useEffect, useState } from "react";
import Tabs from "@mui/material/Tabs";
import Tab from "@mui/material/Tab";
import Typography from "@mui/material/Typography";
import PropTypes from "prop-types";
//import BasicTable from "../../../../Components/BasicTable";
import FsLightbox from "fslightbox-react";

import MuiAlert from "@mui/material/Alert";
//import ViewPolicyDialog from "../../price_policy/ViewPolicyDialog";
import parse from "html-react-parser";
import BasicTable from "../../../../Components/BasicTable";
//import BasicTable from "../../../Components/BasicTable";


const Alert = React.forwardRef(function Alert(props, ref) {
    return <MuiAlert elevation={6} ref={ref} variant="filled" {...props} />;
});

const StyledHeaderTitle = styled(DialogTitle)(({ theme }) => ({
    background: theme.palette.primary.main,
    color: "#fff",
    position: "relative",
    "& button": {
        position: "absolute",
        right: "15px",
        top: "15px",
        color: "#fff",
    },
}));

const FlexContent = styled("div")(({ theme }) => ({
    display: "flex",
    flexDirection: "row",
    fontSize: "17px",
    marginBottom: "10px",
    alignItems: "flex-start",
}));

const FlexInnerTitle = styled("div")(({ theme }) => ({
    display: "flex",
    alignItems: "center",
    justifyContent: "space-between",
    minWidth: "249px",
    maxWidth: "250px",
    fontWeight: "600",
}));

const FlexContent2 = styled("div")(({ theme }) => ({
    display: "flex",
    flexDirection: "row",
    fontSize: "17px",
}));

const FlexInnerTitle2 = styled("div")(({ theme }) => ({
    display: "flex",
    fontWeight: "600",
    gap: "5px",
    marginRight: "5px",
}));

const BoxDiv = styled("div")(({ theme }) => ({
    textAlign: "center",
}));

const Values = styled("div")(({ theme }) => ({
    marginLeft: "10px",
    fontWeight: "500",
    color: theme.palette.primary.dark,
}));

const ImageDiv = styled("div")(({ theme }) => ({
    display: "flex",
    alignItems: "center",
    width: "100%",
    flexWrap: "wrap",
    marginBottom: "10px",
}));

const ImageCell = styled("div")(({ theme }) => ({
    margin: "10px",
    width: "280px",
    borderRadius: "5px",
    overflow: "hidden",
    "& img": {
        width: "250px",
        height: "250px",
        objectFit: "cover",
        transition: "0.5s",
        boxShadow: theme.palette.primary.shadow,
        marginBottom: "10px",
        overflow: "hidden",
    },
    "& img:hover": {
        transform: "scale(1.1)",
    },
}));

const price_policyBox = styled(Box)(({ theme }) => ({
    display: "flex",
    marginBottom: "15px",
    "& h5": {
        margin: "5px",
    },
}));

const AppBarTabs = styled(AppBar)(({ theme }) => ({
    background: "#fff",
    color: theme.palette.primary.dark,
}));

function TabPanel(props) {
    const { children, value, index, ...other } = props;

    return (
        <div
            role="tabpanel"
            hidden={value !== index}
            id={`full-width-tabpanel-${index}`}
            aria-labelledby={`full-width-tab-${index}`}
            {...other}
        >
            {value === index && (
                <Box sx={{ p: 3 }}>
                    <Typography>{children}</Typography>
                </Box>
            )}
        </div>
    );
}

TabPanel.propTypes = {
    children: PropTypes.node,
    index: PropTypes.number.isRequired,
    value: PropTypes.number.isRequired,
};

function a11yProps(index) {
    return {
        id: `full-width-tab-${index}`,
        "aria-controls": `full-width-tabpanel-${index}`,
    };
}

const orderColumns = [
    { id: "order_number", name: "Order Number" },
    { id: "total_items", name: "Total Items" },
    { id: "total_price", name: "Total Price" },
    { id: "order_created_at", name: "Order Created Date" },

];

const ViewCustomerDetail = (props) => {
    const theme = useTheme();
    const [value, setValue] = useState(0);
    const [open, setOpen] = useState(false);
    const [message, setMessage] = useState("");
    const [messageState, setMessageState] = useState("");
    const [togglerLanding, setTogglerLanding] = useState(false);
    const [imgIndex1, setImgIndex1] = useState(0);

    const handleChange = (event, newValue) => {
        setValue(newValue);
    };


    const [dialogDetails, setDialogDetails] = useState({
        open: true,
    });

    useEffect(() => {
        props.sendDetails(dialogDetails);
    }, [props, dialogDetails]);

    const handleImageTogglerLanding = (index) => {
        setImgIndex1(index);
        setTogglerLanding((prev) => !prev);
    };

    const handleClose = () => {
        setDialogDetails({
            ...dialogDetails,
            open: false,
        });
    };

    const displayText = (descriptionTexts) => {
        const textIsEmpty = descriptionTexts === null || descriptionTexts === "";
        return !textIsEmpty ? (
            parse(descriptionTexts)
        ) : (
            "-"
        );
    }


    const handleCloseSnack = (event, reason) => {
        if (reason === "clickaway") {
            return;
        }
        setOpen(false);
    };

    return (
        <div>
            <Dialog
                open={dialogDetails.open}
                onClose={handleClose}
                maxWidth="xl"
                fullWidth
                aria-labelledby="alert-dialog-title"
                aria-describedby="alert-dialog-description"
            >
                <StyledHeaderTitle id="alert-dialog-title">
                    <div>
                        View Customer Details{" "}
                        {"(" +
                            //   (props.viewDetails.handle || "-") +
                            //   "/" +
                            (props.viewDetails.fullname || "-") +
                            ")"}
                    </div>
                    <IconButton onClick={handleClose}>
                        <Close />
                    </IconButton>
                </StyledHeaderTitle>
                {props.singleLoading ? (
                    <DialogContent>
                        <Grid container spacing={2}>
                            <Grid item xs={12} md={6}>
                                <Box p={3} sx={{ width: "100%" }}>
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                </Box>
                            </Grid>
                            <Grid item xs={12} md={6}>
                                <Box p={3} sx={{ width: "100%" }}>
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                </Box>
                            </Grid>
                        </Grid>
                    </DialogContent>
                ) : (
                    <DialogContent sx={{ padding: "0" }}>
                        <AppBarTabs position="static">
                            <Tabs
                                value={value}
                                onChange={handleChange}
                                indicatorColor="secondary"
                                textColor="inherit"
                                variant="fullWidth"
                                aria-label="full width tabs example"
                            >

                                <Tab label="Customer Details" {...a11yProps(0)} />

                            </Tabs>
                        </AppBarTabs>


                        <TabPanel value={value} index={0} dir={theme.direction}>
                            <Box>
                                <Grid container>
                                    <Grid item xs={12} md={6}>
                                        <FlexContent>
                                            <FlexInnerTitle>
                                                <span>Internal ID</span> <span> : </span>
                                            </FlexInnerTitle>
                                            <Values>{props.viewDetails.internalID || "-"}</Values>
                                        </FlexContent>
                                        <FlexContent>
                                            <FlexInnerTitle>
                                                <span>Full Name</span> <span> : </span>
                                            </FlexInnerTitle>
                                            <Values>{props.viewDetails.fullname || "-"}</Values>
                                        </FlexContent>

                                        <FlexContent>
                                            <FlexInnerTitle>
                                                <span>Phone Number</span> <span> : </span>
                                            </FlexInnerTitle>
                                            <Values>{props.viewDetails.phone || "-"}</Values>
                                        </FlexContent>

                                        <FlexContent>
                                            <FlexInnerTitle>
                                                <span>Email</span> <span> : </span>
                                            </FlexInnerTitle>
                                            <Values>{props.viewDetails.email || "-"}</Values>
                                        </FlexContent>

                                        <FlexContent>
                                            <FlexInnerTitle>
                                                <span>Person/Company</span> <span> : </span>
                                            </FlexInnerTitle>
                                            <Values>{props.viewDetails.isperson === "F" ? "Company" : "Person"}</Values>
                                        </FlexContent>

                                        <FlexContent>
                                            <FlexInnerTitle>
                                                <span>Created Date</span> <span> : </span>
                                            </FlexInnerTitle>
                                            <Values>{moment(props.viewDetails.created_at).format("ddd, DD MMM YYYY, h:mm A")}</Values>
                                        </FlexContent>

                                        <FlexContent>
                                            <FlexInnerTitle>
                                                <span>Last Modified Date & Time</span> <span> : </span>
                                            </FlexInnerTitle>
                                            <Values>{moment(props.viewDetails.lastmodifieddate).format("ddd, DD MMM YYYY, h:mm A")}</Values>
                                        </FlexContent>
                                    </Grid>
                                    <Grid item xs={12} md={6}>
                                        <FlexContent>
                                            <FlexInnerTitle>
                                                <span>Address Internal ID</span> <span> : </span>
                                            </FlexInnerTitle>
                                            <Values>{props.viewDetails.netsuite_customer_address[0]?.internalID || "-"}</Values>
                                        </FlexContent>
                                        <FlexContent>
                                            <FlexInnerTitle>
                                                <span>Address</span> <span> : </span>
                                            </FlexInnerTitle>
                                            <Values>{props.viewDetails.netsuite_customer_address[0]?.addressee || "-"}</Values>
                                        </FlexContent>
                                        <FlexContent>
                                            <FlexInnerTitle>
                                                <span>City</span> <span> : </span>
                                            </FlexInnerTitle>
                                            <Values>{props.viewDetails.netsuite_customer_address[0]?.city || "-"}</Values>
                                        </FlexContent>
                                        <FlexContent>
                                            <FlexInnerTitle>
                                                <span>State</span> <span> : </span>
                                            </FlexInnerTitle>
                                            <Values>{props.viewDetails.netsuite_customer_address[0]?.state || "-"}</Values>
                                        </FlexContent>
                                        <FlexContent>
                                            <FlexInnerTitle>
                                                <span>Country</span> <span> : </span>
                                            </FlexInnerTitle>
                                            <Values>{props.viewDetails.netsuite_customer_address[0]?.country || "-"}</Values>
                                        </FlexContent>

                                        <FlexContent>
                                            <FlexInnerTitle>
                                                <span>Last Modified Date & Time</span> <span> : </span>
                                            </FlexInnerTitle>
                                            <Values>{moment(props.viewDetails.netsuite_customer_address[0]?.lastmodifieddate).format("ddd, DD MMM YYYY, h:mm A")}</Values>
                                        </FlexContent>
                                    </Grid>
                                </Grid>
                            </Box>
                        </TabPanel>
                    </DialogContent>
                )}
                <DialogActions>
                    <Button onClick={handleClose} variant="outlined" color="primary">
                        Close
                    </Button>
                </DialogActions>
            </Dialog>



            <Snackbar
                autoHideDuration={3000}
                anchorOrigin={{ vertical: "top", horizontal: "right" }}
                open={open}
                onClose={handleCloseSnack}
            >
                <Alert
                    onClose={handleCloseSnack}
                    severity={messageState}
                    sx={{ width: "100%" }}
                >
                    {message}
                </Alert>
            </Snackbar>
        </div>
    );
};

export default ViewCustomerDetail;
