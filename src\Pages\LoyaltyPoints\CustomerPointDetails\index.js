import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, KeyboardArrowUp, KeyboardArrowDown, UnfoldMore } from '@mui/icons-material';
import {
  Box,
  Button,
  Card,
  CardContent,
  Chip,
  Collapse,
  Divider,
  Grid,
  InputLabel,
  Skeleton,
  Snackbar,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TablePagination,
  TableRow,
  TextField,
  Typography,
} from '@mui/material';
import React, { useEffect, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import moment from 'moment';
import httpclient from '../../../Utils';
import MuiAlert from "@mui/material/Alert";

const Alert = React.forwardRef(function Alert(props, ref) {
  return <MuiAlert elevation={6} ref={ref} variant="filled" {...props} />;
});

const CustomerPointDetails = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [filterOpen, setFilterOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [rows, setRows] = useState([]);
  const [customer, setCustomer] = useState({});
  const [open, setOpen] = useState(false);
  const [message, setMessage] = useState('');
  const [messageState, setMessageState] = useState('success');
  const [pagination, setPagination] = useState({
    page: 0,
    rowsPerPage: 20,
    total: 0,
  });
  const [sortConfig, setSortConfig] = useState([]);
  const [isCardOpen, setIsCardOpen] = useState(false); // State for collapsible card
  const [filters, setFilters] = useState(() => {
    const savedFilters = localStorage.getItem('customer_point_details_filters');
    return savedFilters
      ? JSON.parse(savedFilters)
      : {
        startDate: '',
        endDate: '',
        order_no: '',
        receipt_no: '',
      };
  });

  const columns = [
    { id: 'created_at', name: 'Applied Date', sortable: false, field: 'created_at' },
    { id: 'point', name: 'Applied Point', sortable: false, field: 'point' },
    { id: 'reference_id', name: 'Reference ID', sortable: false, field: 'reference_id' },
    { id: 'orderReceiptNo', name: 'Order Receipt No', sortable: false, field: 'orderReceiptNo' },
    { id: 'orderNo', name: 'Associate Order No', sortable: false, field: 'orderNo' },
    { id: 'transaction', name: 'Transaction Type', sortable: false, field: 'transaction' },
  ];

  useEffect(() => {
    fetchData(0, pagination.rowsPerPage);
  }, [pagination.rowsPerPage]);

  useEffect(() => {
    if (sortConfig.length > 0) {
      setPagination(prev => ({ ...prev, page: 0 }));
      fetchData(0, pagination.rowsPerPage);
    }
  }, [sortConfig]);

  const fetchData = async (page = 0, rowsPerPage = 20, customSortConfig = null) => {
    setLoading(true);
    try {
      let url = `request-response?requestName=lightspeed/customer-point-details/${id}&page=${page + 1}&per_page=${rowsPerPage}`;
      const params = [];
      if (filters.startDate) {
        params.push(`start_date=${filters.startDate}`);
      }
      if (filters.endDate) {
        params.push(`end_date=${filters.endDate}`);
      }
      if (filters.order_no) {
        params.push(`order_no=${filters.order_no}`);
      }
      if (filters.receipt_no) {
        params.push(`receipt_no=${filters.receipt_no}`);
      }

      const currentSortConfig = customSortConfig || sortConfig;
      if (currentSortConfig.length > 0) {
        currentSortConfig.forEach((sort, index) => {
          params.push(`sort[${index}]=${sort.field}:${sort.direction}`);
        });
      }

      if (params.length) {
        url += `&${params.join('&')}`;
      }

      const response = await httpclient.get(url);

      if (response.status === 200) {
        const formattedData = response.data.data.results?.data.map((item) => ({
          created_at: item.created_at === '' ? item.created_at : moment(item.created_at).format("YYYY-MM-DD hh:mm A"),
          point: parseFloat(item.point).toFixed(0),
          reference_id: item.reference_id,
          orderReceiptNo: item.orderReceiptNo,
          orderNo: item.orderNo,
          transaction: item.transaction_type,
        }));
        const formattedCustomer = {
          full_name: response.data.data?.customer?.full_name,
          email: response.data.data?.customer?.email,
          litecard_member_id: response.data.data?.lightSpeedInfo?.external_id,
          current_point: parseFloat(response.data.data?.customer?.current_points).toFixed(0),
          topup_point: response.data.data?.manual_additon,
          deducted_topup_point: response.data.data?.manual_deduction
          ,
          point_reedemed: response.data.data?.customer?.redeemed_points || 0,
        };
        setCustomer(formattedCustomer);
        setRows(formattedData);
        setPagination((prev) => ({
          ...prev,
          page,
          total: response.data.data.results.total,
        }));
      } else {
        setMessage(response.data.message || 'Error fetching Customer Data');
        setMessageState('error');
        setOpen(true);
      }
    } catch (error) {
      console.error('Error fetching data:', error);
      setMessage(error?.response?.data?.message || 'Error fetching Customer Data');
      setMessageState('error');
      setOpen(true);
      setLoading(false);
    } finally {
      setLoading(false);
    }
  };

  const handleFilter = () => {
    localStorage.setItem('customer_point_details_filters', JSON.stringify(filters));
    setPagination(prev => ({ ...prev, page: 0 }));
    fetchData(0, pagination.rowsPerPage);
  };

  const handleRemoveFilter = (filterKey) => {
    const newFilters = { ...filters, [filterKey]: '' };
    setFilters(newFilters);
    localStorage.setItem('customer_point_details_filters', JSON.stringify(newFilters));
    setPagination(prev => ({ ...prev, page: 0 }));
    fetchData(0, pagination.rowsPerPage);
  };

  const handleClearAll = () => {
    const newFilters = {
      startDate: '',
      endDate: '',
      order_no: '',
      receipt_no: '',
      receipt_value: '',
    };
    setFilters(newFilters);
    localStorage.setItem('customer_point_details_filters', JSON.stringify(newFilters));
    setPagination(prev => ({ ...prev, page: 0 }));
    fetchData(0, pagination.rowsPerPage);
  };

  const handleChangePage = (event, newPage) => {
    setPagination((prev) => ({ ...prev, page: newPage }));
    fetchData(newPage, pagination.rowsPerPage);
  };

  const handleChangeRowsPerPage = (event) => {
    const newRowsPerPage = parseInt(event.target.value, 10);
    setPagination((prev) => ({
      ...prev,
      rowsPerPage: newRowsPerPage,
      page: 0,
    }));
    fetchData(0, newRowsPerPage);
  };

  const handleSort = (column) => {
    setSortConfig(prevConfig => {
      const existingIndex = prevConfig.findIndex(sort => sort.field === column);
      if (existingIndex >= 0) {
        const existingSort = prevConfig[existingIndex];
        if (existingSort.direction === 'asc') {
          const newConfig = [...prevConfig];
          newConfig[existingIndex] = { field: column, direction: 'desc' };
          return newConfig;
        } else {
          return prevConfig.filter((_, index) => index !== existingIndex);
        }
      } else {
        return [...prevConfig, { field: column, direction: 'asc' }];
      }
    });
  };

  const savedFilters = JSON.parse(localStorage.getItem('customer_point_details_filters') || '{}');

  return (
    <Box sx={{ p: 3 }}>
      <Grid container alignItems="center" justifyContent="space-between" mb={3}>
        <Grid item>
          <Typography variant="h4" fontWeight="bold" color="primary" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            Customer Point Details
            {customer?.full_name && (
              <>
                <Typography variant="h6" fontWeight="light">
                  - {`${customer.full_name} (${customer.litecard_member_id})`}
                </Typography>
                <Button
                  variant="text"
                  onClick={() => setIsCardOpen(!isCardOpen)}
                  sx={{ minWidth: 'auto', p: 0 }}
                >
                  {isCardOpen ? <KeyboardArrowUp /> : <KeyboardArrowDown />}
                </Button>
              </>
            )}
          </Typography>
        </Grid>
        <Grid item>
          <Box display="flex" gap={2}>
            <Button
              variant="outlined"
              startIcon={<ArrowBack />}
              onClick={() => navigate(-1)}
              sx={{ borderRadius: "8px", textTransform: "none" }}
            >
              Return Back
            </Button>
            <Button
              variant="contained"
              color="primary"
              startIcon={<FilterList />}
              onClick={() => setFilterOpen(!filterOpen)}
              sx={{ borderRadius: "8px", textTransform: "none" }}
            >
              Filters
            </Button>
          </Box>
        </Grid>
      </Grid>

      <Collapse in={isCardOpen}>
        <Card sx={{ p: 1, mb: 3 }}>
          <CardContent>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6}>
                <Typography fontWeight="bold" mb={0.5}>
                  Full Name
                </Typography>
                <Typography fontWeight="medium">
                  {customer?.full_name || "-"}
                </Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography fontWeight="bold" mb={0.5}>
                  Email
                </Typography>
                <Typography fontWeight="medium">
                  {customer?.email || "-"}
                </Typography>
              </Grid>
              <Grid item xs={12}>
                <Divider />
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography fontWeight="bold" mb={0.5}>
                  LiteCard Member ID
                </Typography>
                <Typography fontWeight="medium">
                  {customer?.litecard_member_id || "-"}
                </Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography fontWeight="bold" mb={0.5}>
                  Current Points
                </Typography>
                <Typography fontWeight="medium">
                  {customer?.current_point || 0}
                </Typography>
              </Grid>
              <Grid item xs={12}>
                <Divider />
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography fontWeight="bold" mb={0.5}>
                  Manual TopUp Points
                </Typography>
                <Typography fontWeight="medium">
                  {customer?.topup_point || 0}
                </Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography fontWeight="bold" mb={0.5}>
                  Manual Deducted TopUp Points
                </Typography>
                <Typography fontWeight="medium">
                  {customer?.deducted_topup_point || 0}
                </Typography>
              </Grid>
              <Grid item xs={12}>
                <Divider />
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography fontWeight="bold" mb={0.5}>
                  Points Redeemed
                </Typography>
                <Typography fontWeight="medium">
                  {customer?.point_reedemed || 0}
                </Typography>
              </Grid>
            </Grid>
          </CardContent>
        </Card>
      </Collapse>

      <Collapse in={filterOpen}>
        <Card sx={{ mb: 3 }}>
          <Box sx={{ p: 3 }}>
            <Grid container spacing={2}>
              <Grid item xs={12} md={4}>
                <InputLabel>Start Date</InputLabel>
                <TextField
                  type="date"
                  fullWidth
                  size="small"
                  value={filters.startDate}
                  onChange={(e) => setFilters({ ...filters, startDate: e.target.value })}
                  InputLabelProps={{ shrink: true }}
                />
              </Grid>
              <Grid item xs={12} md={4}>
                <InputLabel>End Date</InputLabel>
                <TextField
                  type="date"
                  fullWidth
                  size="small"
                  value={filters.endDate}
                  onChange={(e) => setFilters({ ...filters, endDate: e.target.value })}
                  InputLabelProps={{ shrink: true }}
                />
              </Grid>
              <Grid item xs={12} md={4}>
                <InputLabel>Order No</InputLabel>
                <TextField
                  fullWidth
                  size="small"
                  value={filters.order_no}
                  onChange={(e) => setFilters({ ...filters, order_no: e.target.value })}
                />
              </Grid>
              <Grid item xs={12} md={4}>
                <InputLabel>Receipt No</InputLabel>
                <TextField
                  fullWidth
                  size="small"
                  value={filters.receipt_no}
                  onChange={(e) => setFilters({ ...filters, receipt_no: e.target.value })}
                />
              </Grid>
              <Grid item xs={12}>
                <Box display="flex" justifyContent="flex-end" gap={2}>
                  <Button
                    variant="contained"
                    endIcon={<ArrowForward />}
                    onClick={handleFilter}
                  >
                    Apply Filters
                  </Button>
                  <Button variant="outlined" onClick={handleClearAll}>
                    Clear All
                  </Button>
                </Box>
              </Grid>
            </Grid>
          </Box>
        </Card>
      </Collapse>

      {Object.values(savedFilters).some(Boolean) && (
        <Box sx={{ mb: 2, display: 'flex', flexWrap: 'wrap', gap: 1 }}>
          {savedFilters.startDate && (
            <Chip
              label={`Start Date: ${moment(savedFilters.startDate).format('DD MMM YYYY')}`}
              onDelete={() => handleRemoveFilter('startDate')}
            />
          )}
          {savedFilters.endDate && (
            <Chip
              label={`End Date: ${moment(savedFilters.endDate).format('DD MMM YYYY')}`}
              onDelete={() => handleRemoveFilter('endDate')}
            />
          )}
          {savedFilters.order_no && (
            <Chip
              label={`Order No: ${savedFilters.order_no}`}
              onDelete={() => handleRemoveFilter('order_no')}
            />
          )}
          {savedFilters.receipt_no && (
            <Chip
              label={`Receipt No: ${savedFilters.receipt_no}`}
              onDelete={() => handleRemoveFilter('receipt_no')}
            />
          )}
        </Box>
      )}

      <Card>
        <TableContainer>
          <Table>
            <TableHead sx={{ backgroundColor: 'primary.main' }}>
              <TableRow>
                {columns.map((column) => {
                  if (column.sortable) {
                    const sortInfo = sortConfig.find(sort => sort.field === column.field);
                    const isActive = !!sortInfo;
                    const direction = sortInfo?.direction || 'asc';
                    return (
                      <TableCell
                        key={column.id}
                        sx={{
                          color: 'white',
                          cursor: 'pointer',
                          userSelect: 'none'
                        }}
                        onClick={() => handleSort(column.field)}
                      >
                        <span style={{
                          fontWeight: isActive ? "700" : "bold",
                          display: 'flex',
                          alignItems: 'center',
                          gap: '4px'
                        }}>
                          {column.name}
                          {isActive ? (
                            direction === 'asc' ?
                              <KeyboardArrowUp fontSize="small" /> :
                              <KeyboardArrowDown fontSize="small" />
                          ) : (
                            <UnfoldMore fontSize="small" />
                          )}
                        </span>
                      </TableCell>
                    );
                  } else {
                    return (
                      <TableCell key={column.id} sx={{ color: 'white', fontWeight: 'bold' }}>
                        {column.name}
                      </TableCell>
                    );
                  }
                })}
              </TableRow>
            </TableHead>
            <TableBody>
              {loading ? (
                [...Array(5)].map((_, rowIndex) => (
                  <TableRow key={rowIndex}>
                    {columns.map((column) => (
                      <TableCell key={`${rowIndex}-${column.id}`}>
                        <Skeleton variant="text" />
                      </TableCell>
                    ))}
                  </TableRow>
                ))
              ) : rows.length > 0 ? (
                <>
                  {rows.map((row, rowIndex) => (
                    <TableRow hover key={rowIndex}>
                      {columns.map((column) => (
                        <TableCell key={`${rowIndex}-${column.id}`}>
                          {row[column.id]}
                        </TableCell>
                      ))}
                    </TableRow>
                  ))}
                  <TableRow sx={{ backgroundColor: '#f3f4f6' }}>
                    {columns.map((column, index) => {
                      if (index === 0) {
                        return (
                          <TableCell key="total-label" sx={{ fontWeight: 'bold' }}>
                            Total Applied Point
                          </TableCell>
                        );
                      }
                      if (index === 1) {
                        const totalPoints = rows.reduce(
                          (sum, item) => sum + (parseFloat(item.point) || 0),
                          0
                        );
                        return (
                          <TableCell key="total-point" sx={{ fontWeight: 'bold' }}>
                            {totalPoints}
                          </TableCell>
                        );
                      }
                      return <TableCell key={`empty-${column.id}`} />;
                    })}
                  </TableRow>
                </>
              ) : (
                <TableRow>
                  <TableCell colSpan={columns.length} align="center">
                    No records found
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </TableContainer>
        <TablePagination
          rowsPerPageOptions={[20, 50, 100]}
          component="div"
          count={pagination.total}
          rowsPerPage={pagination.rowsPerPage}
          page={pagination.page}
          onPageChange={handleChangePage}
          onRowsPerPageChange={handleChangeRowsPerPage}
          sx={{ borderTop: '1px solid rgba(224, 224, 224, 1)' }}
        />
      </Card>
      <Snackbar autoHideDuration={3000} anchorOrigin={{ vertical: "top", horizontal: "right" }} open={open} onClose={() => setOpen(false)}>
        <Alert onClose={() => setOpen(false)} severity={messageState} sx={{ width: '100%' }}>{message}</Alert>
      </Snackbar>
    </Box>
  );
};

export default CustomerPointDetails;