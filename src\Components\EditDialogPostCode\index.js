import {
    Box,
    Button,
    Dialog,
    DialogActions,
    DialogContent,
    DialogTitle,
    FormControl,
    Grid,
    InputLabel,
    MenuItem,
    Select,
    styled,
    TextField,
} from "@mui/material";
import { useEffect, useState } from "react";
import * as React from 'react';


const StyledHeaderTitle = styled(DialogTitle)(({ theme }) => ({
    background: theme.palette.primary.main,
    color: "#fff",
    position: "relative",
    "& button": {
        position: "absolute",
        right: "15px",
        top: "15px",
        color: "#fff",
    },
}));

const EditDialogPostCode = (props) => {
    const [dialogDetails, setDialogDetails] = useState({
        open: true,
        success: false,
    });
    const [errors, setErrors] = useState({});
    const [formData, setFormData] = useState({
        postcode_name: '',
        postcode_id: '',
        country_id: '',
        type: "postcode"

    });

    useEffect(() => {
        if (props.viewDetails) {
            setFormData({
                postcode_name: props.viewDetails.name || '',
                postcode_id: props.viewDetails.id || '',
                country_id: props.viewDetails.countryID || '',
                type: "postcode"
            });
        }
    }, [props.viewDetails]);


    useEffect(() => {
        props.sendEdit(dialogDetails, formData);
    }, [dialogDetails]);


    const handleClose = () => {
        setDialogDetails({
            ...dialogDetails,
            open: false,
        });
    };

    const handleYes = () => {
        if (validate()) {
            setDialogDetails({
                ...dialogDetails,
                open: false,
                success: true,

            });
        }
    };

    const validate = () => {
        const newErrors = {};
        if (!formData.postcode_name) newErrors.postcode_name = 'PostCode Name is required';
        if (!formData.country_id) newErrors.postcode_name = 'Country ID is required';
        // if (!formData.postcode_id) newErrors.postcode_id = 'PostCode ID is required';

        setErrors(newErrors);
        return Object.keys(newErrors).length === 0;
    };


    const handleChange = (e) => {
        const { name, value } = e.target;
        setFormData((prevData) => ({
            ...prevData,
            [name]: value,
        }));
    };

    return (
        <div>
            <Dialog
                open={dialogDetails.open}
                onClose={handleClose}
                maxWidth="md"
                fullWidth
                aria-labelledby="alert-dialog-title"
                aria-describedby="alert-dialog-description"
            >
                <StyledHeaderTitle id="alert-dialog-title">
                    {props.viewDetails.id ? `Edit PostCode` : `Add PostCode`}
                </StyledHeaderTitle>
                <DialogContent>
                    <Box pt={3}>
                        {props.viewDetails.id &&
                            <TextField
                                required
                                disabled
                                label="PostCode ID"
                                name="postcode_id"
                                type="number"
                                value={formData.postcode_id}
                                onChange={handleChange}
                                error={!!errors.postcode_id}
                                helperText={errors.postcode_id}
                                fullWidth
                                margin="normal"
                            />
                        }
                        <FormControl required fullWidth margin="normal">
                            <InputLabel>Select Country</InputLabel>
                            <Select
                                label="Select Country"
                                name="country_id"
                                value={formData.country_id}
                                onChange={handleChange}
                                error={!!errors.country_id}
                                helperText={errors.country_id}
                            >
                                <MenuItem value="">Select Country</MenuItem>
                                {props.countries && props.countries.map((country) => (
                                    <MenuItem value={country.id}>{country.name}</MenuItem>
                                ))}
                            </Select>
                        </FormControl>

                        <TextField
                            required
                            label="PostCode Name"
                            name="postcode_name"
                            value={formData.postcode_name}
                            onChange={handleChange}
                            error={!!errors.postcode_name}
                            helperText={errors.postcode_name}
                            fullWidth
                            margin="normal"
                        />


                    </Box>
                </DialogContent>
                <DialogActions styled={{ margin: "5px 10px" }}>
                    <Button onClick={handleClose} color="error" variant="contained" autoFocus>
                        Cancel
                    </Button>
                    <Button
                        onClick={handleYes}
                        color="primary"
                        variant="contained"
                        autoFocus
                    >
                        {props.viewDetails.id ? `Edit` : `Save`}
                    </Button>
                </DialogActions>
            </Dialog>
        </div>
    );
};

export default EditDialogPostCode;
