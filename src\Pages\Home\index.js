import { Badge, Box, Card, Grid, IconButton, styled, Tooltip } from '@mui/material'
import React, { useCallback, useEffect, useRef, useState } from 'react'
import MuiAlert from "@mui/material/Alert";
import {
  Snackbar,
} from "@mui/material";
import Footer from '../../Components/Footer';
import DashboardGrid from './DashboardGrid';
import useTokenRefresh from '../../Hooks/useTokenRefresh';
import httpclient from '../../Utils';
import { Masonry } from '@mui/lab';
import DateRangePicker from 'rsuite/DateRangePicker';
import 'rsuite/DateRangePicker/styles/index.css';
import { useTimer } from '../../Components/Context/TimerContext';
import { Accordion, AccordionSummary, AccordionDetails, Typography } from "@mui/material";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import { useNavigate } from 'react-router-dom';


const Alert = React.forwardRef(function Alert(props, ref) {
  return <MuiAlert elevation={6} ref={ref} variant="filled" {...props} />;
});

const MyCard = styled(Card)(({ theme }) => ({
  padding: "45px",
  "& h3": {
    margin: "0"
  }
}))

const Logos = styled("div")({

  maxHeight: "60px",
  "& img": {
    width: "160px",
    objectFit: "cover",
    transition: "0.5s",
    marginBottom: "10px",
    overflow: "hidden",
  },

});
const LogosMid = styled("div")({
  marginTop: "10px",
  maxHeight: "60px",
  "& img": {
    width: "auto",
    objectFit: "cover",
    transition: "0.5s",
    overflow: "hidden",
  },

});
const LogosRight = styled("div")(({ theme }) => ({

  maxHeight: "60px",
  "& img": {
    width: "60px",
    objectFit: "cover",
    transition: "0.5s",
    marginBottom: "10px",
    position: "absolute",
    right: 35,
    overflow: "hidden",
  },
  "& span": {
    position: "absolute",
    marginTop: "20px",
    fontFamily: "Poppins",
    fontSize: "16px",
    fontWeight: "600",
    right: "10%",
  },
  "& span1": {
    position: "absolute",
    right: "2%",
    marginTop: "-30px",
    zIndex: 100,
    padding: "5px",
    border: "1px solid #fffff",
    borderRadius: "50%",
  },
}));


const Home = (props) => {
  const seconds = useTimer();
  const navigate = useNavigate();
  const { getTokenRefreshed: refresh, open: tokenOpen, overlay: overlay, setOpen: setTokenOpen, message: tokenMessage, messageState: tokenMessageState } = useTokenRefresh();
  const [loading, setLoading] = useState(false);
  const [erplyLoading, setErplyLoading] = useState(false);
  const [lightspeedLoading, setLightspeedLoading] = useState(false);
  const [klaviyoLoading, setKlaviyoLoading] = useState(false);
  const [dynamicLoading, setDynamicLoading] = useState(false);
  const [netsuiteLoading, setNetsuiteLoading] = useState(false);
  const [shopifyData, setShopifyData] = useState({});
  const [erplyData, setErplyData] = useState({});
  const [lightSpeedData, setLightSpeedData] = useState([]);
  const [klaviyoData, setKlaviyoData] = useState({});
  const [dynamicData, setDynamicData] = useState({});
  const [netsuiteData, setNetsuiteData] = useState({});
  const [open, setOpen] = useState(false);
  const [message, setMessage] = useState("");
  const [messageState, setMessageState] = useState("");
  const [flags, setFlags] = useState([]);
  const [shopifyHeaders, setShopifyHeaders] = useState({});
  const [erplyHeaders, setErplyHeaders] = useState({});
  const [lightspeedHeaders, setLightspeedHeaders] = useState({});
  const [dynamicHeaders, setDynamicHeaders] = useState({});
  const [netsuiteHeaders, setNetsuiteHeaders] = useState({});
  const [klaviyoHeaders, setKlaviyoHeaders] = useState({});
  const [customerByStore, setCustomerByStore] = useState([]);
  const [customerByStoreLoading, setCustomerByStoreLoading] = useState(false);
  const [customerByStoreHeaders, setCustomerByStoreHeaders] = useState({});


  const today = new Date();
  const yesterday = new Date();
  yesterday.setDate(today.getDate() - 1);
  const last7Days = new Date();
  last7Days.setDate(today.getDate() - 6);
  const last30Days = new Date();
  last30Days.setDate(today.getDate() - 29);
  const oneYearAgo = new Date();
  oneYearAgo.setFullYear(today.getFullYear() - 1);

  const [startDate, setStartDate] = useState(oneYearAgo.toISOString().split('T')[0]);
  const [endDate, setEndDate] = useState(today.toISOString().split('T')[0]);


  useEffect(() => {
    if (seconds === 0 && flags.length > 0) {
      if (flags.some((f) => f.component_name === "shopify_data_count" && f.status === 1)) {
        fetchShopifyData();
      }
      if (flags.some((f) => f.component_name === "erply_data_count" && f.status === 1)) {
        fetchErplyData();
      }
      if (flags.some((f) => f.component_name === "lightspeed_data_count" && f.status === 1)) {
        fetchLightspeedData();
      }
      if (flags.some((f) => f.component_name === "klaviyo_data_count" && f.status === 1)) {
        fetchKlaviyoData();
      }
      if (flags.some((f) => f.component_name === "dynamic_data_count" && f.status === 1)) {
        fetchDynamicData();
      }
      if (flags.some((f) => f.component_name === "netsuite_data_count" && f.status === 1)) {
        fetchNetsuiteData();
      }
      if (flags.some((f) => f.component_name === "lightspeed_litecard_members_stores" && f.status === 1)) {
        fetchCustomerByStore();
      }
    }
  }, [seconds, flags]);


  useEffect(() => {
    if (flags.some((f) => f.component_name === "shopify_data_count" && f.status === 1)) {
      fetchShopifyData();
    }
    if (flags.some((f) => f.component_name === "erply_data_count" && f.status === 1)) {
      fetchErplyData();
    }
    if (flags.some((f) => f.component_name === "klaviyo_data_count" && f.status === 1)) {
      fetchKlaviyoData();
    }
    if (flags.some((f) => f.component_name === "lightspeed_data_count" && f.status === 1)) {
      fetchLightspeedData();
    }
    if (flags.some((f) => f.component_name === "dynamic_data_count" && f.status === 1)) {
      fetchDynamicData();
    }
    if (flags.some((f) => f.component_name === "netsuite_data_count" && f.status === 1)) {
      fetchNetsuiteData();
    }
    if (flags.some((f) => f.component_name === "lightspeed_litecard_members_stores" && f.status === 1)) {
        fetchCustomerByStore();
      }
  }, [startDate, endDate]);

  useEffect(() => {
    fetchDataOnLoad()
    fetchHeaderData()
  }, [flags]);

  const fetchHeaderData = () => {
    if (flags.length > 0) {
      const shopifyFlag = flags.find(f => f.component_name === "shopify_data_count" && f.status === 1);
      setShopifyHeaders(shopifyFlag || {});
    }
    if (flags.length > 0) {
      const erplyFlag = flags.find(f => f.component_name === "erply_data_count" && f.status === 1);
      setErplyHeaders(erplyFlag || {});
    }
    if (flags.length > 0) {
      const klaviyoFlag = flags.find(f => f.component_name === "klaviyo_data_count" && f.status === 1);
      setKlaviyoHeaders(klaviyoFlag || {});
    }
    if (flags.length > 0) {
      const lightspeedFlag = flags.find(f => f.component_name === "lightspeed_data_count" && f.status === 1);
      console.log({'lightspeedFlag': lightspeedFlag})
      setLightspeedHeaders(lightspeedFlag || {});
    }
    if (flags.length > 0) {
      const dynamicFlag = flags.find(f => f.component_name === "dynamic_data_count" && f.status === 1);
      setDynamicHeaders(dynamicFlag || {});
    }
    if (flags.length > 0) {
      const netsuiteFlag = flags.find(f => f.component_name === "netsuite_data_count" && f.status === 1);
      setNetsuiteHeaders(netsuiteFlag || {});
    }
    if (flags.length > 0) {
      const customerFlag = flags.find(f => f.component_name === "lightspeed_litecard_members_stores" && f.status === 1);
      console.log({'customerFlag': customerFlag })
      setCustomerByStoreHeaders(customerFlag || {});
    }
    // if (flags.some((f) => f.component_name === "lightspeed_litecard_members_stores" && f.status === 1)) {
    //     setCustomerByStoreHeaders();
    //   }
  }


  const fetchDataOnLoad = () => {
    if (flags.length > 0) {
      if (flags.some((f) => f.component_name === "shopify_data_count" && f.status === 1)) {
        fetchShopifyData();
      }
      if (flags.some((f) => f.component_name === "erply_data_count" && f.status === 1)) {
        fetchErplyData();
      }
      if (flags.some((f) => f.component_name === "lightspeed_data_count" && f.status === 1)) {
        fetchLightspeedData();
      }
      if (flags.some((f) => f.component_name === "klaviyo_data_count" && f.status === 1)) {
        fetchKlaviyoData();
      }
      if (flags.some((f) => f.component_name === "dynamic_data_count" && f.status === 1)) {
        fetchDynamicData();
      }
      if (flags.some((f) => f.component_name === "netsuite_data_count" && f.status === 1)) {
        fetchNetsuiteData();
      }
      if (flags.some((f) => f.component_name === "lightspeed_litecard_members_stores" && f.status === 1)) {
        fetchCustomerByStore();
      }
    }
  };

  useEffect(() => {
    fetchComponentList();
  }, []);

  const fetchComponentList = () => {
    httpclient
      .get(`dashboard-component-list`)
      .then(({ data }) => {
        if (data.status === 200) {
          setFlags(data.data);
        } else {
          console.log("Error!");
        }
      }).catch((err) => {
        if (err.response.status === 401) {
          refresh();
          setOpen(tokenOpen);
          setMessage(tokenMessage);
          setMessageState("error");
        } else if (err.response.status === 422) {
          const errorMessages = Object.values(err.response.data.errors).flat();
          setOpen(true);
          setMessage(errorMessages);
          setMessageState("error");
        } else if (err.response.status === 400) {
          const errorMessages = Object.values(err.response.data.errors).flat();
          setOpen(true);
          setMessage(errorMessages);
          setMessageState("error");

        } else {
          setOpen(true);
          setMessage(err.response.data.message);
          setMessageState("error");
          setLoading(false);
        }
      })
  };

  const fetchShopifyData = () => {
    setLoading(true);
    httpclient
      .get(`request-response?requestName=shopify/data-count&start_date=${startDate}&end_date=${endDate}`)
      .then(({ data }) => {
        if (data.status === 200) {
          setShopifyData(data.data);
          setLoading(false);
        } else {
          console.log("Error!");
        }
      }).catch((err) => {
        if (err.response.status === 401) {
          refresh();
          setOpen(tokenOpen);
          setMessage(tokenMessage);
          setMessageState("error");
        } else if (err.response.status === 422) {
          const errorMessages = Object.values(err.response.data.errors).flat();
          setOpen(true);
          setMessage(errorMessages);
          setMessageState("error");
          setLoading(false);
        } else if (err.response.status === 400) {
          const errorMessages = Object.values(err.response.data.errors).flat();
          setOpen(true);
          setMessage(errorMessages);
          setMessageState("error");
          setLoading(false);

        } else {
          setOpen(true);
          setMessage(err.response.data.message);
          setMessageState("error");
          setLoading(false);
        }
      })
  };

  const fetchErplyData = () => {
    setErplyLoading(true);
    httpclient
      .get(`request-response?requestName=erply/data-count&start_date=${startDate}&end_date=${endDate}`)
      .then(({ data }) => {
        if (data.status === 200) {
          setErplyData(data.data);
          setErplyLoading(false);
        } else {
          console.log("Error!");
        }
      }).catch((err) => {
        if (err.response.status === 401) {
          refresh();
          setOpen(tokenOpen);
          setMessage(tokenMessage);
          setMessageState("error");
        } else if (err.response.status === 422) {
          const errorMessages = Object.values(err.response.data.errors).flat();
          setOpen(true);
          setMessage(errorMessages);
          setMessageState("error");
          setErplyLoading(false);
        } else if (err.response.status === 400) {
          const errorMessages = Object.values(err.response.data.errors).flat();
          setOpen(true);
          setMessage(errorMessages);
          setMessageState("error");
          setErplyLoading(false);

        } else {
          setOpen(true);
          setMessage(err.response.data.message);
          setMessageState("error");
          setErplyLoading(false);
        }
      })
  };

  const fetchLightspeedData = () => {
    setLightspeedLoading(true);
    httpclient
      .get(`request-response?requestName=lightspeed/data-count&start_date=${startDate}&end_date=${endDate}`)
      .then(({ data }) => {
        if (data.data) {
          setLightSpeedData(data.data);
          setLightspeedLoading(false);
        } else {
          console.log("Error!");
        }
      }).catch((err) => {
        if (err.response.status === 401) {
          refresh();
          setOpen(tokenOpen);
          setMessage(tokenMessage);
          setMessageState("error");
        } else if (err.response.status === 422) {
          const errorMessages = Object.values(err.response.data.errors).flat();
          setOpen(true);
          setMessage(errorMessages);
          setMessageState("error");
          setLightspeedLoading(false);
        } else if (err.response.status === 400) {
          const errorMessages = Object.values(err.response.data.errors).flat();
          setOpen(true);
          setMessage(errorMessages);
          setMessageState("error");
          setLightspeedLoading(false);

        } else {
          setOpen(true);
          setMessage(err.response.data.message);
          setMessageState("error");
          setLightspeedLoading(false);
        }
      })
  };

  const fetchKlaviyoData = () => {
    setKlaviyoLoading(true);
    httpclient
      .get(`request-response?requestName=klaviyo/data-count&start_date=${startDate}&end_date=${endDate}`)
      .then(({ data }) => {
        if (data.status === 200) {
          setKlaviyoData(data.data);
          setKlaviyoLoading(false);
        } else {
          console.log("Error!");
        }
      }).catch((err) => {
        if (err.response.status === 401) {
          refresh();
          setOpen(tokenOpen);
          setMessage(tokenMessage);
          setMessageState("error");
        } else if (err.response.status === 422) {
          const errorMessages = Object.values(err.response.data.errors).flat();
          setOpen(true);
          setMessage(errorMessages);
          setMessageState("error");
          setKlaviyoLoading(false);
        } else if (err.response.status === 400) {
          const errorMessages = Object.values(err.response.data.errors).flat();
          setOpen(true);
          setMessage(errorMessages);
          setMessageState("error");
          setKlaviyoLoading(false);

        } else {
          setOpen(true);
          setMessage(err.response.data.message);
          setMessageState("error");
          setKlaviyoLoading(false);
        }
      })
  };

  const fetchDynamicData = () => {
    setDynamicLoading(true);
    httpclient
      .get(`request-response?requestName=dynamic/data-count&start_date=${startDate}&end_date=${endDate}`)
      .then(({ data }) => {
        if (data.status === 200) {
          setDynamicData(data.data);
          setDynamicLoading(false);
        } else {
          console.log("Error!");
        }
      }).catch((err) => {
        if (err.response.status === 401) {
          refresh();
          setOpen(tokenOpen);
          setMessage(tokenMessage);
          setMessageState("error");
        } else if (err.response.status === 422) {
          const errorMessages = Object.values(err.response.data.errors).flat();
          setOpen(true);
          setMessage(errorMessages);
          setMessageState("error");
          setDynamicLoading(false);
        } else if (err.response.status === 400) {
          const errorMessages = Object.values(err.response.data.errors).flat();
          setOpen(true);
          setMessage(errorMessages);
          setMessageState("error");
          setDynamicLoading(false);

        } else {
          setOpen(true);
          setMessage(err.response.data.message);
          setMessageState("error");
          setDynamicLoading(false);
        }
      })
  };

  const fetchNetsuiteData = () => {
    setNetsuiteLoading(true);
    httpclient
      .get(`request-response?requestName=netsuite/data-count&start_date=${startDate}&end_date=${endDate}`)
      .then(({ data }) => {
        if (data.status === 200) {
          setNetsuiteData(data.data);
          setNetsuiteLoading(false);
        } else {
          console.log("Error!");
        }
      }).catch((err) => {
        if (err.response.status === 401) {
          refresh();
          setOpen(tokenOpen);
          setMessage(tokenMessage);
          setMessageState("error");
        } else if (err.response.status === 422) {
          const errorMessages = Object.values(err.response.data.errors).flat();
          setOpen(true);
          setMessage(errorMessages);
          setMessageState("error");
          setNetsuiteLoading(false);
        } else if (err.response.status === 400) {
          const errorMessages = Object.values(err.response.data.errors).flat();
          setOpen(true);
          setMessage(errorMessages);
          setMessageState("error");
          setNetsuiteLoading(false);

        } else {
          setOpen(true);
          setMessage(err.response.data.message);
          setMessageState("error");
          setNetsuiteLoading(false);
        }
      })
  };

  const fetchCustomerByStore=()=>{
    setCustomerByStoreLoading(true)
    httpclient
      .get(`request-response?requestName=lightspeed/chart/customers-by-store&start_date=${startDate}&end_date=${endDate}`)
      .then(({ data }) => {
        if (data.data) {
          setCustomerByStore(data.data);
          setCustomerByStoreLoading(false);
        } else {
          console.log("Error!");
        }
      }).catch((err) => {
        if (err.response.status === 401) {
          refresh();
          setOpen(tokenOpen);
          setMessage(tokenMessage);
        }
      else if (err.response.status === 422) {
          const errorMessages = Object.values(err.response.data.errors).flat();
          setOpen(true);
          setMessage(errorMessages);
          setMessageState("error");
          setCustomerByStoreLoading(false);
        } else if (err.response.status === 400) {
          const errorMessages = Object.values(err.response.data.errors).flat();
          setOpen(true);
          setMessage(errorMessages);
          setMessageState("error");
          setCustomerByStoreLoading(false);

        } else {
          setOpen(true);
          setMessage(err.response.data.message);
          setMessageState("error");
          setCustomerByStoreLoading(false);
        }
      })
  }



  const handleClose = (event, reason) => {
    if (reason === "clickaway") {
      return;
    }
    setOpen(false);
    props.setOpen(false);
    setTokenOpen(false);

  };


  const handleChange = (value) => {
    if (value) {
      setStartDate(value[0].toISOString().split('T')[0]); // Format and set start date
      setEndDate(value[1].toISOString().split('T')[0]);
    }
  };

  const moveToWorkflow = (moduleID, moduleName) => {
    const modulee = {
      moduleID: moduleID,
      moduleName: moduleName
    }
    navigate(`/workflow/list`, { state: modulee });
  };


  const sections = [
    { name: "Shopify", data: "shopify_data_count", headers: shopifyHeaders, dashboard: <DashboardGrid loading={loading} productStatus={shopifyData} startDate={startDate} endDate={endDate} /> },
    { name: "Erply", data: "erply_data_count", headers: erplyHeaders, dashboard: <DashboardGrid loading={erplyLoading} productStatus={erplyData} startDate={startDate} endDate={endDate} /> },
    { name: "Klaviyo", data: "klaviyo_data_count", headers: klaviyoHeaders, dashboard: <DashboardGrid loading={klaviyoLoading} productStatus={klaviyoData} startDate={startDate} endDate={endDate} /> },
    { name: "LightSpeed", data: "lightspeed_data_count", headers: lightspeedHeaders, dashboard: <DashboardGrid loading={lightspeedLoading} productStatus={lightSpeedData} startDate={startDate} endDate={endDate} /> },
    { name: "Dynamic 365", data: "dynamic_data_count", headers: dynamicHeaders, dashboard: <DashboardGrid loading={dynamicLoading} productStatus={dynamicData} startDate={startDate} endDate={endDate} /> },
    { name: "NetSuite", data: "netsuite_data_count", headers: netsuiteHeaders, dashboard: <DashboardGrid loading={netsuiteLoading} productStatus={netsuiteData} startDate={startDate} endDate={endDate} /> },
    { name: "Customer by Store", data: "lightspeed_litecard_members_stores", headers: customerByStoreHeaders, dashboard: <DashboardGrid loading={customerByStoreLoading} productStatus={customerByStore} startDate={startDate} endDate={endDate} /> }
  ];

  return (
    <div>
      <div>
        <Grid container spacing={2} sx={{ pb: 2 }}>
          <Grid item xs={12} sm={6} md={8}>
          </Grid>
          <Grid item xs={12} sm={6} md={1}>
            <strong>Filter Range :</strong>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>

            <DateRangePicker
              format="MMM dd, yyyy"
              character=" – "
              placement="bottomEnd"
              container={() => document.body}
              style={{ width: 360 }}
              defaultValue={[new Date(oneYearAgo), new Date(today)]}
              onChange={handleChange}
              ranges={[
                {
                  label: 'Today',
                  value: [today, today],
                },
                {
                  label: 'Yesterday',
                  value: [yesterday, yesterday],
                },
                {
                  label: 'Last 7 Days',
                  value: [last7Days, today],
                },
                {
                  label: 'Last 30 Days',
                  value: [last30Days, today],
                },
                {
                  label: 'Last 12 Months',
                  value: [oneYearAgo, today],
                },
              ]} />
          </Grid>
        </Grid>
        <Grid container spacing={3}>
          {sections.map((section, index) => {
            const hasChart = flags.some((f) => f.component_name === section.chart && f.status === 1);
            const hasData = flags.some((f) => f.component_name === section.data && f.status === 1);

            if (!hasChart && !hasData) return null;

            return (
              <Grid item xs={12} sm={12} md={12} key={index}>
                <Accordion defaultExpanded>
                  <AccordionSummary sx={{ background: "#281E50", color: "#fff" }} expandIcon={<ExpandMoreIcon sx={{ zIndex: "9999 !important", color: "#fff" }} />}>

                    <Logos>
                      <img src={section.headers?.module_logo} alt="Logo" />
                    </Logos>
                    <LogosMid>
                      <img src={section.headers?.sync_arrow} alt="Logo"/>
                    </LogosMid>
                    <Logos>
                      <img src={section.headers?.synccare_logo} alt="Logo"/>
                    </Logos>

                    <LogosRight onClick={() => moveToWorkflow(section.headers?.module_workflow_id, section.headers?.module_workflow_name)}>
                      <span>Last Updated: {section.headers?.module_last_updated} </span>
                      <img src={section.headers?.module_status_logo} alt="Logo" />
                      <span1>
                        {section.headers?.module_status !== "Complete" &&
                          <span>{section.headers?.module_status_data_count}</span>
                        }
                      </span1>
                    </LogosRight>
                  </AccordionSummary>

                  <AccordionDetails>
                    {/* {hasChart && section.graph} */}
                    {hasData && section.dashboard}
                  </AccordionDetails>
                </Accordion>
              </Grid>
            );
          })}
        </Grid>
        <Masonry
          columns={{ xs: 1, sm: 2, md: 3 }}
          spacing={3}
          sx={{ margin: "0", width: "auto" }}
        >
          {/* </Grid> */}
        </Masonry>
        <Footer overlay={props.overlayNew || props.overlay || overlay} />

      </div>

      <Snackbar
        open={props.open || open || tokenOpen}
        autoHideDuration={3000}
        onClose={props.handleClose || handleClose}
        anchorOrigin={{ vertical: "top", horizontal: "right" }}
      >
        <Alert
          onClose={props.handleClose || handleClose}
          severity={props.messageState || messageState || tokenMessageState}
          sx={{ width: "100%" }}
        >
          {props.message || message || tokenMessage}
        </Alert>
      </Snackbar>
    </div >
  );
};

export default Home;
