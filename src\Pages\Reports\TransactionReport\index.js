import React, { useEffect, useState, useRef } from "react";
import {
  <PERSON><PERSON><PERSON><PERSON>,
  Check,
  Clear,
  Close,
  Download,
  FilterList,
} from "@mui/icons-material";
import TableComponent from "../TableComponent";
import httpclient from "../../../Utils";
import {
  Box,
  Button,
  Card,
  Collapse,
  FormControl,
  Grid,
  InputLabel,
  MenuItem,
  Select,
  styled,
  TextField,
  Snackbar,
  Autocomplete,
  Checkbox,
} from "@mui/material";

import MuiAlert from "@mui/material/Alert";
// import StatusDialog from "../StatusDialog";
// import BackdropLoader from "../../../../Components/BackdropLoader";
import { useLocation, useNavigate } from "react-router";
import useTokenRefresh from "../../../Hooks/useTokenRefresh";
import Footer from "../../../Components/Footer";
import ViewReportDetail from "./ViewReportDetail";
import CheckBoxOutlineBlankIcon from '@mui/icons-material/CheckBoxOutlineBlank';
import CheckBoxIcon from '@mui/icons-material/CheckBox';
import moment from "moment/moment";

const icon = <CheckBoxOutlineBlankIcon fontSize="small" />;
const checkedIcon = <CheckBoxIcon fontSize="small" />;

//import { useLocation } from "react-router-dom";

const Alert = React.forwardRef(function Alert(props, ref) {
  return <MuiAlert elevation={6} ref={ref} variant="filled" {...props} />;
});



const columns = [
  //{ id: "checkColumn", name: " " },
  { id: "storeId", name: "Store ID" },
  { id: "storeName", name: "Store Name" },
  { id: "totalTransactionOrderCount", name: "Total Transaction Order Count" },
  { id: "totalTransactionAmount", name: "Total $ Amount" },
  { id: "totalTransactionPoint", name: "Total Transaction Points" },
  { id: "totalKlaviyoSignupPoint", name: "Total Klaviyo Signup Points" },
  { id: "totalAppliedPoint", name: "Total Applied Points" },

];

const FilteredBox = styled(Box)(({ theme }) => ({
  background: "#f9f9f9",
  padding: "5px 10px",
  borderRadius: "5px",
  "& p": {
    margin: "3px 0",
    marginRight: "10px",
    display: "inline-block",
    background: "#dedede",
    borderRadius: "10px",
    padding: "2px 5px",
  },
  "& svg": {
    fontSize: "15px",
    cursor: "pointer",
    position: "relative",
    top: "3px",
    background: theme.palette.primary.dark,
    color: "#fff",
    borderRadius: "50%",
    padding: "2px",
    marginLeft: "2px",
  },
}));

const Header = styled("div")(({ theme }) => ({
  "& h1": {
    color: theme.palette.primary.dark,
    margin: "0",
  },
}));

const configRowPerPage = JSON.parse(localStorage.getItem("configRowPerPage"));

const TransactionReport = (props) => {

  const { getTokenRefreshed: refresh, open: tokenOpen, overlay: overlay, setOpen: setTokenOpen, message: tokenMessage, messageState: tokenMessageState } = useTokenRefresh();

  const location = useLocation();
  const navigate = useNavigate();
  const buttonRef = useRef(null);

  const [openViewDialog, setOpenViewDialog] = useState(false);
  const [viewDetails, setViewDetails] = useState({});
  const [openStatusDialog, setOpenStatusDialog] = useState(false);
  const [exceptionStatus, setExceptionStatus] = useState([]);
  const [rows, setRows] = useState([]);
  const [exportRows, setExportRows] = useState("");
  const [rowChecked, setRowChecked] = useState([]);
  const [company, setCompany] = useState([]);
  const [status, setStatus] = useState("");
  const [selected, setSelected] = useState([]);
  const [selected1, setSelected1] = useState([]);
  const [buttonLoader, setButtonLoader] = useState(false);
  const [backdropLoader, setBackdropLoader] = useState(false);
  const [loading, setLoading] = useState(false);
  const [singleLoading, setSingleLoading] = useState(false);
  const [direction, setDirection] = useState(false);
  const [currentColumn, setCurrentColumn] = useState("");
  const [page, setPage] = useState(1);
  const [from, setFrom] = useState(1);
  const [to, setTo] = useState(
    configRowPerPage && configRowPerPage
      ? configRowPerPage && configRowPerPage
      : 20
  );

  const [rowsPerPage, setRowsPerPage] = useState(
    configRowPerPage && configRowPerPage
      ? configRowPerPage && configRowPerPage
      : 20
  );
  const [total, setTotal] = useState("");
  const [overallTotalAppliedPoint, setOverallTotalAppliedPoint] = useState("");
  const [overallTotalKlaviyoPoint, setOverallTotalKlaviyoPoint] = useState("");
  const [overallTotalTransactionPoint, setOverallTotalTransactionPoint] = useState("");
  const [overallTotalTransactionAmount, setOverallTotalTransactionAmount] = useState("");
  const [overallTotalTransactionOrderCount, setOverallTotalTransactionOrderCount] = useState("");

  const [filterOpen, setFilterOpen] = useState(false);

  const [open, setOpen] = useState(false);
  const [message, setMessage] = useState("");
  const [messageState, setMessageState] = useState("");
  const [companyList, setCompanyList] = useState([]);
  const [sites, setSites] = useState([]);
  const [siteNames, setSiteNames] = useState([]);
  const [filterData, setFilterData] = useState({

    id: "",
    customer_id: "",
    status: "",
    name: "",
    startDate: "",
    compareStartDate: "",
    compareEndDate: "",
    endDate: "",
    remove: false,
  });

  // useEffect(() => {
  //   const siteNamesString = siteNames.map(site => site.name).join(', ');
  //   setFilterData(prevData => ({
  //     ...prevData,
  //     name: siteNamesString,
  //   }));
  //   setSubmittedData(prevData => ({
  //     ...prevData,
  //     id: siteNamesString,
  //   }));

  // }, [siteNames])

  const [submittedData, setSubmittedData] = useState({

    id: "",
    customer_id: "",
    status: "",
    name: "",
    startDate: "",
    compareStartDate: "",
    compareEndDate: "",
    endDate: "",

    submit: false,
  });

  let val1 = filterData.startDate === "" ? "Today" : null;
  let val2 = filterData.endDate === "" ? "Today" : null;
  // let val3 = filterData.compareStartDate === "" ? "Tomorrow" : null;
  // let val4 = filterData.compareEndDate === "" ? "Tomorrow" : null;

  useEffect(() => {
    if (
      (val1 === "Today" && val2 === "Today")
    ) {
      filterData.startDate = moment().format("YYYY-MM-DD")
      filterData.endDate = moment().format("YYYY-MM-DD")
      // filterData.compareStartDate = moment().add(1, "day").format("YYYY-MM-DD");
      // filterData.compareEndDate = moment().add(1, "day").format("YYYY-MM-DD");
      setTimeout(() => {
        handleFilter();
        navigate("#", { replace: true });
      }, 1500);
    }
    setTimeout(() => {
      navigate("#", { replace: true });
    }, 1500);
  }, [val1, val2]);

  useEffect(() => {
    if (

      filterData.id === "" ||
      filterData.customer_id === "" ||
      filterData.status === "" ||
      filterData.name === "" ||
      filterData.startDate === "" ||
      filterData.endDate === "" ||
      filterData.compareStartDate === "" ||
      filterData.compareEndDate === ""

    ) {
      setSubmittedData({
        ...submittedData,
        submit: false,
      });
    }

    if (filterData.id === " ") filterData.id = "";
    if (filterData.customer_id === " ") filterData.customer_id = "";
    if (filterData.status === " ") filterData.status = "";
    if (filterData.name === " ") filterData.name = "";
    if (filterData.startDate === " ") filterData.startDate = "";
    if (filterData.endDate === " ") filterData.endDate = "";
    if (filterData.compareStartDate === " ") filterData.compareStartDate = "";
    if (filterData.compareEndDate === " ") filterData.compareEndDate = "";

    filterData.remove === true && handleFilter();
  }, [filterData]);

  useEffect(() => {
    let currentpolicy = JSON.parse(localStorage.getItem("transactionreport_filter"));
    currentpolicy !== null && setFilterData(currentpolicy);

    currentpolicy == null
      ? getTransactionReport()
      :
      currentpolicy.id == "" &&
        currentpolicy.customer_id == "" &&
        currentpolicy.status == "" &&
        currentpolicy.name == "" &&
        currentpolicy.startDate == "" &&
        currentpolicy.endDate == "" &&
        currentpolicy.compareStartDate == "" &&
        currentpolicy.compareEndDate == "" &&

        currentpolicy.removed == false
        ? getTransactionReport()
        : console.log("");
  }, []);


  const getTransactionReport = () => {
    setLoading(true);
    httpclient
      .get(`${props.request_name}&pagination=${rowsPerPage}&page=${page}`)
      .then(({ data }) => {
        if (data.status === 200) {
          setRows(data.data.stores);
          setOverallTotalAppliedPoint(data.data.totals.overallTotalAppliedPoint)
          setOverallTotalTransactionPoint(data.data.totals.overallTotalTransactionPoint)
          setOverallTotalKlaviyoPoint(data.data.totals.overallTotalKlaviyoPoint)
          setOverallTotalTransactionAmount(data.data.totals.overallTotalTransactionAmount);
          setOverallTotalTransactionOrderCount(data.data.totals.overallTotalTransactionOrderCount)
          // setSites(data.sites);
          // setTotal(data.meta.total);
          // setRowsPerPage(parseInt(data.meta.per_page));
          // setPage(data.meta.current_page);
          // setFrom(data.meta.from);
          // setTo(data.meta.to);
          setLoading(false);
        } else {
          setOpen(true);
          setMessage(data.message);
          setMessageState("error");
          setLoading(false);
        }

      }).catch((err) => {
        if (err.response.status === 401) {
          refresh();
          setOpen(tokenOpen);
          setMessage(tokenMessage);
          setMessageState("error");
        } else if (err.response.status === 422) {
          const errorMessages = Object.values(err.response.data.errors).flat();
          setOpen(true);
          setMessage(errorMessages);
          setMessageState("error");
          setLoading(false);
        } else if (err.response.status === 400) {
          const errorMessages = Object.values(err.response.data.errors).flat();
          setOpen(true);
          setMessage(errorMessages);
          setMessageState("error");
          setLoading(false);

        } else {
          setOpen(true);
          setMessage(err.response.data.message);
          setMessageState("error");
          setLoading(false);
        }
      })
  };


  const handleView = (row) => {
    setSingleLoading(true);
    setOpenViewDialog(true);
    httpclient
      .get(`request-response?requestName=lightspeed/orders/${row.id || row}`)
      .then(({ data }) => {
        if (data) {
          setViewDetails(data.data.stores);
          setSingleLoading(false);
        }
        else {
          setOpen(true);
          setMessage(data.message);
          setMessageState("error");
          setLoading(false);
        }

      }).catch((err) => {
        if (err.response.status === 401) {
          refresh();
          setOpen(tokenOpen);
          setMessage(tokenMessage);
          setMessageState("error");
        } else if (err.response.status === 422) {
          const errorMessages = Object.values(err.response.data.errors).flat();
          setOpen(true);
          setMessage(errorMessages);
          setMessageState("error");
          setLoading(false);
        } else if (err.response.status === 400) {
          const errorMessages = Object.values(err.response.data.errors).flat();
          setOpen(true);
          setMessage(errorMessages);
          setMessageState("error");
          setLoading(false);

        } else {
          setOpen(true);
          setMessage(err.response.data.message);
          setMessageState("error");
          setLoading(false);
        }
      })
  };

  const sendDetails = (callback) => {
    if (callback.open === false) {
      setOpenViewDialog(false);
      setViewDetails({});
    }
    if (callback.refetch === true) {
      handleView(callback.id);
      setTimeout(() => {
        handleFilter();
      }, 1000);
    }
  };

  const handleChange = (e) => {
    const { name, value } = e.target;

    if (name === "id") {
      setFilterData({
        ...filterData,
        name: value,
        remove: false,
      });
    }

  };

  // const handleChangeSites= (value) => {
  //   setFilterData({
  //     ...filterData,
  //     id: value !== null ? value.id : "",
  //     name: value !== null ? value.name : "",
  //     remove: false,
  //   });
  // };
  const handleChangeSites = (value) => {
    setSiteNames(value);
    const ids = value.map((item) => item.id);
    const names = value.map((item) => item.name).join(", ");

    setFilterData({
      ...filterData,
      id: ids,
      name: names,
      remove: false,
    });
  };

  const handleFilter = () => {
    setSubmittedData({
      ...submittedData,

      id: filterData.id,
      customer_id: filterData.customer_id,
      status: filterData.status,
      name: filterData.name,
      startDate: filterData.startDate,
      endDate: filterData.endDate,
      compareStartDate: filterData.compareStartDate,
      compareEndDate: filterData.compareEndDate,
      submit: true,
    });

    filterData.remove = true;

    localStorage.setItem("transactionreport_filter", JSON.stringify(filterData));


    setLoading(true);
    if (

      filterData.id ||
      filterData.customer_id ||
      filterData.status ||
      filterData.name ||
      filterData.startDate ||
      filterData.endDate ||
      filterData.compareStartDate ||
      filterData.compareEndDate

    ) {
      const idParams = filterData.id !== "" ? filterData.id
        .map((id, index) => `filters[site_id][$in][${index}]=${id}`)
        .join("&") : `filters[site_id][$in][0]=`;
      httpclient
        .get(
          `${props.request_name}&start_date=${filterData.startDate
          }&end_date=${filterData.endDate
          }&compare_start_date=${filterData.compareStartDate
          }&compare_end_date=${filterData.compareEndDate
          }&pagination=${rowsPerPage}&page=${1}`
        )
        .then(({ data }) => {
          if (data.status === 200) {
            setRows(data.data.stores);
            setOverallTotalAppliedPoint(data.data.totals.overallTotalAppliedPoint)
            setOverallTotalTransactionPoint(data.data.totals.overallTotalTransactionPoint)
            setOverallTotalKlaviyoPoint(data.data.totals.overallTotalKlaviyoPoint)
            setOverallTotalTransactionAmount(data.data.totals.overallTotalTransactionAmount);
            setOverallTotalTransactionOrderCount(data.data.totals.overallTotalTransactionOrderCount)
            // setSites(data.sites);
            // setTotal(data.meta.total);
            // setRowsPerPage(data.meta.per_page);
            // setPage(data.meta.current_page);
            // setFrom(data.meta.from);
            // setTo(data.meta.to);
            setLoading(false);
          }
          else {
            setOpen(true);
            setMessage(data.message);
            setMessageState("error");
            setLoading(false);
          }

        }).catch((err) => {
          if (err.response.status === 401) {
            refresh();
            setOpen(tokenOpen);
            setMessage(tokenMessage);
            setMessageState("error");
          } else if (err.response.status === 422) {
            const errorMessages = Object.values(err.response.data.errors).flat();
            setOpen(true);
            setMessage(errorMessages);
            setMessageState("error");
            setLoading(false);
          } else if (err.response.status === 400) {
            const errorMessages = Object.values(err.response.data.errors).flat();
            setOpen(true);
            setMessage(errorMessages);
            setMessageState("error");
            setLoading(false);

          } else {
            setOpen(true);
            setMessage(err.response.data.message);
            setMessageState("error");
            setLoading(false);
          }
        });
    } else {
      getTransactionReport();
    }
  };



  const hadleFilterOpen = () => {
    setFilterOpen((prev) => !prev);
  };

  const handleChangeFilter = (e) => {
    const { name, value } = e.target;
    setFilterData({
      ...filterData,
      [name]: value,
      remove: false,
    });
  };

  const handleRemove = (data) => {
    if (data === "id") {
      filterData.name = "";
      submittedData.name = "";
      setSiteNames([]);
    }

    if (data === "startDate") {
      setFilterData({
        ...filterData,
        startDate: "",
        endDate: "",
        remove: true,
      });
      setSubmittedData({
        ...submittedData,
        startDate: "",
        endDate: "",
      });
    } else if (data === "compareStartDate") {
      setFilterData({
        ...filterData,
        compareStartDate: "",
        compareEndDate: "",
        remove: true,
      });
      setSubmittedData({
        ...submittedData,
        startDate: "",
        endDate: "",
      });
    } else {
      setFilterData({
        ...filterData,
        [data]: "",
        remove: true,
      });
      setSubmittedData({
        ...submittedData,
        [data]: "",
      });
    }
  };

  const handleSort = (column) => {
    setDirection((prevDirection) => !prevDirection);
    setCurrentColumn(column);
    setLoading(true);
    const idParams = filterData.id !== "" ? filterData.id
      .map((id, index) => `filters[site_id][$in][${index}]=${id}`)
      .join("&") : `filters[site_id][$in][0]=`;
    submittedData.submit
      ?
      httpclient
        .get(
          `${props.request_name}&start_date=${filterData.startDate
          }&end_date=${filterData.endDate
          }&compare_start_date=${filterData.compareStartDate
          }&compare_end_date=${filterData.compareEndDate
          }&sort[0]=${column}:${!direction ? "asc" : "desc"
          }&pagination=${rowsPerPage}&page=${page}`
        )
        .then(({ data }) => {
          if (data.status === 200) {
            setRows(data.data.stores);
            // setTotal(data.meta.total);
            // setRowsPerPage(parseInt(data.meta.per_page));
            // setPage(data.meta.current_page);
            // setFrom(data.meta.from);
            // setTo(data.meta.to);
            setLoading(false);
          }
          else {
            setOpen(true);
            setMessage(data.message);
            setMessageState("error");
            setLoading(false);
          }

        }).catch((err) => {
          if (err.response.status === 401) {
            refresh();
            setOpen(tokenOpen);
            setMessage(tokenMessage);
            setMessageState("error");
          } else if (err.response.status === 422) {
            const errorMessages = Object.values(err.response.data.errors).flat();
            setOpen(true);
            setMessage(errorMessages);
            setMessageState("error");
            setLoading(false);
          } else if (err.response.status === 400) {
            const errorMessages = Object.values(err.response.data.errors).flat();
            setOpen(true);
            setMessage(errorMessages);
            setMessageState("error");
            setLoading(false);

          } else {
            setOpen(true);
            setMessage(err.response.data.message);
            setMessageState("error");
            setLoading(false);
          }
        })

      : httpclient
        .get(
          `${props.request_name}&sort[0]=${column}:${!direction ? "asc" : "desc"
          }&pagination=${rowsPerPage}&page=${page}`
        )
        .then(({ data }) => {
          if (data.status === 200) {
            setRows(data.data.stores);
            setLoading(false);
          }
          else {
            setOpen(true);
            setMessage(data.message);
            setMessageState("error");
            setLoading(false);
          }

        }).catch((err) => {
          if (err.response.status === 401) {
            refresh();
            setOpen(tokenOpen);
            setMessage(tokenMessage);
            setMessageState("error");
          } else if (err.response.status === 422) {
            const errorMessages = Object.values(err.response.data.errors).flat();
            setOpen(true);
            setMessage(errorMessages);
            setMessageState("error");
            setLoading(false);
          } else if (err.response.status === 400) {
            const errorMessages = Object.values(err.response.data.errors).flat();
            setOpen(true);
            setMessage(errorMessages);
            setMessageState("error");
            setLoading(false);

          } else {
            setOpen(true);
            setMessage(err.response.data.message);
            setMessageState("error");
            setLoading(false);
          }

        });
  };

  const handleChangePage = (e, page) => {
    setLoading(true);
    const idParams = filterData.id !== "" ? filterData.id
      .map((id, index) => `filters[site_id][$in][${index}]=${id}`)
      .join("&") : `filters[site_id][$in][0]=`;
    submittedData.submit
      ?
      httpclient
        .get(
          `${props.request_name}&start_date=${filterData.startDate
          }&end_date=${filterData.endDate
          }&compare_start_date=${filterData.compareStartDate
          }&compare_end_date=${filterData.compareEndDate
          }${currentColumn ? `&sort[0]=${currentColumn}:${direction ? "asc" : "desc"}` : ''
          }&pagination=${rowsPerPage}&page=${page}`
        )
        .then(({ data }) => {
          if (data.status === 200) {
            setRows(data.data.stores);
            // setTotal(data.meta.total);
            // setRowsPerPage(parseInt(data.meta.per_page));
            // setPage(data.meta.current_page);
            // setFrom(data.meta.from);
            // setTo(data.meta.to);
            setLoading(false);
          }
          else {
            setOpen(true);
            setMessage(data.message);
            setMessageState("error");
            setLoading(false);
          }

        }).catch((err) => {
          if (err.response.status === 401) {
            refresh();
            setOpen(tokenOpen);
            setMessage(tokenMessage);
            setMessageState("error");
          } else if (err.response.status === 422) {
            const errorMessages = Object.values(err.response.data.errors).flat();
            setOpen(true);
            setMessage(errorMessages);
            setMessageState("error");
            setLoading(false);
          } else if (err.response.status === 400) {
            const errorMessages = Object.values(err.response.data.errors).flat();
            setOpen(true);
            setMessage(errorMessages);
            setMessageState("error");
            setLoading(false);

          } else {
            setOpen(true);
            setMessage(err.response.data.message);
            setMessageState("error");
            setLoading(false);
          }

        })
      : httpclient
        .get(
          `${props.request_name
          }${currentColumn ? `&sort[0]=${currentColumn}:${direction ? "asc" : "desc"}` : ''}&pagination=${rowsPerPage}&page=${page}`
        )
        .then(({ data }) => {
          if (data.status === 200) {
            setRows(data.data.stores);
            // setTotal(data.meta.total);
            // setRowsPerPage(parseInt(data.meta.per_page));
            // setPage(data.meta.current_page);
            // setFrom(data.meta.from);
            // setTo(data.meta.to);
            setLoading(false);
          }
          else {
            setOpen(true);
            setMessage(data.message);
            setMessageState("error");
            setLoading(false);
          }

        }).catch((err) => {
          if (err.response.status === 401) {
            refresh();
            setOpen(tokenOpen);
            setMessage(tokenMessage);
            setMessageState("error");
          } else if (err.response.status === 422) {
            const errorMessages = Object.values(err.response.data.errors).flat();
            setOpen(true);
            setMessage(errorMessages);
            setMessageState("error");
            setLoading(false);
          } else if (err.response.status === 400) {
            const errorMessages = Object.values(err.response.data.errors).flat();
            setOpen(true);
            setMessage(errorMessages);
            setMessageState("error");
            setLoading(false);

          } else {
            setOpen(true);
            setMessage(err.response.data.message);
            setMessageState("error");
            setLoading(false);
          }

        });
  };

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(+event.target.value);
    setLoading(true);

    localStorage.setItem("configRowPerPage", event.target.value);
    const idParams = filterData.id !== "" ? filterData.id
      .map((id, index) => `filters[site_id][$in][${index}]=${id}`)
      .join("&") : `filters[site_id][$in][0]=`;
    submittedData.submit
      ? httpclient
        .get(
          `${props.request_name}&start_date=${filterData.startDate
          }&end_date=${filterData.endDate
          }&compare_start_date=${filterData.compareStartDate
          }&compare_end_date=${filterData.compareEndDate
          }${currentColumn ? `&sort[0]=${currentColumn}:${direction ? "asc" : "desc"}` : ''
          }&pagination=${+event.target.value}&page=${page}`
        )
        .then(({ data }) => {
          if (data.status === 200) {
            setRows(data.data.stores);
            // setTotal(data.meta.total);
            // setRowsPerPage(parseInt(data.meta.per_page));
            // setPage(data.meta.current_page);
            // setFrom(data.meta.from);
            // setTo(data.meta.to);
            setLoading(false);
          }
          else {
            setOpen(true);
            setMessage(data.message);
            setMessageState("error");
            setLoading(false);
          }

        }).catch((err) => {
          if (err.response.status === 401) {
            refresh();
            setOpen(tokenOpen);
            setMessage(tokenMessage);
            setMessageState("error");
          } else if (err.response.status === 422) {
            const errorMessages = Object.values(err.response.data.errors).flat();
            setOpen(true);
            setMessage(errorMessages);
            setMessageState("error");
            setLoading(false);
          } else if (err.response.status === 400) {
            const errorMessages = Object.values(err.response.data.errors).flat();
            setOpen(true);
            setMessage(errorMessages);
            setMessageState("error");
            setLoading(false);

          } else {
            setOpen(true);
            setMessage(err.response.data.message);
            setMessageState("error");
            setLoading(false);
          }
        })
      : httpclient
        .get(
          `${props.request_name
          }${currentColumn ? `&sort[0]=${currentColumn}:${direction ? "asc" : "desc"}` : ''}&pagination=${+event.target.value}&page=${1}`
        )
        .then(({ data }) => {
          if (data.status === 200) {
            setRows(data.data.stores);
            // setTotal(data.meta.total);
            // setRowsPerPage(parseInt(data.meta.per_page));
            // setFrom(data.meta.from);
            // setTo(data.meta.to);
            // setPage(data.meta.current_page);
            setLoading(false);
          }
          else {
            setOpen(true);
            setMessage(data.message);
            setMessageState("error");
            setLoading(false);
          }

        }).catch((err) => {
          if (err.response.status === 401) {
            refresh();
            setOpen(tokenOpen);
            setMessage(tokenMessage);
            setMessageState("error");
          } else if (err.response.status === 422) {
            const errorMessages = Object.values(err.response.data.errors).flat();
            setOpen(true);
            setMessage(errorMessages);
            setMessageState("error");
            setLoading(false);
          } else if (err.response.status === 400) {
            const errorMessages = Object.values(err.response.data.errors).flat();
            setOpen(true);
            setMessage(errorMessages);
            setMessageState("error");
            setLoading(false);

          } else {
            setOpen(true);
            setMessage(err.response.data.message);
            setMessageState("error");
            setLoading(false);
          }
        })
  };

  const handleClose = (event, reason) => {
    if (reason === "clickaway") {
      return;
    }
    setOpen(false);
    setTokenOpen(false);
  };


  return (
    <div>
      <Grid container spacing={2}>
        <Grid item md={8} xs={12}>
          <Header>
            <h1>List Transaction Report</h1>
          </Header>
        </Grid>
        <Grid
          item
          md={4}
          xs={12}
          display="flex"
          alignItems="center"
          justifyContent="flex-end"
        >
          <Button color="primary" variant="contained" onClick={hadleFilterOpen}>
            Filter <FilterList style={{ marginLeft: "5px" }} fontSize="small" />
          </Button>

        </Grid>
        <Grid
          item
          md={12}
          xs={12}
          display="flex"
          flexDirection="column"
          alignItems="flex-end"
        >
          {overallTotalTransactionOrderCount && <div><strong>Total Transaction Order Count :</strong> {overallTotalTransactionOrderCount}</div>}
          {overallTotalTransactionAmount && <div><strong>Total Transaction Amount :</strong> ${overallTotalTransactionAmount}</div>}
          {overallTotalTransactionPoint && <div><strong>Total Transaction Points :</strong> {overallTotalTransactionPoint}</div>}
          {overallTotalKlaviyoPoint && <div><strong>Total Klaviyo Points :</strong> {overallTotalKlaviyoPoint}</div>}
          {overallTotalAppliedPoint && <div><strong>Total Applied Points :</strong> {overallTotalAppliedPoint}</div>}

        </Grid>



        {/* Filter */}
        <Grid item xs={12}>
          <Collapse in={filterOpen}>
            <Card>
              <Box p={4}>
                <Grid container spacing={2}>


                  {/* <Grid item xs={12} md={4}>
                    <InputLabel>Customer ID</InputLabel>
                    <TextField
                      name="customer_id"
                      value={filterData.customer_id}
                      onChange={handleChangeFilter}
                      onKeyDown={e => { if (e.key === "Enter") handleFilter() }}
                      variant="outlined"
                      fullWidth
                    />
                  </Grid>

                  <Grid item xs={12} md={4}>
                    <InputLabel>Status</InputLabel>
                    <FormControl fullWidth>
                      <Select
                        value={filterData.status}
                        name="status"
                        onChange={handleChangeFilter}
                      >
                        <MenuItem value={""}>Select</MenuItem>
                        <MenuItem value={"ACCEPTED"}>Accepted</MenuItem>
                        <MenuItem value={"REJECTED"}>Rejected</MenuItem>
                        <MenuItem value={"COMPLETE"}>Complete</MenuItem>
                        <MenuItem value={"ON_HOLD"}>On Hold</MenuItem>

                      </Select>
                    </FormControl>

                  </Grid> */}
                  {/* <Grid item xs={12} md={4}>
                    <InputLabel>Order Name</InputLabel>
                    <TextField
                      name="name"
                      value={filterData.name}
                      onChange={handleChangeFilter}
                      onKeyDown={e => { if (e.key === "Enter") handleFilter() }}
                      variant="outlined"
                      fullWidth
                    />
                  </Grid> */}
                  <Grid item xs={12} md={4}>
                    <InputLabel>Start Date</InputLabel>
                    <TextField
                      variant="outlined"
                      name="startDate"
                      type="date"
                      value={filterData.startDate}
                      onChange={(e) => handleChangeFilter(e)}
                      fullWidth
                      InputLabelProps={{
                        shrink: true,
                      }}
                    />
                  </Grid>

                  <Grid item xs={12} md={4}>
                    <InputLabel>End Date</InputLabel>
                    <TextField
                      variant="outlined"
                      name="endDate"
                      type="date"
                      value={filterData.endDate}
                      onChange={(e) => handleChangeFilter(e)}
                      fullWidth
                      InputLabelProps={{
                        shrink: true,
                      }}
                    />
                  </Grid>
                  <Grid item xs={12} md={4}>
                    <InputLabel>Compare Start Date</InputLabel>
                    <TextField
                      variant="outlined"
                      name="compareStartDate"
                      type="date"
                      value={filterData.compareStartDate}
                      onChange={(e) => handleChangeFilter(e)}
                      fullWidth
                      InputLabelProps={{
                        shrink: true,
                      }}
                    />
                  </Grid>

                  <Grid item xs={12} md={4}>
                    <InputLabel>Compare End Date</InputLabel>
                    <TextField
                      variant="outlined"
                      name="compareEndDate"
                      type="date"
                      value={filterData.compareEndDate}
                      onChange={(e) => handleChangeFilter(e)}
                      fullWidth
                      InputLabelProps={{
                        shrink: true,
                      }}
                    />
                  </Grid>


                  <Grid item xs={12}>
                    <Box textAlign={"right"}>
                      <Button
                        variant="contained"
                        color="primary"
                        onClick={handleFilter}
                      >
                        Filter{" "}
                        <ArrowForward
                          fontSize="small"
                          style={{ marginLeft: "5px" }}
                        />
                      </Button>
                    </Box>
                  </Grid>
                </Grid>
              </Box>
            </Card>
          </Collapse>
        </Grid>

        {
          submittedData.id ||
            submittedData.customer_id ||
            submittedData.status ||
            submittedData.startDate ||
            submittedData.endDate ||
            submittedData.compareStartDate ||
            submittedData.compareEndDate
            ? (
              <Grid item xs={12}>
                <FilteredBox>
                  <span>Filtered: </span>


                  {submittedData.id && (
                    <p>
                      <span>Sites: {submittedData.name}</span>
                      <Close
                        fontSize="small"
                        onClick={() => handleRemove("id")}
                      />
                    </p>
                  )}
                  {submittedData.customer_id && (
                    <p>
                      <span>Customer ID: {submittedData.customer_id}</span>
                      <Close
                        fontSize="small"
                        onClick={() => handleRemove("customer_id")}
                      />
                    </p>
                  )}
                  {submittedData.status && (
                    <p>
                      <span>Status: {submittedData.status}</span>
                      <Close
                        fontSize="small"
                        onClick={() => handleRemove("status")}
                      />
                    </p>
                  )}
                  {(submittedData.startDate || submittedData.endDate) && (
                    <p>
                      <span>
                        Filter Date Range: {submittedData.startDate} -{" "}
                        {submittedData.endDate}
                      </span>
                      <Close
                        fontSize="small"
                        onClick={() => handleRemove("startDate")}
                      />
                    </p>
                  )}
                  {(submittedData.compareStartDate || submittedData.compareEndDate) && (
                    <p>
                      <span>
                        Filter Comparison Date Range: {submittedData.compareStartDate} -{" "}
                        {submittedData.compareEndDate}
                      </span>
                      <Close
                        fontSize="small"
                        onClick={() => handleRemove("compareStartDate")}
                      />
                    </p>
                  )}

                </FilteredBox>
              </Grid>
            ) : (
              <Box></Box>
            )}
        {/* Filter */}



        <Grid item xs={12}>
          <TableComponent
            columns={columns}
            rows={rows}
            sort={false}
            //handleView={handleView}
            handleSort={handleSort}
            loading={loading}
            handleChangeRowsPerPage={handleChangeRowsPerPage}
            handleChangePage={handleChangePage}
            // handleRowCheck={handleRowCheck}
            rowChecked={rowChecked}
            buttonLoader={buttonLoader}
            direction={direction}
            currentColumn={currentColumn}
            page={page}
            total={total && total}
            fromTable={from}
            toTable={to}
            rowsPerPage={rowsPerPage}
            filterData={filterData}
            footer={false}
          />
        </Grid>

        <Footer overlay={overlay || props.overlayNew} />
      </Grid>

      {openViewDialog && (
        <ViewReportDetail
          singleLoading={singleLoading}
          viewDetails={viewDetails}
          sendDetails={sendDetails}
        />
      )}

      {/* {backdropLoader && <BackdropLoader />} */}

      <Snackbar
        autoHideDuration={3000}
        anchorOrigin={{ vertical: "top", horizontal: "right" }}
        open={open || tokenOpen}
        onClose={handleClose}
      >
        <Alert
          onClose={handleClose}
          severity={messageState || tokenMessageState}
          sx={{ width: "100%" }}
        >
          {message || tokenMessage}
        </Alert>
      </Snackbar>
    </div>
  );
};

export default TransactionReport;
