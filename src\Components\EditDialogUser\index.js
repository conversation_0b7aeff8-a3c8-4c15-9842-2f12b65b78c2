import { Visibility, VisibilityOff } from "@mui/icons-material";
import {
    Box,
    Button,
    Dialog,
    DialogActions,
    DialogContent,
    DialogTitle,
    FormControl,
    Grid,
    IconButton,
    InputAdornment,
    InputLabel,
    MenuItem,
    Select,
    styled,
    TextField,
} from "@mui/material";
import { useEffect, useState } from "react";
import * as React from 'react';


const StyledHeaderTitle = styled(DialogTitle)(({ theme }) => ({
    background: theme.palette.primary.main,
    color: "#fff",
    position: "relative",
    "& button": {
        position: "absolute",
        right: "15px",
        top: "15px",
        color: "#fff",
    },
}));

const EditDialogUser = (props) => {
    const [dialogDetails, setDialogDetails] = useState({
        open: true,
        success: false,
    });
    const [errors, setErrors] = useState({});
    const [showPassword, setShowPassword] = useState(false);  // State for visibility toggle
    const [showConfirmPassword, setShowConfirmPassword] = useState(false);
    const [formData, setFormData] = useState({
        full_name: '',
        user_name: '',
        role_id: '',
        email: '',
        address: '',
        mobile: '',
        //status: '',
        password: '',
        confirm_password: '',
    });

    useEffect(() => {
        if (props.viewDetails) {
            setFormData({
                full_name: props.viewDetails.full_name || '',
                user_name: props.viewDetails.user_name || '',
                role_id: props.viewDetails.role_id || 1,
                email: props.viewDetails.email || '',
                address: props.viewDetails.address || '',
                mobile: props.viewDetails.mobile || '',
                //status: '',
                password: '',
                confirm_password: '',
            });
        }
    }, [props.viewDetails]);


    useEffect(() => {
        props.sendEdit(dialogDetails, formData);
    }, [dialogDetails]);


    const handleClose = () => {
        setDialogDetails({
            ...dialogDetails,
            open: false,
        });
    };

    const handleYes = () => {
        if (validate()) {
            setDialogDetails({
                ...dialogDetails,
                open: false,
                success: true,

            });
        }
    };

    const validate = () => {
        const newErrors = {};
        if (!formData.full_name) newErrors.full_name = 'Name is required';
        if (!formData.role_id) newErrors.role_id = 'Role ID is required';
        if (!formData.email) newErrors.email = 'Email is required';
        if (!formData.user_name && props.viewDetails.id) newErrors.user_name = 'Username is required';
        //if (formData.status === "") newErrors.status = 'Status is required';
        if (formData.mobile && formData.mobile.length !== 10) newErrors.mobile = 'Phone number should be of 10 digits.';
        if (!props.viewDetails.id) {
            if (!formData.password) {
                newErrors.password = 'Password is required';
            } else if (formData.password.length < 6) {
                newErrors.password = 'Password should be at least 6 characters';
            }
            if (!formData.confirm_password) {
                newErrors.confirm_password = 'Confirm Password is required';
            } else if (formData.confirm_password !== formData.password) {
                newErrors.confirm_password = 'Passwords do not match';
            }
        }
        setErrors(newErrors);
        return Object.keys(newErrors).length === 0;
    };


    const handleChange = (e) => {
        const { name, value } = e.target;
        setFormData((prevData) => ({
            ...prevData,
            [name]: value,
        }));
        if (name === "confirm_password" && value !== "" && value !== formData.password) {
            setErrors((prev) => ({
                ...prev,
                confirm_password: "Passwords do not match",
            }));
        } else {
            setErrors((prev) => ({
                ...prev,
                confirm_password: "",
            }));
        }
    };

    return (
        <div>
            <Dialog
                open={dialogDetails.open}
                onClose={handleClose}
                maxWidth="md"
                fullWidth
                aria-labelledby="alert-dialog-title"
                aria-describedby="alert-dialog-description"
            >
                <StyledHeaderTitle id="alert-dialog-title">
                    {props.viewDetails.id ? `Edit User` : `Add User`}
                </StyledHeaderTitle>
                <DialogContent>
                    <Box pt={3}>

                        <FormControl required fullWidth margin="normal">
                            <InputLabel>Roles</InputLabel>

                            <Select
                                label="Role"
                                name="role_id"
                                value={formData.role_id}
                                onChange={handleChange}
                                error={!!errors.role_id}
                                helperText={errors.role_id}
                            >
                                <MenuItem value={""}>Select</MenuItem>
                                {props.roles?.map((r) => (
                                    <MenuItem value={r.id}>{r.name}</MenuItem>
                                ))}
                            </Select>
                        </FormControl>
                        <TextField
                            required
                            label="Name"
                            name="full_name"
                            value={formData.full_name}
                            onChange={handleChange}
                            error={!!errors.full_name}
                            helperText={errors.full_name}
                            fullWidth
                            margin="normal"
                        />
                        {props.viewDetails.id &&
                            <TextField
                                required
                                label="Username"
                                name="user_name"
                                value={formData.user_name}
                                onChange={handleChange}
                                error={!!errors.user_name}
                                helperText={errors.user_name}
                                fullWidth
                                margin="normal"
                            />
                        }
                        <TextField
                            required
                            label="Email"
                            name="email"
                            type="email"
                            value={formData.email}
                            onChange={handleChange}
                            error={!!errors.email}
                            helperText={errors.email}
                            fullWidth
                            margin="normal"
                        />
                        {/* {props.viewDetails.id ? null :
                            <FormControl fullWidth margin="normal">
                                <InputLabel>Status</InputLabel>

                                <Select
                                    label="Status"
                                    name="status"
                                    value={formData.status}
                                    onChange={handleChange}
                                    error={!!errors.status}
                                    helperText={errors.status}
                                >
                                    <MenuItem value={""}>Select</MenuItem>
                                    <MenuItem value={1}>Active</MenuItem>
                                    <MenuItem value={0}>Inactive</MenuItem>

                                </Select>
                            </FormControl>
                        } */}
                        <TextField
                            label="Address"
                            name="address"
                            value={formData.address}
                            onChange={handleChange}
                            fullWidth
                            margin="normal"
                        />
                        <TextField
                            label="Mobile"
                            name="mobile"
                            value={formData.mobile}
                            onChange={handleChange}
                            error={!!errors.mobile}
                            helperText={errors.mobile}
                            fullWidth
                            margin="normal"
                        />

                        <TextField
                            label="Password"
                            name="password"
                            type={showPassword ? "text" : "password"} // Toggle between text and password
                            value={formData.password}
                            onChange={handleChange}
                            error={!!errors.password}
                            helperText={errors.password}
                            fullWidth
                            margin="normal"
                            autoComplete="new-password"
                            InputProps={{
                                endAdornment: (
                                    <InputAdornment position="end">
                                        <IconButton
                                            onClick={() => setShowPassword(!showPassword)}
                                            edge="end"
                                        >
                                            {showPassword ? <VisibilityOff /> : <Visibility />}
                                        </IconButton>
                                    </InputAdornment>
                                ),
                            }}
                        />
                        {/* {props.viewDetails.id ? null : */}
                        <TextField
                            label="Confirm Password"
                            name="confirm_password"
                            type={showConfirmPassword ? "text" : "password"}
                            value={formData.confirm_password}
                            onChange={handleChange}
                            error={!!errors.confirm_password}
                            helperText={errors.confirm_password}
                            fullWidth
                            margin="normal"
                            InputProps={{
                                endAdornment: (
                                    <InputAdornment position="end">
                                        <IconButton
                                            onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                                            edge="end"
                                        >
                                            {showConfirmPassword ? <VisibilityOff /> : <Visibility />}
                                        </IconButton>
                                    </InputAdornment>
                                ),
                            }}
                        />
                        {/* } */}

                    </Box>
                </DialogContent>
                <DialogActions styled={{ margin: "5px 10px" }}>
                    <Button onClick={handleClose} color="error" variant="contained" autoFocus>
                        Cancel
                    </Button>
                    <Button
                        onClick={handleYes}
                        color="primary"
                        variant="contained"
                        autoFocus
                    >
                        {props.viewDetails.id ? `Edit` : `Save`}
                    </Button>
                </DialogActions>
            </Dialog>
        </div>
    );
};

export default EditDialogUser;
