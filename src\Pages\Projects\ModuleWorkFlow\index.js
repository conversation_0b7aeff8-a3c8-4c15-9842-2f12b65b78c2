import React, { useEffect, useState, useRef } from "react";
import {
    <PERSON><PERSON><PERSON><PERSON>,
    Close,
    Download,
    Filter<PERSON>ist,
    Replay,
    Undo,
} from "@mui/icons-material";

import httpclient from "../../../Utils";
import {
    Box,
    Button,
    Card,
    Collapse,
    Grid,
    InputLabel,
    styled,
    TextField,
    Snackbar,
    Select,
    MenuItem,
    FormControl,
    Autocomplete,
    Tooltip,
} from "@mui/material";
import MuiAlert from "@mui/material/Alert";
import ViewDetail from "./ViewDetail";
import TableComponent from "../../../Components/TableComponent";
import useTokenRefresh from "../../../Hooks/useTokenRefresh";
import { useLocation } from "react-router-dom";


const Alert = React.forwardRef(function Alert(props, ref) {
    return <MuiAlert elevation={6} ref={ref} variant="filled" {...props} />;
});



const columns = [
    // { id: "id", name: "ID" },
    { id: "name", name: "Module Name" },
    { id: "statusInfo", name: "Status" },
    { id: "lastUpdatedModule", name: "Last Updated" },
    { id: "lastError", name: "Last Error" },
    { id: "lastRun", name: "Last Run" },
    { id: "actionModule", name: "Action" },
];

const FilteredBox = styled(Box)(({ theme }) => ({
    background: "#f9f9f9",
    padding: "5px 10px",
    borderRadius: "5px",
    "& p": {
        margin: "0",
        marginRight: "10px",
        display: "inline-block",
        background: "#dedede",
        borderRadius: "10px",
        padding: "2px 5px",
    },
    "& svg": {
        fontSize: "15px",
        cursor: "pointer",
        position: "relative",
        top: "3px",
        background: theme.palette.primary.dark,
        color: "#fff",
        borderRadius: "50%",
        padding: "2px",
        marginLeft: "2px",
    },
}));

const Header = styled("div")(({ theme }) => ({
    "& h1": {
        color: theme.palette.primary.dark,
        margin: "0",
    },
}));



const ModuleWorkflow = () => {
    const location = useLocation();
    const { getTokenRefreshed: refresh, open: tokenOpen, overlay: overlay, setOpen: setTokenOpen, message: tokenMessage, messageState: tokenMessageState } = useTokenRefresh();
    const [openViewDialog, setOpenViewDialog] = useState(false);
    const [ID, setID] = useState("");
    const [moduleName, setModuleName] = useState("");
    const [viewDetails, setViewDetails] = useState({});
    const [rows, setRows] = useState([]);
    const [loading, setLoading] = useState(false);
    const [singleLoading, setSingleLoading] = useState(false);
    const [open, setOpen] = useState(false);
    const [message, setMessage] = useState("");
    const [messageState, setMessageState] = useState("");
    const [projectName, setProjectName] = useState("");
    const [newName, setNewName] = useState("");
    const [projectID, setProjectID] = useState("");

    useEffect(() => {
        if (location.state !== null) {
            if (location.state?.id) {
                getOrdersException(location.state.id);
                setProjectName(location.state.name);
                setProjectID(location.state.id);
            } else if (location.state?.moduleID) {
                getOrdersException();
                setNewName(location.state.moduleName);
                handleView(location.state.moduleID);             
            }
        } else {
            getOrdersException();
        }
    }, [location.state]);


    const getOrdersException = (id) => {
        setLoading(true);
        const api = id ? `workflow/list?project_id=${id}` : `workflow/list`
        httpclient
            .get(api)
            .then(({ data }) => {
                if (data.status === 200) {
                    setRows(data.data);
                    setLoading(false);
                }
            }).catch((err) => {
                if (err.response.status === 401) {
                    refresh();
                    setOpen(tokenOpen);
                    setMessage(tokenMessage);
                    setMessageState("error");
                } else if (err.response.status === 422) {
                    const errorMessages = Object.values(err.response.data.errors).flat();
                    setOpen(true);
                    setMessage(errorMessages);
                    setMessageState("error");
                    setLoading(false);
                } else if (err.response.status === 400) {
                    const errorMessages = Object.values(err.response.data.errors).flat();
                    setOpen(true);
                    setMessage(errorMessages);
                    setMessageState("error");
                    setLoading(false);

                } else {
                    setOpen(true);
                    setMessage(err.response.data.message);
                    setMessageState("error");
                    setLoading(false);
                }
            })
    };

    const handleView = (row) => {
        setSingleLoading(true);
        setOpenViewDialog(true);
        setID(row.id || row);
        setModuleName(row.name);
        setProjectID(projectID);
    };

    const sendDetails = (callback) => {
        if (callback.open === false) {
            setOpenViewDialog(false);
            setViewDetails({});
        }
    };

    const handleRefetch = () => {
        getOrdersException();
    }
    const handleUndo = () => {
        location.state = null
        setProjectName("");
        setProjectID("");
    }

    const handleClose = (event, reason) => {
        if (reason === "clickaway") {
            return;
        }
        setOpen(false);
    };


    return (
        <div>
            <Grid container spacing={2}>
                <Grid item md={8} xs={12}>
                    <Header>
                        <h1>
                            List Module Workflow {projectName !== "" && `(${projectName})`}{projectName !== "" && <Tooltip title="Undo Filter"><Replay onClick={handleUndo} /></Tooltip>}
                        </h1>

                    </Header>
                </Grid>
                <Grid
                    item
                    md={4}
                    xs={12}
                    display="flex"
                    alignItems="center"
                    justifyContent="flex-end"
                >

                </Grid>
                {/* Filter */}



                {/* Filter */}

                <Grid item xs={12}>
                    <TableComponent
                        columns={columns}
                        rows={rows}
                        sort={false}
                        view={true}
                        handleView={handleView}
                        loading={loading}
                        footer={false}

                    />
                </Grid>
            </Grid>

            {openViewDialog && (
                <ViewDetail
                    loading={singleLoading}
                    setLoading={setSingleLoading}
                    ID={ID}
                    moduleName={moduleName}
                    projectID={projectID}
                    newName={newName}
                    // viewDetails={viewDetails}
                    sendDetails={sendDetails}
                />
            )}

            <Snackbar
                autoHideDuration={3000}
                anchorOrigin={{ vertical: "top", horizontal: "right" }}
                open={open}
                onClose={handleClose}
            >
                <Alert
                    onClose={handleClose}
                    severity={messageState}
                    sx={{ width: "100%" }}
                >
                    {message}
                </Alert>
            </Snackbar>
        </div>
    );
};

export default ModuleWorkflow;
