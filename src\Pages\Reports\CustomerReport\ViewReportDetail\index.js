import { Check, Clear, Close, Download, KeyboardArrowLeft, Sync, Visibility } from "@mui/icons-material";
import {
    AppBar,
    Box,
    Button,
    CircularProgress,
    Dialog,
    DialogActions,
    DialogContent,
    DialogTitle,
    Grid,
    IconButton,
    Skeleton,
    styled,
    useTheme,
    Snackbar
} from "@mui/material";
import moment from "moment";
import React, { useEffect, useState } from "react";
import Tabs from "@mui/material/Tabs";
import Tab from "@mui/material/Tab";
import Typography from "@mui/material/Typography";
import PropTypes from "prop-types";
//import BasicTable from "../../../../Components/BasicTable";
import FsLightbox from "fslightbox-react";

import MuiAlert from "@mui/material/Alert";
//import ViewPolicyDialog from "../../price_policy/ViewPolicyDialog";
import parse from "html-react-parser";
import BasicTable from "../../../../Components/BasicTable";
import TableComponent from "../../../../Components/TableComponent";
import httpclient from "../../../../Utils";
//import BasicTable from "../../../Components/BasicTable";


const Alert = React.forwardRef(function Alert(props, ref) {
    return <MuiAlert elevation={6} ref={ref} variant="filled" {...props} />;
});

const StyledHeaderTitle = styled(DialogTitle)(({ theme }) => ({
    background: theme.palette.primary.main,
    color: "#fff",
    position: "relative",
    "& button": {
        position: "absolute",
        right: "15px",
        top: "15px",
        color: "#fff",
    },
}));

const FlexContent = styled("div")(({ theme }) => ({
    display: "flex",
    flexDirection: "row",
    fontSize: "17px",
    marginBottom: "10px",
    alignItems: "flex-start",
}));


const FlexInnerTitle = styled("div")(({ theme }) => ({
    display: "flex",
    alignItems: "center",
    justifyContent: "space-between",
    minWidth: "249px",
    maxWidth: "250px",
    fontWeight: "600",
}));
const FlexInnerTitle1 = styled("div")(({ theme }) => ({
    display: "flex",
    alignItems: "center",
    justifyContent: "space-between",
    minWidth: "180px",
    maxWidth: "180px",
    fontWeight: "600",
}));

const FlexContent2 = styled("div")(({ theme }) => ({
    display: "flex",
    flexDirection: "row",
    fontSize: "17px",
}));

const FlexInnerTitle2 = styled("div")(({ theme }) => ({
    display: "flex",
    fontWeight: "600",
    gap: "5px",
    marginRight: "5px",
}));

const BoxDiv = styled("div")(({ theme }) => ({
    textAlign: "center",
}));

const Values = styled("div")(({ theme }) => ({
    marginLeft: "10px",
    fontWeight: "500",
    color: theme.palette.primary.dark,
}));

const ImageDiv = styled("div")(({ theme }) => ({
    display: "flex",
    alignItems: "center",
    width: "100%",
    flexWrap: "wrap",
    marginBottom: "10px",
}));

const ImageCell = styled("div")(({ theme }) => ({
    margin: "10px",
    width: "280px",
    borderRadius: "5px",
    overflow: "hidden",
    "& img": {
        width: "250px",
        height: "250px",
        objectFit: "cover",
        transition: "0.5s",
        boxShadow: theme.palette.primary.shadow,
        marginBottom: "10px",
        overflow: "hidden",
    },
    "& img:hover": {
        transform: "scale(1.1)",
    },
}));

const price_policyBox = styled(Box)(({ theme }) => ({
    display: "flex",
    marginBottom: "15px",
    "& h5": {
        margin: "5px",
    },
}));

const AppBarTabs = styled(AppBar)(({ theme }) => ({
    background: "#fff",
    color: theme.palette.primary.dark,
}));

function TabPanel(props) {
    const { children, value, index, ...other } = props;

    return (
        <div
            role="tabpanel"
            hidden={value !== index}
            id={`full-width-tabpanel-${index}`}
            aria-labelledby={`full-width-tab-${index}`}
            {...other}
        >
            {value === index && (
                <Box sx={{ p: 3 }}>
                    <Typography>{children}</Typography>
                </Box>
            )}
        </div>
    );
}

TabPanel.propTypes = {
    children: PropTypes.node,
    index: PropTypes.number.isRequired,
    value: PropTypes.number.isRequired,
};

function a11yProps(index) {
    return {
        id: `full-width-tab-${index}`,
        "aria-controls": `full-width-tabpanel-${index}`,
    };
}

// const pointTransactionColumns = [
//     { id: "sn", name: "SN" },
//     { id: "reference_id", name: "Reference ID" },
//     { id: "point", name: "Points" },
//     { id: "remarks", name: "Remarks" },
//     { id: "created_at", name: "Created Date" },

// ];
const pointTransactionColumns = [
    { id: "sn", name: "SN" },
    { id: "product_name", name: "Item Name" },
    { id: "item_price", name: "Item Price" },
    { id: "quantity", name: "Quantity" },
    { id: "paid_amount", name: "Paid Amount" },

];

const orderTransactionColumns = [
    { id: "id", name: "Order ID" },
    { id: "status", name: "Status" },
    { id: "total", name: "Total Amount" },
    { id: "paid", name: "Paid Amount" },

    { id: "site_id", name: "Site" },
    { id: "staff_member_id", name: "Staff" },

    { id: "ls_created_at", name: "Transaction Date" },

];

const rewardColumns = [
    { id: "applied_rule_id", name: "Reward ID" },
    { id: "rule_name", name: "Reward Name" },
    { id: "applied_time", name: "Eligible Date" },
    { id: "is_redeemed", name: "Redeemed ?" },
    { id: "redeemed_date", name: "Redeemed Date" },
];

const configRowPerPage = JSON.parse(localStorage.getItem("configRowPerPage"));

const ViewReportDetail = (props) => {
    const theme = useTheme();
    const [value, setValue] = useState(0);
    const [open, setOpen] = useState(false);
    const [message, setMessage] = useState("");
    const [messageState, setMessageState] = useState("");
    const [togglerLanding, setTogglerLanding] = useState(false);
    const [imgIndex1, setImgIndex1] = useState(0);
    const [pointTransectionDetails, setPointTransectionDetails] = useState("");
    const [direction, setDirection] = useState(false);
    const [loading, setLoading] = useState(false);
    const [currentColumn, setCurrentColumn] = useState("");
    const [page, setPage] = useState(1);
    const [from, setFrom] = useState(1);
    const [rows, setRows] = useState([]);
    const [to, setTo] = useState(
        configRowPerPage && configRowPerPage
            ? configRowPerPage && configRowPerPage
            : 20
    );

    const [rowsPerPage, setRowsPerPage] = useState(
        configRowPerPage && configRowPerPage
            ? configRowPerPage && configRowPerPage
            : 20
    );
    const [total, setTotal] = useState("");


    const handleChange = (event, newValue) => {
        setValue(newValue);
    };


    const [dialogDetails, setDialogDetails] = useState({
        open: true,
    });

    useEffect(() => {
        props.sendDetails(dialogDetails);
    }, [props, dialogDetails]);

    const handleImageTogglerLanding = (index) => {
        setImgIndex1(index);
        setTogglerLanding((prev) => !prev);
    };

    const handleClose = () => {
        setDialogDetails({
            ...dialogDetails,
            open: false,
        });
    };

    const displayText = (descriptionTexts) => {
        const textIsEmpty = descriptionTexts === null || descriptionTexts === "";
        return !textIsEmpty ? (
            parse(descriptionTexts)
        ) : (
            "-"
        );
    }

    useEffect(() => {
        if (props.viewDetails.id) {
            handleOrderTransections(props.viewDetails.id);
        }
    }, [props]);

    const handleOrderTransections = (id) => {
        setLoading(true);
        httpclient
            .get(`request-response?requestName=lightspeed/orders/customer/${id}&pagination=${rowsPerPage}&page=${page}`)
            .then(({ data }) => {
                if (data.status === 200) {
                    setRows(data.data);
                    setTotal(data.meta.total);
                    setRowsPerPage(parseInt(data.meta.per_page));
                    setPage(data.meta.current_page);
                    setFrom(data.meta.from);
                    setTo(data.meta.to);
                    setLoading(false);
                } else {
                    setOpen(true);
                    setMessage(data.message);
                    setMessageState("error");
                    setLoading(false);
                }
            })
    }

    const handleChangeRowsPerPage = (event) => {
        setRowsPerPage(+event.target.value);
        setLoading(true);
        httpclient
            .get(`request-response?requestName=lightspeed/orders/customer/${props.viewDetails.id}&pagination=${+event.target.value}&page=${page}`)
            .then(({ data }) => {
                if (data.status === 200) {
                    setRows(data.data);
                    setTotal(data.meta.total);
                    setRowsPerPage(parseInt(data.meta.per_page));
                    setPage(data.meta.current_page);
                    setFrom(data.meta.from);
                    setTo(data.meta.to);
                    setLoading(false);
                } else {
                    setOpen(true);
                    setMessage(data.message);
                    setMessageState("error");
                    setLoading(false);
                }
            })
    }

    const handleChangePage = (e, page) => {
        setLoading(true);
        httpclient
            .get(`request-response?requestName=lightspeed/orders/customer/${props.viewDetails.id}&pagination=${rowsPerPage}&page=${page}`)
            .then(({ data }) => {
                if (data.status === 200) {
                    setRows(data.data);
                    setTotal(data.meta.total);
                    setRowsPerPage(parseInt(data.meta.per_page));
                    setPage(data.meta.current_page);
                    setFrom(data.meta.from);
                    setTo(data.meta.to);
                    setLoading(false);
                } else {
                    setOpen(true);
                    setMessage(data.message);
                    setMessageState("error");
                    setLoading(false);
                }
            })
    }


    const handleView = (row) => {
        setPointTransectionDetails(row);
    };

    const handleBack = () => {
        setPointTransectionDetails("");
    }


    const handleCloseSnack = (event, reason) => {
        if (reason === "clickaway") {
            return;
        }
        setOpen(false);
    };

    return (
        <div>
            <Dialog
                open={dialogDetails.open}
                onClose={handleClose}
                maxWidth="xl"
                fullWidth
                aria-labelledby="alert-dialog-title"
                aria-describedby="alert-dialog-description"
            >
                <StyledHeaderTitle id="alert-dialog-title">
                    <div>
                        View Customer Details{" "}
                        {"(" +
                            //   (props.viewDetails.handle || "-") +
                            //   "/" +
                            (props.viewDetails.id || "-") +
                            ")"}
                    </div>
                    <IconButton onClick={handleClose}>
                        <Close />
                    </IconButton>
                </StyledHeaderTitle>
                {props.singleLoading ? (
                    <DialogContent>
                        <Grid container spacing={2}>
                            <Grid item xs={12} md={6}>
                                <Box p={3} sx={{ width: "100%" }}>
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                </Box>
                            </Grid>
                            <Grid item xs={12} md={6}>
                                <Box p={3} sx={{ width: "100%" }}>
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                </Box>
                            </Grid>
                        </Grid>
                    </DialogContent>
                ) : (
                    <DialogContent sx={{ padding: "0" }}>
                        <AppBarTabs position="static">
                            <Tabs
                                value={value}
                                onChange={handleChange}
                                indicatorColor="secondary"
                                textColor="inherit"
                                variant="fullWidth"
                                aria-label="full width tabs example"
                            >

                                <Tab label="Customer Details" {...a11yProps(0)} />
                                <Tab label="Order Transaction" {...a11yProps(1)} />
                                <Tab label="Reward History" {...a11yProps(2)} />
                            </Tabs>
                        </AppBarTabs>


                        <TabPanel value={value} index={0} dir={theme.direction}>

                            <Box>
                                <Grid container>
                                    <Grid item xs={12} md={6}>
                                        <FlexContent>
                                            <FlexInnerTitle>
                                                <span>Customer ID</span> <span> : </span>
                                            </FlexInnerTitle>
                                            <Values>{props.viewDetails.id || "-"}</Values>
                                        </FlexContent>
                                        <FlexContent>
                                            <FlexInnerTitle>
                                                <span>Company ID</span> <span> : </span>
                                            </FlexInnerTitle>
                                            <Values>{props.viewDetails.company_id || "-"}</Values>
                                        </FlexContent>
                                        <FlexContent>
                                            <FlexInnerTitle>
                                                <span>LiteCard Member ID</span> <span> : </span>
                                            </FlexInnerTitle>
                                            <Values>{props.viewDetails.litecard_member_id || "-"}</Values>
                                        </FlexContent>
                                        <FlexContent>
                                            <FlexInnerTitle>
                                                <span>Customer Name</span> <span> : </span>
                                            </FlexInnerTitle>
                                            <Values>{[props.viewDetails.first_name, props.viewDetails.last_name].filter(Boolean).join(' ')}</Values>
                                        </FlexContent>

                                        <FlexContent>
                                            <FlexInnerTitle>
                                                <span>Customer Primary Address</span> <span> : </span>
                                            </FlexInnerTitle>
                                            <Values>{[props.viewDetails.primary_address, props.viewDetails.primary_city, props.viewDetails.primary_state, props.viewDetails.primary_postal_code, props.viewDetails.primary_country].filter(Boolean).join(', ')}</Values>
                                        </FlexContent>

                                        <FlexContent>
                                            <FlexInnerTitle>
                                                <span>Customer Shipping Address</span> <span> : </span>
                                            </FlexInnerTitle>
                                            <Values>{[props.viewDetails.shipping_address, props.viewDetails.shipping_city, props.viewDetails.shipping_state, props.viewDetails.shipping_postal_code, props.viewDetails.shipping_country].filter(Boolean).join(', ')}</Values>
                                        </FlexContent>

                                        <FlexContent>
                                            <FlexInnerTitle>
                                                <span>Primary Email Address</span> <span> : </span>
                                            </FlexInnerTitle>
                                            <Values>{props.viewDetails.primary_email_address || "-"}</Values>
                                        </FlexContent>

                                        <FlexContent>
                                            <FlexInnerTitle>
                                                <span>Phone</span> <span> : </span>
                                            </FlexInnerTitle>
                                            <Values>
                                                {props.viewDetails.phone || "-"}
                                            </Values>
                                        </FlexContent>

                                    </Grid>
                                    <Grid item xs={12} md={6}>
                                        {/* <FlexContent>
                                            <FlexInnerTitle>
                                                <span>Tags</span> <span> : </span>
                                            </FlexInnerTitle>
                                            <Values>{JSON.parse(props.viewDetails.tags) || "-"}</Values>
                                        </FlexContent> */}

                                        <FlexContent>
                                            <FlexInnerTitle>
                                                <span>Current Points</span> <span> : </span>
                                            </FlexInnerTitle>
                                            <Values>
                                                {props.viewDetails.currentPoints || "-"}
                                            </Values>
                                        </FlexContent>
                                        <FlexContent>
                                            <FlexInnerTitle>
                                                <span>Points Redemption</span> <span> : </span>
                                            </FlexInnerTitle>
                                            <Values>
                                                {props.viewDetails.pointsRedemption || "-"}
                                            </Values>
                                        </FlexContent>
                                        <FlexContent>
                                            <FlexInnerTitle>
                                                <span>Date Of Birth</span> <span> : </span>
                                            </FlexInnerTitle>
                                            <Values>
                                                {props.viewDetails.dob
                                                    ? moment(props.viewDetails.dob).format("ddd, DD MMM YYYY, h:mm A")
                                                    : "-"}
                                            </Values>

                                        </FlexContent>


                                        <FlexContent>
                                            <FlexInnerTitle>
                                                <span>Customer Created Date</span> <span> : </span>
                                            </FlexInnerTitle>
                                            <Values>{moment(props.viewDetails.ls_created_at).format("ddd, DD MMM YYYY, h:mm A")}</Values>
                                        </FlexContent>

                                        <FlexContent>
                                            <FlexInnerTitle>
                                                <span>Customer Updated Date</span> <span> : </span>
                                            </FlexInnerTitle>
                                            <Values>{moment(props.viewDetails.ls_updated_at).format("ddd, DD MMM YYYY, h:mm A")}</Values>
                                        </FlexContent>
                                    </Grid>
                                </Grid>
                            </Box>

                        </TabPanel>
                        <TabPanel value={value} index={1} dir={theme.direction}>
                            {pointTransectionDetails ? (
                                <>
                                    <Box display={"flex"} justifyContent={"space-between"}>
                                        {/* <h3>Point Transection Details</h3> */}
                                        <Button onClick={handleBack}>
                                            <KeyboardArrowLeft fontSize="small" sx={{ marginRight: "5px" }} />
                                            <span>Back</span>
                                        </Button>
                                        <span><strong>Transaction Date:</strong> {moment(pointTransectionDetails.ls_created_at).format("ddd, DD MMM YYYY, h:mm A")} </span>
                                    </Box>

                                    <BasicTable
                                        columns={pointTransactionColumns}
                                        rows={pointTransectionDetails.lines}
                                    />
                                    <Box display={"flex"} justifyContent={"space-between"} sx={{ marginTop: "10px" }}>
                                        {/* <h3>Point Transection Details</h3> */}
                                        <Grid item xs={12} md={6}></Grid>

                                        <Grid item xs={12} md={6} style={{ borderLeft: "1px solid #999", height: "100%" }}>
                                            <Box pl={2}>
                                                <FlexContent>
                                                    <FlexInnerTitle1>
                                                        <span>Total Amount</span> <span> : </span>
                                                    </FlexInnerTitle1>
                                                    <Values>${pointTransectionDetails.reports?.total_amount}</Values>
                                                </FlexContent>
                                                <FlexContent>
                                                    <FlexInnerTitle1>
                                                        <span>Total Gain Point</span> <span> : </span>
                                                    </FlexInnerTitle1>
                                                    <Values>{pointTransectionDetails.reports?.gain_point}</Values>
                                                </FlexContent>
                                                <FlexContent>
                                                    <FlexInnerTitle1>
                                                        <span>Redeemed Point</span> <span> : </span>
                                                    </FlexInnerTitle1>
                                                    <Values>{pointTransectionDetails.reports?.redeemed_point}</Values>
                                                </FlexContent>
                                                <FlexContent>
                                                    <FlexInnerTitle1>
                                                        <span>Current Point</span> <span> : </span>
                                                    </FlexInnerTitle1>
                                                    <Values>{pointTransectionDetails.reports?.current_point}</Values>
                                                </FlexContent>
                                                <FlexContent>
                                                    <FlexInnerTitle1>
                                                        <span>Product Redemption</span> <span> : </span>
                                                    </FlexInnerTitle1>
                                                    <Values>{pointTransectionDetails.reports?.product_redeemptions.map((p) => p).join(', ')}</Values>
                                                </FlexContent>
                                            </Box>
                                        </Grid>
                                    </Box>
                                </>
                            ) : (
                                <TableComponent
                                    columns={orderTransactionColumns}
                                    rows={rows}
                                    page={page}
                                    total={total && total}
                                    fromTable={from}
                                    toTable={to}
                                    rowsPerPage={rowsPerPage}
                                    loading={loading}
                                    handleChangeRowsPerPage={handleChangeRowsPerPage}
                                    handleChangePage={handleChangePage}
                                    handleView={handleView}
                                />
                            )}
                        </TabPanel>

                        <TabPanel value={value} index={2} dir={theme.direction}>
                            <BasicTable
                                columns={rewardColumns}
                                rows={props.viewDetails.rewardHistories}
                            />
                        </TabPanel>
                    </DialogContent>
                )}
                <DialogActions>
                    <Button onClick={handleClose} variant="outlined" color="primary">
                        Close
                    </Button>
                </DialogActions>
            </Dialog>



            <Snackbar
                autoHideDuration={3000}
                anchorOrigin={{ vertical: "top", horizontal: "right" }}
                open={open}
                onClose={handleCloseSnack}
            >
                <Alert
                    onClose={handleCloseSnack}
                    severity={messageState}
                    sx={{ width: "100%" }}
                >
                    {message}
                </Alert>
            </Snackbar>
        </div>
    );
};

export default ViewReportDetail;
