import {
    Table,
    TableBody,
    TableCell,
    TableContainer,
    TableHead,
    TableRow,
    Paper,
    Button,
    Typography,
    Skeleton,
} from '@mui/material';
import { styled } from '@mui/material/styles';

const StyledTableCell = styled(TableCell)(({ theme }) => ({
    fontWeight: 'bold',
    backgroundColor: theme.palette.grey[100],
}));

const ErplyHeaderCell = styled(TableCell)(({ theme }) => ({
    fontWeight: 'bold',
    backgroundColor: theme.palette.primary.main,
    color: theme.palette.primary.contrastText,
    textAlign: 'center',
}));

const ShopifyHeaderCell = styled(TableCell)(({ theme }) => ({
    fontWeight: 'bold',
    backgroundColor: theme.palette.secondary.main,
    color: theme.palette.secondary.contrastText,
    textAlign: 'center',
}));

const StyledTableRow = styled(TableRow)(({ theme }) => ({
    '&:nth-of-type(odd)': {
        backgroundColor: theme.palette.action.hover,
    },
    '&:hover': {
        backgroundColor: theme.palette.action.selected,
    },
}));

const VariantsTable = ({ variants, handleMapping, loading }) => {
    function getSizeAndColourValue(jsonString, separator = ' ') {
        try {
            const variations = JSON.parse(jsonString);

            if (!Array.isArray(variations)) return '';

            const colour = variations.find(v => v.name === 'Colour')?.value || '';
            const size = variations.find(v => v.name === 'Size')?.value || '';

            return [size, colour].filter(Boolean).join(separator);
        } catch (error) {
            console.error('Invalid JSON string:', error);
            return '';
        }
    }
    return (
        <Paper sx={{ width: "100%", overflow: "hidden", marginBottom: "20px" }}>
            <TableContainer sx={{ maxHeight: 600 }}>
                <Table stickyHeader aria-label="variants table" sx={{ minWidth: 1200 }}>
                    <TableHead>
                        <TableRow>
                            <ErplyHeaderCell colSpan={5}>ERPLY VARIANT DETAILS</ErplyHeaderCell>
                            <ShopifyHeaderCell colSpan={5}>SHOPIFY VARIANT DETAILS</ShopifyHeaderCell>
                            <StyledTableCell>Actions</StyledTableCell>
                        </TableRow>
                        <TableRow>
                            {/* Erply columns */}
                            {/* <StyledTableCell sx={{ minWidth: '100px' }}>Product ID</StyledTableCell> */}
                            <StyledTableCell sx={{ minWidth: '150px' }}>Product Code</StyledTableCell>
                            <StyledTableCell sx={{ minWidth: '200px' }}>Size Color</StyledTableCell>
                            <StyledTableCell sx={{ minWidth: '100px' }}>Barcode</StyledTableCell>
                            <StyledTableCell sx={{ minWidth: '100px' }}>Price (VAT)</StyledTableCell>
                            <StyledTableCell sx={{ minWidth: '100px', borderRight: '2px solid #8c8c8c' }}>Status</StyledTableCell>

                            {/* Shopify columns */}
                            <StyledTableCell sx={{ minWidth: '200px' }}>Size Color</StyledTableCell>
                            <StyledTableCell sx={{ minWidth: '150px' }}>SKU</StyledTableCell>
                            <StyledTableCell sx={{ minWidth: '100px' }}>Barcode</StyledTableCell>
                            <StyledTableCell sx={{ minWidth: '100px' }}>Price</StyledTableCell>
                            <StyledTableCell sx={{ minWidth: '100px'}}>Status</StyledTableCell>

                            {/* Actions */}
                            <StyledTableCell sx={{ minWidth: '150px' }}>Actions</StyledTableCell>
                        </TableRow>
                    </TableHead>
                    <TableBody>
                        {loading ? (
                            [...Array(5)].map((_, rowIndex) => (
                                <TableRow key={rowIndex}>
                                    {/* Erply columns */}
                                    <TableCell><Skeleton variant="text" /></TableCell>
                                    <TableCell><Skeleton variant="text" /></TableCell>
                                    <TableCell><Skeleton variant="text" /></TableCell>
                                    <TableCell><Skeleton variant="text" /></TableCell>
                                    <TableCell sx={{ borderRight: '2px solid #ddd' }}><Skeleton variant="text" /></TableCell>
                                    
                                    {/* Shopify columns */}
                                    <TableCell><Skeleton variant="text" /></TableCell>
                                    <TableCell><Skeleton variant="text" /></TableCell>
                                    <TableCell><Skeleton variant="text" /></TableCell>
                                    <TableCell><Skeleton variant="text" /></TableCell>
                                    <TableCell sx={{ borderRight: '2px solid #ddd' }}><Skeleton variant="text" /></TableCell>
                                    
                                    {/* Actions column */}
                                    <TableCell><Skeleton variant="text" /></TableCell>
                                </TableRow>
                            ))
                        ) : variants?.length > 0 ? (
                            variants?.map((variant, index) => (
                                <StyledTableRow
                                    key={variant.productID || index}
                                    sx={{
                                        backgroundColor: variant.shopify_product ? "#e8f5e8 !important" : undefined,
                                    }}
                                >
                                    {/* Erply data */}
                                    {/* <TableCell>{variant.productID || "-"}</TableCell> */}
                                    <TableCell>{variant.code || "-"}</TableCell>
                                    <TableCell
                                        sx={{
                                            maxWidth: '200px',
                                            overflow: 'hidden',
                                            textOverflow: 'ellipsis',
                                            whiteSpace: 'nowrap'
                                        }}
                                        title={variant.name}
                                    >
                                        {getSizeAndColourValue(variant.variationDescription) || "-"}
                                    </TableCell>
                                    <TableCell>{variant.code2 || "-"}</TableCell>
                                    <TableCell>${Number(variant.priceWithVat || 0).toFixed(2)}</TableCell>
                                    <TableCell sx={{ borderRight: '2px solid #8c8c8c' }}>
                                        {variant.status || "-"}
                                    </TableCell>

                                    {/* Shopify data */}
                                    <TableCell
                                        sx={{
                                            maxWidth: '200px',
                                            overflow: 'hidden',
                                            textOverflow: 'ellipsis',
                                            whiteSpace: 'nowrap'
                                        }}
                                        title={variant.shopify_product?.title}
                                    >
                                        {variant.shopify_product?.title || (
                                            <Typography variant="body2" color="textSecondary" sx={{ fontStyle: 'italic' }}>
                                                No mapping
                                            </Typography>
                                        )}
                                    </TableCell>
                                    <TableCell>{variant.shopify_product?.sku || "-"}</TableCell>
                                    <TableCell>{variant.shopify_product?.barcode || "-"}</TableCell>
                                    <TableCell>
                                        {variant.shopify_product?.price ?
                                            `$${Number(variant.shopify_product.price).toFixed(2)}` : "-"
                                        }
                                    </TableCell>
                                    <TableCell>
                                        {variant.shopify_product?.status || "-"}
                                    </TableCell>

                                    {/* Actions */}
                                    <TableCell>
                                        <Button
                                            variant="contained"
                                            size="small"
                                            onClick={() => handleMapping(variant)}
                                        >
                                            {variant.shopify_product ? 'Re-map' : 'Map'}
                                        </Button>
                                    </TableCell>
                                </StyledTableRow>
                            ))
                        ) : (
                            <TableRow>
                                <TableCell colSpan={11} align="center">
                                    <Typography variant="body1" color="textSecondary">
                                        No variants found
                                    </Typography>
                                </TableCell>
                            </TableRow>
                        )}
                    </TableBody>
                </Table>
            </TableContainer>
        </Paper>
    );
};

export default VariantsTable;







