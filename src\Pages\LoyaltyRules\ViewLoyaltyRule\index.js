import {
    App<PERSON><PERSON>,
    Box,
    Button,
    Dialog,
    DialogActions,
    DialogContent,
    DialogTitle,
    Grid,
    Skeleton,
    styled,
    TextField,
    Typography,
    useTheme
} from "@mui/material";
import moment from "moment/moment";
import { useEffect, useState } from "react";
import Tabs from "@mui/material/Tabs";
import Tab from "@mui/material/Tab";
import * as React from 'react';
import useTokenRefresh from "../../../Hooks/useTokenRefresh";
import httpclient from "../../../Utils";
import PropTypes from "prop-types";
import TableComponent from "../../../Components/TableComponent";

const StyledHeaderTitle = styled(DialogTitle)(({ theme }) => ({
    background: theme.palette.primary.main,
    color: "#fff",
    position: "relative",
    "& button": {
        position: "absolute",
        right: "15px",
        top: "15px",
        color: "#fff",
    },
}));

const FlexContent = styled("div")(({ theme }) => ({
    display: "flex",
    flexDirection: "row",
    fontSize: "17px",
    marginBottom: "10px",
    alignItems: "flex-start",
}));

const FlexInnerTitle = styled("div")(({ theme }) => ({
    display: "flex",
    alignItems: "center",
    justifyContent: "space-between",
    minWidth: "270px",
    maxWidth: "270px",
    fontWeight: "600",
}));


const Values = styled("div")(({ theme }) => ({
    marginLeft: "15px",
    fontWeight: "500",
    color: theme.palette.primary.dark,
}));

const AppBarTabs = styled(AppBar)(({ theme }) => ({
    background: "#fff",
    color: theme.palette.primary.dark,
}));

function TabPanel(props) {
    const { children, value, index, ...other } = props;

    return (
        <div
            role="tabpanel"
            hidden={value !== index}
            id={`full-width-tabpanel-${index}`}
            aria-labelledby={`full-width-tab-${index}`}
            {...other}
        >
            {value === index && (
                <Box sx={{ p: 3 }}>
                    <Typography>{children}</Typography>
                </Box>
            )}
        </div>
    );
}

TabPanel.propTypes = {
    children: PropTypes.node,
    index: PropTypes.number.isRequired,
    value: PropTypes.number.isRequired,
};

function a11yProps(index) {
    return {
        id: `full-width-tab-${index}`,
        "aria-controls": `full-width-tabpanel-${index}`,
    };
}

const columns = [
    { id: "sn", name: "SN" },
    { id: "lite_card_member_id", name: "LiteCard Member ID" },
    { id: "name", name: "Name" },
    { id: "email", name: "Email" },

];

const ViewLoyaltyRule = (props) => {
    const [dialogDetails, setDialogDetails] = useState({
        open: true,
        success: false,
    });

    const handleClose = () => {
        setDialogDetails({
            ...dialogDetails,
            open: false,
        });
        props.handleCloseLoyaltyRule();
    };


    return (
        <div>
            <Dialog
                open={dialogDetails.open}
                onClose={handleClose}
                maxWidth="xl"
                fullWidth
                aria-labelledby="alert-dialog-title"
                aria-describedby="alert-dialog-description"
            >
                <StyledHeaderTitle id="alert-dialog-title">
                    {`View Loyalty Rule Detail (${props.viewDetails?.name || "-"})`}
                </StyledHeaderTitle>
                {props.singleLoading ? (
                    <DialogContent>
                        <Grid container spacing={2}>
                            <Grid item xs={12} md={6}>
                                <Box p={3} sx={{ width: "100%" }}>
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                </Box>
                            </Grid>
                            <Grid item xs={12} md={6}>
                                <Box p={3} sx={{ width: "100%" }}>
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                </Box>
                            </Grid>
                        </Grid>
                    </DialogContent>
                ) : (
                    <DialogContent sx={{ padding: '0' }}>
                        <Box p={3}>
                            <Grid container spacing={2}>
                                <Grid item xs={12} md={6}>
                                    <FlexContent>
                                        <FlexInnerTitle>
                                            <span>Title</span> <span> : </span>
                                        </FlexInnerTitle>
                                        <Values>{props.viewDetails.name || '-'}</Values>
                                    </FlexContent>


                                    <FlexContent>
                                        <FlexInnerTitle>
                                            <span>Start Date</span> <span> : </span>
                                        </FlexInnerTitle>
                                        <Values>{props.viewDetails.start_date ? moment(props.viewDetails.start_date).format('ddd, DD MMM YYYY, h:mm A') : '-'}</Values>
                                    </FlexContent>

                                    <FlexContent>
                                        <FlexInnerTitle>
                                            <span>End Date</span> <span> : </span>
                                        </FlexInnerTitle>
                                        <Values>{props.viewDetails.end_date ? moment(props.viewDetails.end_date).format('ddd, DD MMM YYYY, h:mm A') : '-'}</Values>
                                    </FlexContent>


                                    <FlexContent>
                                        <FlexInnerTitle>
                                            <span>Input Details</span> <span> : </span>
                                        </FlexInnerTitle>
                                        <Values>
                                            {props.viewDetails.inputDetails?.map((detail, idx) => (
                                                <div key={idx}>
                                                    {detail.name}: {detail.inputDetailValue.map(val => val.value).join(', ')}<br></br>
                                                    ({detail.condition})
                                                </div>
                                            )) || '-'}
                                        </Values>
                                    </FlexContent>

                                    <FlexContent>
                                        <FlexInnerTitle>
                                            <span>Output Details</span> <span> : </span>
                                        </FlexInnerTitle>
                                        {/* <Values>{props.viewDetails.outputDetails?.map((detail, idx) => (
                                            <div key={idx}>
                                                {detail.name} : {detail.outputDetailValue.map(val => val.value).join(', ')}<br></br>
                                                ({detail.condition})
                                            </div>
                                        )) || '-'}</Values> */}
                                        <Values>
                                            {props.viewDetails.outputDetails?.length > 0
                                                ? props.viewDetails.outputDetails.map((detail, idx) => (
                                                    <div key={idx}>
                                                        {detail.name} :{' '}
                                                        {detail.id === 12
                                                            ? detail.outputDetailValue
                                                                .map(val => {
                                                                    const categoryValues = val.value.categoryValue.join(', ');
                                                                    const productValues = val.value.productValue.join(', ');
                                                                    return `Category - ${categoryValues} : Product - ${productValues}`;
                                                                }).join(" | ")

                                                            : detail.outputDetailValue.map(val => val.value).join(', ')}
                                                        <br />
                                                        ({detail.condition})
                                                    </div>
                                                ))
                                                : '-'}


                                        </Values>

                                    </FlexContent>

                                    <FlexContent>
                                        <FlexInnerTitle>
                                            <span>Store List</span> <span> : </span>
                                        </FlexInnerTitle>
                                        <Values>{props.viewDetails.storeList.length > 0 ? props.viewDetails.storeList.map(val => val.value).join(', ') : '-'}</Values>
                                    </FlexContent>

                                    <FlexContent>
                                        <FlexInnerTitle>
                                            <span>Template</span> <span> : </span>
                                        </FlexInnerTitle>
                                        <Values>{props.viewDetails.template || '-'}</Values>
                                    </FlexContent>

                                </Grid>
                                <Grid item xs={12} md={6}>

                                    <FlexContent>
                                        <FlexInnerTitle>
                                            <span>Is Active</span> <span> : </span>
                                        </FlexInnerTitle>
                                        <Values>{props.viewDetails.is_active === 1 ? 'Yes' : 'No'}</Values>
                                    </FlexContent>

                                    <FlexContent>
                                        <FlexInnerTitle>
                                            <span>Customer Points Required</span> <span> : </span>
                                        </FlexInnerTitle>
                                        <Values>{props.viewDetails.customer_point_required === 1 ? 'Yes' : 'No'}</Values>
                                    </FlexContent>

                                    <FlexContent>
                                        <FlexInnerTitle>
                                            <span>Used One Time</span> <span> : </span>
                                        </FlexInnerTitle>
                                        <Values>{props.viewDetails.is_used_one_time === 1 ? 'Yes' : 'No'}</Values>
                                    </FlexContent>

                                    <FlexContent>
                                        <FlexInnerTitle>
                                            <span>Added By</span> <span> : </span>
                                        </FlexInnerTitle>
                                        <Values>{props.viewDetails.addedBy || '-'}</Values>
                                    </FlexContent>

                                    <FlexContent>
                                        <FlexInnerTitle>
                                            <span>Added Date</span> <span> : </span>
                                        </FlexInnerTitle>
                                        <Values>{moment(props.viewDetails.addedDate).format('ddd, DD MMM YYYY, h:mm A') || '-'}</Values>
                                    </FlexContent>

                                    <FlexContent>
                                        <FlexInnerTitle>
                                            <span>Updated By</span> <span> : </span>
                                        </FlexInnerTitle>
                                        <Values>{props.viewDetails.updatedBy || '-'}</Values>
                                    </FlexContent>

                                    <FlexContent>
                                        <FlexInnerTitle>
                                            <span>Updated Date</span> <span> : </span>
                                        </FlexInnerTitle>
                                        <Values>{moment(props.viewDetails.updatedDate).format('ddd, DD MMM YYYY, h:mm A') || '-'}</Values>
                                    </FlexContent>
                                </Grid>
                            </Grid>
                        </Box>
                    </DialogContent>
                )}
                <DialogActions styled={{ margin: "5px 10px" }}>
                    <Button onClick={handleClose} color="error" variant="contained" autoFocus>
                        Close
                    </Button>
                </DialogActions>
            </Dialog>

        </div>
    );
};

export default ViewLoyaltyRule;
