import React, { useEffect, useState, useRef } from "react";
import {
  <PERSON><PERSON><PERSON><PERSON>,
  Check,
  Clear,
  Close,
  Download,
  FilterList,
} from "@mui/icons-material";
import TableComponent from "../TableComponent";
import httpclient from "../../../Utils";
import {
  Box,
  Button,
  Card,
  Collapse,
  FormControl,
  Grid,
  InputLabel,
  MenuItem,
  Select,
  styled,
  TextField,
  Snackbar,
  Autocomplete,
  Checkbox,
} from "@mui/material";

import MuiAlert from "@mui/material/Alert";
// import StatusDialog from "../StatusDialog";
// import BackdropLoader from "../../../../Components/BackdropLoader";
import { useLocation, useNavigate } from "react-router";
import useTokenRefresh from "../../../Hooks/useTokenRefresh";
import Footer from "../../../Components/Footer";
import ViewReportDetail from "./ViewReportDetail";
import CheckBoxOutlineBlankIcon from '@mui/icons-material/CheckBoxOutlineBlank';
import CheckBoxIcon from '@mui/icons-material/CheckBox';

const icon = <CheckBoxOutlineBlankIcon fontSize="small" />;
const checkedIcon = <CheckBoxIcon fontSize="small" />;

//import { useLocation } from "react-router-dom";

const Alert = React.forwardRef(function Alert(props, ref) {
  return <MuiAlert elevation={6} ref={ref} variant="filled" {...props} />;
});



const columns = [
  //{ id: "checkColumn", name: " " },
  { id: "customer_id", name: "Customer ID" },
  { id: "first_name", name: "Customer Name" },
  { id: "email", name: "Email" },
  { id: "phone", name: "Phone" },
  { id: "noOfTransection", name: "No. of Transaction" },
  { id: "paidAmount", name: "Amount" },
  { id: "ls_created_at", name: "Last Transaction Date" },
];

const FilteredBox = styled(Box)(({ theme }) => ({
  background: "#f9f9f9",
  padding: "5px 10px",
  borderRadius: "5px",
  "& p": {
    margin: "3px 0",
    marginRight: "10px",
    display: "inline-block",
    background: "#dedede",
    borderRadius: "10px",
    padding: "2px 5px",
  },
  "& svg": {
    fontSize: "15px",
    cursor: "pointer",
    position: "relative",
    top: "3px",
    background: theme.palette.primary.dark,
    color: "#fff",
    borderRadius: "50%",
    padding: "2px",
    marginLeft: "2px",
  },
}));

const Header = styled("div")(({ theme }) => ({
  "& h1": {
    color: theme.palette.primary.dark,
    margin: "0",
  },
}));

const configRowPerPage = JSON.parse(localStorage.getItem("configRowPerPage"));

const CustomerReport = (props) => {

  const { getTokenRefreshed: refresh, open: tokenOpen, overlay: overlay, setOpen: setTokenOpen, message: tokenMessage, messageState: tokenMessageState } = useTokenRefresh();

  const location = useLocation();
  const navigate = useNavigate();
  const buttonRef = useRef(null);

  const [openViewDialog, setOpenViewDialog] = useState(false);
  const [viewDetails, setViewDetails] = useState({});
  const [openStatusDialog, setOpenStatusDialog] = useState(false);
  const [exceptionStatus, setExceptionStatus] = useState([]);
  const [rows, setRows] = useState([]);
  const [exportRows, setExportRows] = useState("");
  const [rowChecked, setRowChecked] = useState([]);
  const [company, setCompany] = useState([]);
  const [status, setStatus] = useState("");
  const [selected, setSelected] = useState([]);
  const [selected1, setSelected1] = useState([]);
  const [buttonLoader, setButtonLoader] = useState(false);
  const [backdropLoader, setBackdropLoader] = useState(false);
  const [loading, setLoading] = useState(false);
  const [singleLoading, setSingleLoading] = useState(false);
  const [direction, setDirection] = useState(false);
  const [currentColumn, setCurrentColumn] = useState("");
  const [page, setPage] = useState(1);
  const [from, setFrom] = useState(1);
  const [to, setTo] = useState(
    configRowPerPage && configRowPerPage
      ? configRowPerPage && configRowPerPage
      : 10
  );

  const [rowsPerPage, setRowsPerPage] = useState(
    configRowPerPage && configRowPerPage
      ? configRowPerPage && configRowPerPage
      : 10
  );
  const [total, setTotal] = useState("");
  const [totalPaid, setTotalPaid] = useState("");
  const [totalTransaction, setTotalTransaction] = useState("");
  const [filterOpen, setFilterOpen] = useState(false);
  const [newFilter, setNewFilter] = useState(false);
  const [open, setOpen] = useState(false);
  const [message, setMessage] = useState("");
  const [messageState, setMessageState] = useState("");
  const [companyList, setCompanyList] = useState([]);
  const [sites, setSites] = useState([]);
  const [siteNames, setSiteNames] = useState([]);
  const [filterBy, setFilterBy] = useState([]);
  const [numItems, setNumItems] = useState([]);
  const [ordering, setOrdering] = useState([]);
  const [filterData, setFilterData] = useState({

    id: "",
    customer_id: "",
    status: "",
    name: "",
    filterBy: "",
    filterByName: "",
    numItems: "",
    numItemsName: "",
    ordering: "",
    orderingName: "",
    startDate: "",
    endDate: "",
    remove: false,
  });

  const today = new Date();
  const endDate = (today.toISOString().split('T')[0]);

  const startDateFormatting = new Date(today);
  startDateFormatting.setMonth(today.getMonth() - 1);
  const startDate = startDateFormatting.toISOString().split('T')[0];

  let val = filterData.filterBy === "" ? 1 : filterData.filterBy;
  let val1 = filterData.numItems === "" ? 1 : filterData.numItems;
  let val2 = filterData.ordering === "" ? 1 : filterData.ordering;
  let val3 = filterData.startDate === "" ? startDate : filterData.startDate;
  let val4 = filterData.endDate === "" ? endDate : filterData.endDate;

  const [submittedData, setSubmittedData] = useState({

    id: "",
    customer_id: "",
    status: "",
    name: "",
    filterBy: "",
    filterByName: "",
    numItems: "",
    numItemsName: "",
    ordering: "",
    orderingName: "",
    startDate: "",
    endDate: "",

    submit: false,
  });

  useEffect(() => {
    if (

      filterData.id === "" ||
      filterData.customer_id === "" ||
      filterData.status === "" ||
      filterData.name === "" ||
      filterData.filterBy === "" ||
      filterData.filterByName === "" ||
      filterData.numItems === "" ||
      filterData.numItemsName === "" ||
      filterData.ordering === "" ||
      filterData.orderingName === "" ||
      filterData.startDate === "" ||
      filterData.endDate === ""

    ) {
      setSubmittedData({
        ...submittedData,
        submit: false,
      });
    }

    if (filterData.id === " ") filterData.id = "";
    if (filterData.customer_id === " ") filterData.customer_id = "";
    if (filterData.status === " ") filterData.status = "";
    if (filterData.name === " ") filterData.name = "";
    if (filterData.filterBy === " ") filterData.filterBy = "";
    if (filterData.numItems === " ") filterData.numItems = "";
    if (filterData.ordering === " ") filterData.ordering = "";
    if (filterData.filterByName === " ") filterData.filterByName = "";
    if (filterData.numItemsName === " ") filterData.numItemsName = "";
    if (filterData.orderingName === " ") filterData.orderingName = "";
    if (filterData.startDate === " ") filterData.startDate = "";
    if (filterData.endDate === " ") filterData.endDate = "";

    filterData.remove === true && handleFilter();
  }, [filterData]);

  useEffect(() => {
    let currentpolicy = JSON.parse(localStorage.getItem("customerreport_filter"));
    currentpolicy !== null && setFilterData(currentpolicy);
    currentpolicy == null
      ?
      //getCustomerReport()
      setNewFilter(true)
      :
      currentpolicy.id == "" &&
        currentpolicy.customer_id == "" &&
        currentpolicy.status == "" &&
        currentpolicy.name == "" &&
        currentpolicy.filterBy == "" &&
        currentpolicy.numItems == "" &&
        currentpolicy.ordering == "" &&
        currentpolicy.filterByName == "" &&
        currentpolicy.numItemsName == "" &&
        currentpolicy.orderingName == "" &&
        currentpolicy.startDate == "" &&
        currentpolicy.endDate == ""

        //currentpolicy.removed == false
        ?
        //getCustomerReport()
        setNewFilter(true)
        :
        console.log("no data");
  }, []);

  useEffect(() => {
    if (newFilter === true) {
      if (val !== "") {
        filterData.filterBy = val
        filterData.filterByName = "Transaction"
      }
      if (val1 !== "") {
        filterData.numItems = val1
        filterData.numItemsName = "10"
      }
      if (val2 !== "") {
        filterData.ordering = val2
        filterData.orderingName = "Top"
      }
      if (val3 !== "") {
        filterData.startDate = val3
      }
      if (val4 !== "") {
        filterData.endDate = val4
      }
      setTimeout(() => {
        handleFilter();
        navigate("#", { replace: true });
      }, 1500);
    }
  }, [newFilter]);

  const getCustomerReport = () => {
    setLoading(true);
    httpclient
      .get(`${props.request_name}&pagination=${rowsPerPage}&page=${page}`)
      .then(({ data }) => {
        if (data.status === 200) {
          setRows(data.data);
          setTotalPaid(data.totalPaidAmount);
          setTotalTransaction(data.totalTransaction);
          setSites(data.sites);
          setFilterBy(data.filterBy);
          setNumItems(data.numItems);
          setOrdering(data.ordering);
          setTotal(data.meta.total);
          setRowsPerPage(parseInt(data.meta.per_page));
          setPage(data.meta.current_page);
          setFrom(data.meta.from);
          setTo(data.meta.to);
          setLoading(false);
        } else {
          setOpen(true);
          setMessage(data.message);
          setMessageState("error");
          setLoading(false);
        }

      }).catch((err) => {
        if (err.response.status === 401) {
          refresh();
          setOpen(tokenOpen);
          setMessage(tokenMessage);
          setMessageState("error");
        } else if (err.response.status === 422) {
          const errorMessages = Object.values(err.response.data.errors).flat();
          setOpen(true);
          setMessage(errorMessages);
          setMessageState("error");
          setLoading(false);
        } else if (err.response.status === 400) {
          const errorMessages = Object.values(err.response.data.errors).flat();
          setOpen(true);
          setMessage(errorMessages);
          setMessageState("error");
          setLoading(false);

        } else {
          setOpen(true);
          setMessage(err.response.data.message);
          setMessageState("error");
          setLoading(false);
        }
      })
  };


  const handleView = (row) => {
    if (row.customer_id === "" || row === "") {
      return
    } else {
      setSingleLoading(true);
      setOpenViewDialog(true);
      httpclient
        .get(`request-response?requestName=lightspeed/customers/${row.customer_id || row}`)
        .then(({ data }) => {
          if (data) {
            setViewDetails(data.data);
            setSingleLoading(false);
          }
          else {
            setOpen(true);
            setMessage(data.message);
            setMessageState("error");
            setLoading(false);
          }

        }).catch((err) => {
          if (err.response.status === 401) {
            refresh();
            setOpen(tokenOpen);
            setMessage(tokenMessage);
            setMessageState("error");
          } else if (err.response.status === 422) {
            const errorMessages = Object.values(err.response.data.errors).flat();
            setOpen(true);
            setMessage(errorMessages);
            setMessageState("error");
            setLoading(false);
          } else if (err.response.status === 400) {
            const errorMessages = Object.values(err.response.data.errors).flat();
            setOpen(true);
            setMessage(errorMessages);
            setMessageState("error");
            setLoading(false);

          } else {
            setOpen(true);
            setMessage(err.response.data.message);
            setMessageState("error");
            setLoading(false);
          }
        })
    }
  };

  const sendDetails = (callback) => {
    if (callback.open === false) {
      setOpenViewDialog(false);
      setViewDetails({});
    }
    if (callback.refetch === true) {
      handleView(callback.customer_id);
      setTimeout(() => {
        handleFilter();
      }, 1000);
    }
  };

  const handleChange = (e) => {
    const { name, value } = e.target;

    if (name === "id") {
      setFilterData({
        ...filterData,
        name: value,
        remove: false,
      });
    }
    if (name === "filterBy") {
      setFilterData({
        ...filterData,
        filterByName: value,
        remove: false,
      });
    }
    if (name === "numItems") {
      setFilterData({
        ...filterData,
        numItemsName: value,
        remove: false,
      });
    }
    if (name === "ordering") {
      setFilterData({
        ...filterData,
        orderingName: value,
        remove: false,
      });
    }

  };

  // const handleChangeSites= (value) => {
  //   setFilterData({
  //     ...filterData,
  //     id: value !== null ? value.id : "",
  //     name: value !== null ? value.name : "",
  //     remove: false,
  //   });
  // };
  const handleChangeSites = (value) => {
    setSiteNames(value);
    const ids = value.map((item) => item.id);
    const names = value.map((item) => item.name).join(", ");

    setFilterData({
      ...filterData,
      id: ids,
      name: names,
      remove: false,
    });
  };

  const handleFilter = () => {
    setNewFilter(false);
    setSubmittedData({
      ...submittedData,

      id: filterData.id,
      customer_id: filterData.customer_id,
      status: filterData.status,
      name: filterData.name,
      filterBy: filterData.filterBy,
      numItems: filterData.numItems,
      ordering: filterData.ordering,
      filterByName: filterData.filterByName,
      numItemsName: filterData.numItemsName,
      orderingName: filterData.orderingName,
      startDate: filterData.startDate,
      endDate: filterData.endDate,
      submit: true,
    });

    filterData.remove = true;

    localStorage.setItem("customerreport_filter", JSON.stringify(filterData));


    setLoading(true);
    if (

      filterData.id ||
      filterData.customer_id ||
      filterData.status ||
      filterData.name ||
      filterData.filterBy ||
      filterData.numItems ||
      filterData.ordering ||
      filterData.filterByName ||
      filterData.numItemsName ||
      filterData.orderingName ||
      filterData.startDate ||
      filterData.endDate

    ) {
      const idParams = filterData.id !== "" ? filterData.id
        .map((id, index) => `filters[site_id][$in][${index}]=${id}`)
        .join("&") : `filters[site_id][$in][0]=`;
      httpclient
        .get(
          `${props.request_name}&${idParams
          }&filters[customer_id][$eq]=${filterData.customer_id
          }&filterBy=${filterData.filterBy
          }&numItems=${filterData.numItems
          }&ordering=${filterData.ordering
          // }&filters[status][$eq]=${filterData.status
          // }&filters[name][$contains]=${filterData.name
          }&filters[ls_created_at][$between][0]=${filterData.startDate
          }&filters[ls_created_at][$between][1]=${filterData.endDate
          }&pagination=${rowsPerPage}&page=${1}`
        )
        .then(({ data }) => {
          if (data.status === 200) {
            setRows(data.data);
            setTotalPaid(data.totalPaidAmount);
            setTotalTransaction(data.totalTransaction);
            setSites(data.sites);
            setFilterBy(data.filterBy);
            setNumItems(data.numItems);
            setOrdering(data.ordering);
            setTotal(data.meta.total);
            setRowsPerPage(data.meta.per_page);
            setPage(data.meta.current_page);
            setFrom(data.meta.from);
            setTo(data.meta.to);
            setLoading(false);
          }
          else {
            setOpen(true);
            setMessage(data.message);
            setMessageState("error");
            setLoading(false);
          }

        }).catch((err) => {
          if (err.response.status === 401) {
            refresh();
            setOpen(tokenOpen);
            setMessage(tokenMessage);
            setMessageState("error");
          } else if (err.response.status === 422) {
            const errorMessages = Object.values(err.response.data.errors).flat();
            setOpen(true);
            setMessage(errorMessages);
            setMessageState("error");
            setLoading(false);
          } else if (err.response.status === 400) {
            const errorMessages = Object.values(err.response.data.errors).flat();
            setOpen(true);
            setMessage(errorMessages);
            setMessageState("error");
            setLoading(false);

          } else {
            setOpen(true);
            setMessage(err.response.data.message);
            setMessageState("error");
            setLoading(false);
          }
        });
    } else {
      getCustomerReport();
    }
  };



  const hadleFilterOpen = () => {
    setFilterOpen((prev) => !prev);
  };

  const handleChangeFilter = (e) => {
    const { name, value } = e.target;
    if (name === "filterBy") {
      const selectedItem = filterBy?.find((fil) => fil.id === value);
      filterData.filterBy = value;
      filterData.filterByName = selectedItem.name;
    }
    if (name === "numItems") {
      const selectedItem = numItems?.find((fil) => fil.id === value);
      filterData.numItems = value;
      filterData.numItemsName = selectedItem.name;
    }
    if (name === "ordering") {
      const selectedItem = ordering?.find((fil) => fil.id === value);
      filterData.ordering = value;
      filterData.orderingName = selectedItem.name;
    }

    setFilterData({
      ...filterData,
      [name]: value,
      remove: false,
    });
  };

  const handleRemove = (data) => {
    if (data === "id") {
      filterData.name = "";
      submittedData.name = "";
      setSiteNames([]);
    }

    if (data === "filterBy") {
      filterData.filterByName = "";
      submittedData.filterByName = "";
    }
    if (data === "numItems") {
      filterData.numItemsName = "";
      submittedData.numItemsName = "";
    }
    if (data === "ordering") {
      filterData.orderingName = "";
      submittedData.orderingName = "";
    }

    if (data === "startDate") {
      setFilterData({
        ...filterData,
        startDate: "",
        endDate: "",
        remove: true,
      });
      setSubmittedData({
        ...submittedData,
        startDate: "",
        endDate: "",
      });
    } else {
      setFilterData({
        ...filterData,
        [data]: "",
        remove: true,
      });
      setSubmittedData({
        ...submittedData,
        [data]: "",
      });
    }
  };

  const handleSort = (column) => {
    setDirection((prevDirection) => !prevDirection);
    setCurrentColumn(column);
    setLoading(true);
    const idParams = filterData.id !== "" ? filterData.id
      .map((id, index) => `filters[site_id][$in][${index}]=${id}`)
      .join("&") : `filters[site_id][$in][0]=`;
    submittedData.submit
      ?
      httpclient
        .get(
          `${props.request_name}&${idParams
          }&filters[customer_id][$eq]=${filterData.customer_id
          }&filterBy=${filterData.filterBy
          }&numItems=${filterData.numItems
          }&ordering=${filterData.ordering
          // }&filters[status][$eq]=${filterData.status
          // }&filters[name][$contains]=${filterData.name
          }&filters[ls_created_at][$between][0]=${filterData.startDate
          }&filters[ls_created_at][$between][1]=${filterData.endDate
          }&sort[0]=${column}:${!direction ? "asc" : "desc"
          }&pagination=${rowsPerPage}&page=${page}`
        )
        .then(({ data }) => {
          if (data.status === 200) {
            setRows(data.data);
            setTotal(data.meta.total);
            setRowsPerPage(parseInt(data.meta.per_page));
            setPage(data.meta.current_page);
            setFrom(data.meta.from);
            setTo(data.meta.to);
            setLoading(false);
          }
          else {
            setOpen(true);
            setMessage(data.message);
            setMessageState("error");
            setLoading(false);
          }

        }).catch((err) => {
          if (err.response.status === 401) {
            refresh();
            setOpen(tokenOpen);
            setMessage(tokenMessage);
            setMessageState("error");
          } else if (err.response.status === 422) {
            const errorMessages = Object.values(err.response.data.errors).flat();
            setOpen(true);
            setMessage(errorMessages);
            setMessageState("error");
            setLoading(false);
          } else if (err.response.status === 400) {
            const errorMessages = Object.values(err.response.data.errors).flat();
            setOpen(true);
            setMessage(errorMessages);
            setMessageState("error");
            setLoading(false);

          } else {
            setOpen(true);
            setMessage(err.response.data.message);
            setMessageState("error");
            setLoading(false);
          }
        })

      : httpclient
        .get(
          `${props.request_name}&sort[0]=${column}:${!direction ? "asc" : "desc"
          }&pagination=${rowsPerPage}&page=${page}`
        )
        .then(({ data }) => {
          if (data.status === 200) {
            setRows(data.data);
            setLoading(false);
          }
          else {
            setOpen(true);
            setMessage(data.message);
            setMessageState("error");
            setLoading(false);
          }

        }).catch((err) => {
          if (err.response.status === 401) {
            refresh();
            setOpen(tokenOpen);
            setMessage(tokenMessage);
            setMessageState("error");
          } else if (err.response.status === 422) {
            const errorMessages = Object.values(err.response.data.errors).flat();
            setOpen(true);
            setMessage(errorMessages);
            setMessageState("error");
            setLoading(false);
          } else if (err.response.status === 400) {
            const errorMessages = Object.values(err.response.data.errors).flat();
            setOpen(true);
            setMessage(errorMessages);
            setMessageState("error");
            setLoading(false);

          } else {
            setOpen(true);
            setMessage(err.response.data.message);
            setMessageState("error");
            setLoading(false);
          }

        });
  };

  const handleChangePage = (e, page) => {
    setLoading(true);
    const idParams = filterData.id !== "" ? filterData.id
      .map((id, index) => `filters[site_id][$in][${index}]=${id}`)
      .join("&") : `filters[site_id][$in][0]=`;
    submittedData.submit
      ?
      httpclient
        .get(
          `${props.request_name}&${idParams
          }&filters[customer_id][$eq]=${filterData.customer_id
          }&filterBy=${filterData.filterBy
          }&numItems=${filterData.numItems
          }&ordering=${filterData.ordering
          // }&filters[status][$eq]=${filterData.status
          // }&filters[name][$contains]=${filterData.name
          }&filters[ls_created_at][$between][0]=${filterData.startDate
          }&filters[ls_created_at][$between][1]=${filterData.endDate
          }${currentColumn ? `&sort[0]=${currentColumn}:${direction ? "asc" : "desc"}` : ''
          }&pagination=${rowsPerPage}&page=${page}`
        )
        .then(({ data }) => {
          if (data.status === 200) {
            setRows(data.data);
            setTotal(data.meta.total);
            setRowsPerPage(parseInt(data.meta.per_page));
            setPage(data.meta.current_page);
            setFrom(data.meta.from);
            setTo(data.meta.to);
            setLoading(false);
          }
          else {
            setOpen(true);
            setMessage(data.message);
            setMessageState("error");
            setLoading(false);
          }

        }).catch((err) => {
          if (err.response.status === 401) {
            refresh();
            setOpen(tokenOpen);
            setMessage(tokenMessage);
            setMessageState("error");
          } else if (err.response.status === 422) {
            const errorMessages = Object.values(err.response.data.errors).flat();
            setOpen(true);
            setMessage(errorMessages);
            setMessageState("error");
            setLoading(false);
          } else if (err.response.status === 400) {
            const errorMessages = Object.values(err.response.data.errors).flat();
            setOpen(true);
            setMessage(errorMessages);
            setMessageState("error");
            setLoading(false);

          } else {
            setOpen(true);
            setMessage(err.response.data.message);
            setMessageState("error");
            setLoading(false);
          }

        })
      : httpclient
        .get(
          `${props.request_name
          }${currentColumn ? `&sort[0]=${currentColumn}:${direction ? "asc" : "desc"}` : ''}&pagination=${rowsPerPage}&page=${page}`
        )
        .then(({ data }) => {
          if (data.status === 200) {
            setRows(data.data);
            setTotal(data.meta.total);
            setRowsPerPage(parseInt(data.meta.per_page));
            setPage(data.meta.current_page);
            setFrom(data.meta.from);
            setTo(data.meta.to);
            setLoading(false);
          }
          else {
            setOpen(true);
            setMessage(data.message);
            setMessageState("error");
            setLoading(false);
          }

        }).catch((err) => {
          if (err.response.status === 401) {
            refresh();
            setOpen(tokenOpen);
            setMessage(tokenMessage);
            setMessageState("error");
          } else if (err.response.status === 422) {
            const errorMessages = Object.values(err.response.data.errors).flat();
            setOpen(true);
            setMessage(errorMessages);
            setMessageState("error");
            setLoading(false);
          } else if (err.response.status === 400) {
            const errorMessages = Object.values(err.response.data.errors).flat();
            setOpen(true);
            setMessage(errorMessages);
            setMessageState("error");
            setLoading(false);

          } else {
            setOpen(true);
            setMessage(err.response.data.message);
            setMessageState("error");
            setLoading(false);
          }

        });
  };

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(+event.target.value);
    setLoading(true);

    localStorage.setItem("configRowPerPage", event.target.value);
    const idParams = filterData.id !== "" ? filterData.id
      .map((id, index) => `filters[site_id][$in][${index}]=${id}`)
      .join("&") : `filters[site_id][$in][0]=`;
    submittedData.submit
      ? httpclient
        .get(
          `${props.request_name}&${idParams
          }&filters[customer_id][$eq]=${filterData.customer_id
          }&filterBy=${filterData.filterBy
          }&numItems=${filterData.numItems
          }&ordering=${filterData.ordering
          // }&filters[status][$eq]=${filterData.status
          // }&filters[name][$contains]=${filterData.name
          }&filters[ls_created_at][$between][0]=${filterData.startDate
          }&filters[ls_created_at][$between][1]=${filterData.endDate
          }${currentColumn ? `&sort[0]=${currentColumn}:${direction ? "asc" : "desc"}` : ''
          }&pagination=${+event.target.value}&page=${page}`
        )
        .then(({ data }) => {
          if (data.status === 200) {
            setRows(data.data);
            setTotal(data.meta.total);
            setRowsPerPage(parseInt(data.meta.per_page));
            setPage(data.meta.current_page);
            setFrom(data.meta.from);
            setTo(data.meta.to);
            setLoading(false);
          }
          else {
            setOpen(true);
            setMessage(data.message);
            setMessageState("error");
            setLoading(false);
          }

        }).catch((err) => {
          if (err.response.status === 401) {
            refresh();
            setOpen(tokenOpen);
            setMessage(tokenMessage);
            setMessageState("error");
          } else if (err.response.status === 422) {
            const errorMessages = Object.values(err.response.data.errors).flat();
            setOpen(true);
            setMessage(errorMessages);
            setMessageState("error");
            setLoading(false);
          } else if (err.response.status === 400) {
            const errorMessages = Object.values(err.response.data.errors).flat();
            setOpen(true);
            setMessage(errorMessages);
            setMessageState("error");
            setLoading(false);

          } else {
            setOpen(true);
            setMessage(err.response.data.message);
            setMessageState("error");
            setLoading(false);
          }
        })
      : httpclient
        .get(
          `${props.request_name
          }${currentColumn ? `&sort[0]=${currentColumn}:${direction ? "asc" : "desc"}` : ''}&pagination=${+event.target.value}&page=${1}`
        )
        .then(({ data }) => {
          if (data.status === 200) {
            setRows(data.data);
            setTotal(data.meta.total);
            setRowsPerPage(parseInt(data.meta.per_page));
            setFrom(data.meta.from);
            setTo(data.meta.to);
            setPage(data.meta.current_page);
            setLoading(false);
          }
          else {
            setOpen(true);
            setMessage(data.message);
            setMessageState("error");
            setLoading(false);
          }

        }).catch((err) => {
          if (err.response.status === 401) {
            refresh();
            setOpen(tokenOpen);
            setMessage(tokenMessage);
            setMessageState("error");
          } else if (err.response.status === 422) {
            const errorMessages = Object.values(err.response.data.errors).flat();
            setOpen(true);
            setMessage(errorMessages);
            setMessageState("error");
            setLoading(false);
          } else if (err.response.status === 400) {
            const errorMessages = Object.values(err.response.data.errors).flat();
            setOpen(true);
            setMessage(errorMessages);
            setMessageState("error");
            setLoading(false);

          } else {
            setOpen(true);
            setMessage(err.response.data.message);
            setMessageState("error");
            setLoading(false);
          }
        })
  };

  const handleClose = (event, reason) => {
    if (reason === "clickaway") {
      return;
    }
    setOpen(false);
    setTokenOpen(false);
  };

  return (
    <div>
      <Grid container spacing={2}>
        <Grid item md={8} xs={12}>
          <Header>
            <h1>List Customer Report</h1>
          </Header>
        </Grid>
        <Grid
          item
          md={4}
          xs={12}
          display="flex"
          alignItems="center"
          justifyContent="flex-end"
        >
          <Button color="primary" variant="contained" onClick={hadleFilterOpen}>
            Filter <FilterList style={{ marginLeft: "5px" }} fontSize="small" />
          </Button>

        </Grid>
        <Grid
          item
          md={12}
          xs={12}
          display="flex"
          flexDirection="column" // Stack items vertically
          alignItems="flex-end"  // Align items to the right
        >
          {totalTransaction && <div><strong>Total Transactions :</strong> {totalTransaction}</div>}
          {totalPaid && <div style={{ marginTop: "5px" }}><strong>Total Paid Amount :</strong> ${totalPaid}</div>}
        </Grid>


        {/* Filter */}
        <Grid item xs={12}>
          <Collapse in={filterOpen}>
            <Card>
              <Box p={4}>
                <Grid container spacing={2}>

                  <Grid item xs={12} md={4}>
                    <InputLabel>Sites</InputLabel>
                    {/* <TextField
                      name="id"
                      value={filterData.id}
                      onChange={handleChangeFilter}
                      onKeyDown={e => { if (e.key === "Enter") handleFilter() }}
                      variant="outlined"
                      fullWidth
                    /> */}
                    {/* <Autocomplete
                      disablePortal
                      id="site_id"
                      options={sites}
                      onChange={(event, newValue) => {
                        handleChangeSites(newValue);
                      }}
                      inputValue={filterData.name}
                      getOptionLabel={(option) => option.name}
                      renderInput={(params) => (
                        <TextField
                          {...params}
                          onChange={handleChange}
                          onKeyDown={(e) => {
                            if (e.key === "Enter") handleFilter();
                          }}
                          value={filterData.name}
                          variant="outlined"
                          name="id"
                          // label="Company"
                        />
                      )}
                    /> */}
                    <Autocomplete
                      options={sites}
                      onChange={(event, newValue) => {
                        handleChangeSites(newValue);
                      }}
                      multiple
                      id="checkboxes-tags-demo"
                      disableCloseOnSelect
                      value={siteNames}
                      getOptionLabel={(option) => option.name}
                      renderOption={(props, option, { selected }) => (
                        <li {...props}>
                          <Checkbox
                            icon={icon}
                            checkedIcon={checkedIcon}
                            style={{ marginRight: 8 }}
                            checked={selected}
                          />
                          {option.name}
                        </li>
                      )}
                      renderInput={(params) => (
                        <TextField
                          {...params}
                          onChange={handleChange}
                          onKeyDown={(e) => {
                            if (e.key === "Enter") handleFilter();
                          }}
                          value={filterData.name}
                          variant="outlined"
                          name="id"
                        // label="Company"
                        />
                      )}
                    />

                  </Grid>
                  {/* <Grid item xs={12} md={4}>
                    <InputLabel>Customer ID</InputLabel>
                    <TextField
                      name="customer_id"
                      value={filterData.customer_id}
                      onChange={handleChangeFilter}
                      onKeyDown={e => { if (e.key === "Enter") handleFilter() }}
                      variant="outlined"
                      fullWidth
                    />
                  </Grid> */}

                  <Grid item xs={12} md={4}>
                    <InputLabel>Top Customer By</InputLabel>
                    <FormControl fullWidth>
                      <Select
                        value={filterData.filterBy}
                        name="filterBy"
                        onChange={handleChangeFilter}
                      >
                        <MenuItem value={""}>Select</MenuItem>
                        {filterBy?.map((fil) => (
                          <MenuItem value={fil.id}>{fil.name}</MenuItem>
                        ))}
                      </Select>
                    </FormControl>

                  </Grid>
                  <Grid item xs={12} md={4}>
                    <InputLabel>Sort By</InputLabel>
                    <FormControl fullWidth>
                      <Select
                        value={filterData.ordering}
                        name="ordering"
                        onChange={handleChangeFilter}
                      >
                        <MenuItem value={""}>Select</MenuItem>
                        {ordering?.map((ord) => (
                          <MenuItem value={ord.id}>{ord.name}</MenuItem>
                        ))}
                      </Select>
                    </FormControl>

                  </Grid>
                  <Grid item xs={12} md={4}>
                    <InputLabel>List Data</InputLabel>
                    <FormControl fullWidth>
                      <Select
                        value={filterData.numItems}
                        name="numItems"
                        onChange={handleChangeFilter}
                      >
                        <MenuItem value={""}>Select</MenuItem>
                        {numItems?.map((num) => (
                          <MenuItem value={num.id}>{num.name}</MenuItem>
                        ))}
                      </Select>
                    </FormControl>

                  </Grid>
                  {/* <Grid item xs={12} md={4}>
                    <InputLabel>Order Name</InputLabel>
                    <TextField
                      name="name"
                      value={filterData.name}
                      onChange={handleChangeFilter}
                      onKeyDown={e => { if (e.key === "Enter") handleFilter() }}
                      variant="outlined"
                      fullWidth
                    />
                  </Grid> */}
                  <Grid item xs={12} md={4}>
                    <InputLabel>Start Date</InputLabel>
                    <TextField
                      variant="outlined"
                      name="startDate"
                      type="date"
                      value={filterData.startDate}
                      onChange={(e) => handleChangeFilter(e)}
                      fullWidth
                      InputLabelProps={{
                        shrink: true,
                      }}
                    />
                  </Grid>

                  <Grid item xs={12} md={4}>
                    <InputLabel>End Date</InputLabel>
                    <TextField
                      variant="outlined"
                      name="endDate"
                      type="date"
                      value={filterData.endDate}
                      onChange={(e) => handleChangeFilter(e)}
                      fullWidth
                      InputLabelProps={{
                        shrink: true,
                      }}
                    />
                  </Grid>


                  <Grid item xs={12}>
                    <Box textAlign={"right"}>
                      <Button
                        variant="contained"
                        color="primary"
                        onClick={handleFilter}
                      >
                        Filter{" "}
                        <ArrowForward
                          fontSize="small"
                          style={{ marginLeft: "5px" }}
                        />
                      </Button>
                    </Box>
                  </Grid>
                </Grid>
              </Box>
            </Card>
          </Collapse>
        </Grid>

        {
          submittedData.id ||
            submittedData.customer_id ||
            submittedData.status ||
            submittedData.filterBy ||
            submittedData.numItems ||
            submittedData.ordering ||
            submittedData.startDate ||
            submittedData.endDate
            ? (
              <Grid item xs={12}>
                <FilteredBox>
                  <span>Filtered: </span>


                  {submittedData.id && (
                    <p>
                      <span>Sites: {submittedData.name}</span>
                      <Close
                        fontSize="small"
                        onClick={() => handleRemove("id")}
                      />
                    </p>
                  )}
                  {submittedData.customer_id && (
                    <p>
                      <span>Customer ID: {submittedData.customer_id}</span>
                      <Close
                        fontSize="small"
                        onClick={() => handleRemove("customer_id")}
                      />
                    </p>
                  )}
                  {submittedData.filterBy && (
                    <p>
                      <span>Top Customer By: {submittedData.filterByName}</span>
                      <Close
                        fontSize="small"
                        onClick={() => handleRemove("filterBy")}
                      />
                    </p>
                  )}
                  {submittedData.numItems && (
                    <p>
                      <span>List Data: {submittedData.numItemsName}</span>
                      <Close
                        fontSize="small"
                        onClick={() => handleRemove("numItems")}
                      />
                    </p>
                  )}
                  {submittedData.ordering && (
                    <p>
                      <span>Sort By: {submittedData.orderingName}</span>
                      <Close
                        fontSize="small"
                        onClick={() => handleRemove("ordering")}
                      />
                    </p>
                  )}
                  {(submittedData.startDate || submittedData.endDate) && (
                    <p>
                      <span>
                        Transaction Date Range: {submittedData.startDate} -{" "}
                        {submittedData.endDate}
                      </span>
                      <Close
                        fontSize="small"
                        onClick={() => handleRemove("startDate")}
                      />
                    </p>
                  )}

                </FilteredBox>
              </Grid>
            ) : (
              <Box></Box>
            )}
        {/* Filter */}



        <Grid item xs={12}>
          <TableComponent
            columns={columns}
            rows={rows}
            sort={true}
            handleView={handleView}
            handleSort={handleSort}
            loading={loading}
            handleChangeRowsPerPage={handleChangeRowsPerPage}
            handleChangePage={handleChangePage}
            // handleRowCheck={handleRowCheck}
            rowChecked={rowChecked}
            buttonLoader={buttonLoader}
            direction={direction}
            currentColumn={currentColumn}
            page={page}
            total={total && total}
            fromTable={from}
            toTable={to}
            rowsPerPage={rowsPerPage}
            filterData={filterData}
          />
        </Grid>
        <Footer overlay={overlay || props.overlayNew} />
      </Grid>

      {openViewDialog && (
        <ViewReportDetail
          singleLoading={singleLoading}
          viewDetails={viewDetails}
          sendDetails={sendDetails}
        />
      )}

      {/* {backdropLoader && <BackdropLoader />} */}

      <Snackbar
        autoHideDuration={3000}
        anchorOrigin={{ vertical: "top", horizontal: "right" }}
        open={open || tokenOpen}
        onClose={handleClose}
      >
        <Alert
          onClose={handleClose}
          severity={messageState || tokenMessageState}
          sx={{ width: "100%" }}
        >
          {message || tokenMessage}
        </Alert>
      </Snackbar>
    </div>
  );
};

export default CustomerReport;
