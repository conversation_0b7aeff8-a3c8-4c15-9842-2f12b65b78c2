import {
    <PERSON>,
    Button,
    Dialog,
    DialogActions,
    DialogContent,
    DialogTitle,
    Grid,
    styled,
    TextField,
    Typography,
} from "@mui/material";
import { useEffect, useState } from "react";
import * as React from 'react';


const StyledHeaderTitle = styled(DialogTitle)(({ theme }) => ({
    background: theme.palette.primary.main,
    color: "#fff",
    position: "relative",
    "& button": {
        position: "absolute",
        right: "15px",
        top: "15px",
        color: "#fff",
    },
}));

const ViewProjectList = (props) => {
    const [dialogDetails, setDialogDetails] = useState({
        open: true,
        success: false,
    });

    const handleClose = () => {
        setDialogDetails({
            ...dialogDetails,
            open: false,
        });
        props.handleCloseProjectList();
    };


    return (
        <div>
            <Dialog
                open={dialogDetails.open}
                onClose={handleClose}
                maxWidth="sm"
                fullWidth
                aria-labelledby="alert-dialog-title"
                aria-describedby="alert-dialog-description"
            >
                <StyledHeaderTitle id="alert-dialog-title">
                    Project List
                </StyledHeaderTitle>
                <DialogContent>
                    <Box
                        sx={{
                            position: "relative",
                            top: "10px",
                            right: "10px",
                            display: "flex",
                            flexDirection: "column",
                            alignItems: "end",
                            backgroundColor: "white",
                            padding: "5px 10px 10px 10px",
                            borderRadius: "4px",
                        }}
                    >
                        {/* <div style={{ display: 'flex', justifyContent: "space-between" }}>
                            <Box
                                sx={{
                                    width: "12px",
                                    height: "12px",
                                    backgroundColor: "green",
                                    border: "1px solid black",
                                    margin: "5px 5px",
                                    borderRadius: "50%",
                                }}
                            />
                            <Typography variant="body1">Active</Typography>
                        </div>
                        <div style={{ display: 'flex', justifyContent: "space-between" }}>
                            <Box
                                sx={{
                                    width: "12px",
                                    height: "12px",
                                    backgroundColor: "red",
                                    border: "1px solid black",
                                    margin: "5px 5px",
                                    borderRadius: "50%",
                                }}
                            />
                            <Typography variant="body1">Inactive</Typography></div> */}


                    </Box>

                    <Box p={3}>
                        {props.projectList?.map((menuItem, index) => (
                            <Box key={menuItem.project_id} sx={{ marginBottom: "20px" }}>

                                <Typography
                                    variant="h6"
                                    sx={{
                                        fontWeight: "500",
                                        fontSize: "18px",
                                        display: "inline-flex",
                                        alignItems: "center",
                                    }}
                                >
                                    {index + 1}. {menuItem.project_name}
                                    {/* <Box
                                        sx={{
                                            width: "10px",
                                            height: "10px",
                                            borderRadius: "50%",
                                            backgroundColor: menuItem.status === 1 ? "green" : "red",
                                            marginLeft: "10px",
                                        }}
                                    /> */}
                                </Typography>


                                {/* {menuItem.children && menuItem.children.length > 0 && (
                                    <Box sx={{ marginLeft: "20px" }}>
                                        {menuItem.children.map((child) => (
                                            <Box key={child.id} sx={{ marginBottom: "5px" }}>
                                                <Typography
                                                    key={child.id}
                                                    variant="body1"
                                                    sx={{
                                                        marginTop: "5px",
                                                        display: "inline-flex",
                                                        alignItems: "center",
                                                    }}
                                                >
                                                    - {child.name}{" "}
                                                    <Box
                                                        sx={{
                                                            width: "10px",
                                                            height: "10px",
                                                            borderRadius: "50%",
                                                            backgroundColor: child.status === 1 ? "green" : "red",
                                                            marginLeft: "10px",
                                                        }}
                                                    />
                                                </Typography>
                                            </Box>
                                        ))}
                                    </Box>
                                )} */}
                            </Box>
                        ))}
                    </Box>
                </DialogContent>
                <DialogActions styled={{ margin: "5px 10px" }}>
                    <Button onClick={handleClose} color="error" variant="contained" autoFocus>
                        Close
                    </Button>
                </DialogActions>
            </Dialog>

        </div>
    );
};

export default ViewProjectList;
