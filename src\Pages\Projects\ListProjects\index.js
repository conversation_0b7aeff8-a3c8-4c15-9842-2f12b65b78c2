import React, { useEffect, useState } from "react";

import {
    Box,
    Button,
    Card,
    Collapse,
    FormControl,
    Grid,
    InputLabel,
    MenuItem,
    Select,
    styled,
    TextField,
    Snackbar,
} from "@mui/material";
import { Add, <PERSON><PERSON><PERSON><PERSON>, Close, FilterList } from "@mui/icons-material";
import httpclient from "../../../Utils";
import { useNavigate } from "react-router-dom";
import MuiAlert from "@mui/material/Alert";
import TableComponent from "../../../Components/TableComponent";
import EditDialog from "../../../Components/EditDialog";
import DeleteDialog from "../../../Components/DeleteDialog";
import useTokenRefresh from "../../../Hooks/useTokenRefresh";
import Footer from "../../../Components/Footer";
import ViewMenuList from "./ViewMenuList";
// import DeactivateDialog from "../DeactivateDialog";
// import ResetDialog from "../ResetDialog";
// import DeleteDialog from "../DeleteDialog";

const Alert = React.forwardRef(function Alert(props, ref) {
    return <MuiAlert elevation={6} ref={ref} variant="filled" {...props} />;
});

const login = localStorage.getItem("login");
const loginData = JSON.parse(login);


const columns = [
    { id: "sn", name: "SN" },
    { id: "project_id", name: "Project ID" },
    { id: "name", name: "Project Name" },
    { id: "base_url", name: "Project Base URL" },
    { id: "status", name: "Status" },
    { id: "menuList", name: "Menu List" },
    { id: "actions", name: "Actions" },
];


const superOptions = [
    { id: "edit", name: "Edit", action: "handleEdit" },
    { id: "deactivate", name: "Deactivate", action: "handleDeactivate" },
    { id: "reset", name: "Reset Password", action: "handleResetPassword" },
    { id: "delete", name: "Delete", action: "handleDelete" },
];

const adminOptions = [
    { id: "edit", name: "Edit", action: "handleEdit" },
    { id: "reset", name: "Reset Password", action: "handleResetPassword" },
]

const FilteredBox = styled(Box)(({ theme }) => ({
    background: "#f9f9f9",
    padding: "5px 10px",
    borderRadius: "5px",
    "& p": {
        margin: "0",
        marginRight: "10px",
        display: "inline-block",
        background: "#dedede",
        borderRadius: "10px",
        padding: "2px 5px",
    },
    "& svg": {
        fontSize: "15px",
        cursor: "pointer",
        position: "relative",
        top: "3px",
        background: theme.palette.primary.dark,
        color: "#fff",
        borderRadius: "50%",
        padding: "2px",
        marginLeft: "2px",
    },
}));

const Header = styled("div")(({ theme }) => ({
    "& h1": {
        color: theme.palette.primary.dark,
        margin: "0",
    },
}));

const AddButton = styled(Button)(({ theme }) => ({
    marginLeft: "10px",
    "& svg": {
        fontSize: "15px",
    },
}));

const configRowPerPage = JSON.parse(localStorage.getItem("configRowPerPage"));



const ListProjects = (props) => {

    const { getTokenRefreshed: refresh, open: tokenOpen, overlay: overlay, setOpen: setTokenOpen, message: tokenMessage, messageState: tokenMessageState } = useTokenRefresh();

    const navigate = useNavigate();
    const [openResetDialog, setOpenResetDialog] = useState(false);
    const [viewDetails, setViewDetails] = useState({});
    const [menuList, setMenuList] = useState([]);
    const [openActiveDialog, setOpenActiveDialog] = useState(false);
    const [openDeleteDialog, setOpenDeleteDialog] = useState(false);
    const [openEditDialog, setOpenEditDialog] = useState(false);
    const [openMenuListDialog, setOpenMenuListDialog] = useState(false);
    const [rows, setRows] = useState([]);
    const [modulePermissionList, setModulePermissionList] = useState([]);
    const [open, setOpen] = useState(false);
    const [message, setMessage] = useState("");
    const [messageState, setMessageState] = useState("");

    const [loading, setLoading] = useState(false);
    const [rowLoading, setRowLoading] = useState({});
    const [direction, setDirection] = useState(false);
    const [currentColumn, setCurrentColumn] = useState("");
    const [page, setPage] = useState(1);
    const [from, setFrom] = useState(1);
    const [to, setTo] = useState(
        configRowPerPage && configRowPerPage
            ? configRowPerPage && configRowPerPage
            : 20
    );

    const [rowsPerPage, setRowsPerPage] = useState(
        configRowPerPage && configRowPerPage
            ? configRowPerPage && configRowPerPage
            : 20
    );
    const [total, setTotal] = useState("");
    const [filterOpen, setFilterOpen] = useState(false);

    const [filterData, setFilterData] = useState({
        project_id: "",
        project_name: "",
        project_status: "",
        remove: false,
    });

    const [submittedData, setSubmittedData] = useState({
        project_id: "",
        project_name: "",
        project_status: "",
        submit: false,
    });

    // useEffect(() => {
    //   getAllProjects();
    // }, []);

    useEffect(() => {
        if (
            filterData.project_id === "" &&
            filterData.project_name === "" &&
            filterData.project_status === ""
        ) {
            setSubmittedData({
                ...submittedData,
                submit: false,
            });
        }
        if (filterData.project_id === " ") filterData.project_id = "";
        if (filterData.project_name === " ") filterData.project_name = "";
        if (filterData.project_status === " ") filterData.project_status = "";

        filterData.remove === true && handleFilter();
    }, [filterData]);

    useEffect(() => {
        let userStorage = JSON.parse(localStorage.getItem("project_filter"));
        userStorage !== null && setFilterData(userStorage);

        userStorage == null
            ? getAllProjects()
            : userStorage.project_id == "" &&
                userStorage.project_name == "" &&
                userStorage.project_status == "" &&

                userStorage.removed == false
                ? getAllProjects()
                : console.log("users!");
    }, []);

    const getAllProjects = () => {
        setLoading(true);
        httpclient.get(`projects?pagination=${rowsPerPage}`).then(({ data }) => {
            if (data.status === 200) {
                setRows(data.data);
                setModulePermissionList(data.modulePermissionList);
                setTotal(data.meta.total);
                setRowsPerPage(parseInt(data.meta.per_page));
                setPage(data.meta.current_page);
                setFrom(data.meta.from);
                setTo(data.meta.to);
                setLoading(false);
            } else {
                setOpen(true);
                setMessage(data.message);
                setMessageState("error");
                setLoading(false);
            }

        }).catch((err) => {
            if (err.response.status === 401) {
                refresh();
                setOpen(tokenOpen);
                setMessage(tokenMessage);
                setMessageState("error");
            } else if (err.response.status === 422) {
                const errorMessages = Object.values(err.response.data.errors).flat();
                setOpen(true);
                setMessage(errorMessages);
                setMessageState("error");
                setLoading(false);
            } else if (err.response.status === 400) {
                const errorMessages = Object.values(err.response.data.errors).flat();
                setOpen(true);
                setMessage(errorMessages);
                setMessageState("error");
                setLoading(false);

            } else {
                setOpen(true);
                setMessage(err.response.data.message);
                setMessageState("error");
                setLoading(false);
            }
        })

    };

    const hadleFilterOpen = () => {
        setFilterOpen((prev) => !prev);
    };

    const handleChangeFilter = (e) => {
        const { name, value } = e.target;
        setFilterData({
            ...filterData,
            [name]: value,
            remove: false,
        });
    };

    const handleFilter = () => {
        setSubmittedData({
            ...submittedData,
            project_id: filterData.project_id,
            project_name: filterData.project_name,
            project_status: filterData.project_status,

            submit: true,
        });
        filterData.remove = true;
        localStorage.setItem("project_filter", JSON.stringify(filterData));
        setLoading(true);
        if (
            filterData.project_id ||
            filterData.project_name ||
            filterData.project_status
        ) {
            httpclient
                .get(
                    `projects?filters[project_id][$eq]=${filterData.project_id}&filters[name][$contains]=${filterData.project_name}&filters[status][$eq]=${filterData.project_status}&pagination=${rowsPerPage}&page=${1}`
                )
                .then(({ data }) => {
                    if (data.status === 200) {
                        setRows(data.data);
                        setModulePermissionList(data.modulePermissionList);
                        setTotal(data.meta.total);
                        setRowsPerPage(data.meta.per_page);
                        setPage(data.meta.current_page);
                        setFrom(data.meta.from);
                        setTo(data.meta.to);
                        setLoading(false);
                    } else {
                        setOpen(true);
                        setMessage(data.message);
                        setMessageState("error");
                        setLoading(false);
                    }

                }).catch((err) => {
                    if (err.response.status === 401) {
                        refresh();
                        setOpen(tokenOpen);
                        setMessage(tokenMessage);
                        setMessageState("error");
                    } else if (err.response.status === 422) {
                        const errorMessages = Object.values(err.response.data.errors).flat();
                        setOpen(true);
                        setMessage(errorMessages);
                        setMessageState("error");
                        setLoading(false);
                    } else if (err.response.status === 400) {
                        const errorMessages = Object.values(err.response.data.errors).flat();
                        setOpen(true);
                        setMessage(errorMessages);
                        setMessageState("error");
                        setLoading(false);

                    } else {
                        setOpen(true);
                        setMessage(err.response.data.message);
                        setMessageState("error");
                        setLoading(false);
                    }
                })

        } else {
            getAllProjects();
        }
    };

    const handleRemove = (data) => {
        setFilterData({
            ...filterData,
            [data]: "",
            remove: true,
        });

        setSubmittedData({
            ...submittedData,
            [data]: "",
        });
    };

    const handleSort = (column) => {
        setDirection((prevDirection) => !prevDirection);
        setCurrentColumn(column);
        setLoading(true);
        submittedData.submit
            ? httpclient
                .get(
                    `projects?filters[project_id][$eq]=${filterData.project_id}&filters[name][$contains]=${filterData.project_name}&filters[status][$eq]=${filterData.project_status}&sort[0]=${column}:${!direction ? "asc" : "desc"
                    }&pagination=${rowsPerPage}&page=${page}`
                )
                .then(({ data }) => {
                    if (data.status === 200) {
                        setRows(data.data);
                        setTotal(data.meta.total);
                        setRowsPerPage(parseInt(data.meta.per_page));
                        setPage(data.meta.current_page);
                        setFrom(data.meta.from);
                        setTo(data.meta.to);
                        setLoading(false);
                    } else {
                        setOpen(true);
                        setMessage(data.message);
                        setMessageState("error");
                        setLoading(false);
                    }

                }).catch((err) => {
                    if (err.response.status === 401) {
                        refresh();
                        setOpen(tokenOpen);
                        setMessage(tokenMessage);
                        setMessageState("error");
                    } else if (err.response.status === 422) {
                        const errorMessages = Object.values(err.response.data.errors).flat();
                        setOpen(true);
                        setMessage(errorMessages);
                        setMessageState("error");
                        setLoading(false);
                    } else if (err.response.status === 400) {
                        const errorMessages = Object.values(err.response.data.errors).flat();
                        setOpen(true);
                        setMessage(errorMessages);
                        setMessageState("error");
                        setLoading(false);

                    } else {
                        setOpen(true);
                        setMessage(err.response.data.message);
                        setMessageState("error");
                        setLoading(false);
                    }
                })
            : httpclient
                .get(
                    `projects?sort[0]=${column}:${!direction ? "asc" : "desc"
                    }&pagination=${rowsPerPage}`
                )
                .then(({ data }) => {
                    if (data.status === 200) {
                        setRows(data.data);
                        setTotal(data.meta.total);
                        setRowsPerPage(parseInt(data.meta.per_page));
                        setPage(data.meta.current_page);
                        setFrom(data.meta.from);
                        setTo(data.meta.to);
                        setLoading(false);
                    } else {
                        setOpen(true);
                        setMessage(data.message);
                        setMessageState("error");
                        setLoading(false);
                    }

                }).catch((err) => {
                    if (err.response.status === 401) {
                        refresh();
                        setOpen(tokenOpen);
                        setMessage(tokenMessage);
                        setMessageState("error");
                    } else if (err.response.status === 422) {
                        const errorMessages = Object.values(err.response.data.errors).flat();
                        setOpen(true);
                        setMessage(errorMessages);
                        setMessageState("error");
                        setLoading(false);
                    } else if (err.response.status === 400) {
                        const errorMessages = Object.values(err.response.data.errors).flat();
                        setOpen(true);
                        setMessage(errorMessages);
                        setMessageState("error");
                        setLoading(false);

                    } else {
                        setOpen(true);
                        setMessage(err.response.data.message);
                        setMessageState("error");
                        setLoading(false);
                    }
                })
    };

    const handleChangePage = (e, page) => {
        setLoading(true);
        submittedData.submit
            ? httpclient
                .get(
                    `projects?filters[project_id][$eq]=${filterData.project_id}&filters[name][$contains]=${filterData.project_name}&filters[status][$eq]=${filterData.project_status
                    }${currentColumn ? `&sort[0]=${currentColumn}:${direction ? "asc" : "desc"}` : ''
                    }&pagination=${rowsPerPage}&page=${page}`
                )
                .then(({ data }) => {
                    if (data.status === 200) {
                        setRows(data.data);
                        setTotal(data.meta.total);
                        setRowsPerPage(parseInt(data.meta.per_page));
                        setPage(data.meta.current_page);
                        setFrom(data.meta.from);
                        setTo(data.meta.to);
                        setLoading(false);
                    } else {
                        setOpen(true);
                        setMessage(data.message);
                        setMessageState("error");
                        setLoading(false);
                    }

                }).catch((err) => {
                    if (err.response.status === 401) {
                        refresh();
                        setOpen(tokenOpen);
                        setMessage(tokenMessage);
                        setMessageState("error");
                    } else if (err.response.status === 422) {
                        const errorMessages = Object.values(err.response.data.errors).flat();
                        setOpen(true);
                        setMessage(errorMessages);
                        setMessageState("error");
                        setLoading(false);
                    } else if (err.response.status === 400) {
                        const errorMessages = Object.values(err.response.data.errors).flat();
                        setOpen(true);
                        setMessage(errorMessages);
                        setMessageState("error");
                        setLoading(false);

                    } else {
                        setOpen(true);
                        setMessage(err.response.data.message);
                        setMessageState("error");
                        setLoading(false);
                    }
                })
            : httpclient
                .get(
                    `projects?pagination=${rowsPerPage}&page=${page}`
                )
                .then(({ data }) => {
                    if (data.status === 200) {
                        setRows(data.data);
                        setTotal(data.meta.total);
                        setRowsPerPage(parseInt(data.meta.per_page));
                        setPage(data.meta.current_page);
                        setFrom(data.meta.from);
                        setTo(data.meta.to);
                        setLoading(false);
                    } else {
                        setOpen(true);
                        setMessage(data.message);
                        setMessageState("error");
                        setLoading(false);
                    }

                }).catch((err) => {
                    if (err.response.status === 401) {
                        refresh();
                        setOpen(tokenOpen);
                        setMessage(tokenMessage);
                        setMessageState("error");
                    } else if (err.response.status === 422) {
                        const errorMessages = Object.values(err.response.data.errors).flat();
                        setOpen(true);
                        setMessage(errorMessages);
                        setMessageState("error");
                        setLoading(false);
                    } else if (err.response.status === 400) {
                        const errorMessages = Object.values(err.response.data.errors).flat();
                        setOpen(true);
                        setMessage(errorMessages);
                        setMessageState("error");
                        setLoading(false);

                    } else {
                        setOpen(true);
                        setMessage(err.response.data.message);
                        setMessageState("error");
                        setLoading(false);
                    }
                })
    };

    const handleChangeRowsPerPage = (event) => {
        setRowsPerPage(+event.target.value);
        setLoading(true);

        localStorage.setItem("configRowPerPage", event.target.value);

        submittedData.submit
            ? httpclient
                .get(
                    `projects?filters[project_id][$eq]=${filterData.project_id}&filters[name][$contains]=${filterData.project_name}&filters[status][$eq]=${filterData.project_status
                    }${currentColumn ? `&sort[0]=${currentColumn}:${direction ? "asc" : "desc"}` : ''
                    }&pagination=${+event.target.value}&page=${page}`
                )
                .then(({ data }) => {
                    setLoading(true);
                    if (data.status === 200) {
                        setRows(data.data);
                        setTotal(data.meta.total);
                        setRowsPerPage(parseInt(data.meta.per_page));
                        setPage(data.meta.current_page);
                        setFrom(data.meta.from);
                        setTo(data.meta.to);
                        setLoading(false);
                    } else {
                        setOpen(true);
                        setMessage(data.message);
                        setMessageState("error");
                        setLoading(false);
                    }

                }).catch((err) => {
                    if (err.response.status === 401) {
                        refresh();
                        setOpen(tokenOpen);
                        setMessage(tokenMessage);
                        setMessageState("error");
                    } else if (err.response.status === 422) {
                        const errorMessages = Object.values(err.response.data.errors).flat();
                        setOpen(true);
                        setMessage(errorMessages);
                        setMessageState("error");
                        setLoading(false);
                    } else if (err.response.status === 400) {
                        const errorMessages = Object.values(err.response.data.errors).flat();
                        setOpen(true);
                        setMessage(errorMessages);
                        setMessageState("error");
                        setLoading(false);

                    } else {
                        setOpen(true);
                        setMessage(err.response.data.message);
                        setMessageState("error");
                        setLoading(false);
                    }
                })
            : httpclient
                .get(
                    `projects?pagination=${+event
                        .target.value}&page=${1}`
                )
                .then(({ data }) => {
                    setLoading(true);
                    if (data.status === 200) {
                        setRows(data.data);
                        setTotal(data.meta.total);
                        setRowsPerPage(parseInt(data.meta.per_page));
                        setFrom(data.meta.from);
                        setTo(data.meta.to);
                        setPage(data.meta.current_page);
                        setLoading(false);
                    } else {
                        setOpen(true);
                        setMessage(data.message);
                        setMessageState("error");
                        setLoading(false);
                    }

                }).catch((err) => {
                    if (err.response.status === 401) {
                        refresh();
                        setOpen(tokenOpen);
                        setMessage(tokenMessage);
                        setMessageState("error");
                    } else if (err.response.status === 422) {
                        const errorMessages = Object.values(err.response.data.errors).flat();
                        setOpen(true);
                        setMessage(errorMessages);
                        setMessageState("error");
                        setLoading(false);
                    } else if (err.response.status === 400) {
                        const errorMessages = Object.values(err.response.data.errors).flat();
                        setOpen(true);
                        setMessage(errorMessages);
                        setMessageState("error");
                        setLoading(false);

                    } else {
                        setOpen(true);
                        setMessage(err.response.data.message);
                        setMessageState("error");
                        setLoading(false);
                    }
                })
    };


    const handleAddNew = () => {
        const updatedModulePermissionList = modulePermissionList.map((module) => ({
            id: module.module_id,
            name: module.module_name,
            sub_modules: module.sub_modules,
        }));
        setViewDetails({
            modulePermission: [...updatedModulePermissionList]
        });
        setOpenEditDialog(true)
    };


    const handleEdit = (row) => {
        const updatedModules = row.modules.map((module) => ({
            id: module.module_id,
            name: module.module_name,
            status: module.module_status,
            sub_modules: module.sub_modules,
        }));

        setViewDetails((prevDetails) => ({
            ...prevDetails,
            ...row,
            modulePermission: updatedModules,
        }));

        setOpenEditDialog(true);
    };


    const handleViewMenuList = (row) => {
        setOpenMenuListDialog(true)
        setMenuList(row.menus);
    };
    const handleCloseMenuList = () => {
        setOpenMenuListDialog(false)
        setMenuList([]);
    };

    const sendEdit = (call, formData) => {
        if (call.open === false) {

            setOpenEditDialog(false);
            setViewDetails({});
        }
        if (call.success === true) {
            viewDetails.id ? (
                httpclient
                    .put(`projects/${viewDetails.id}`, formData)
                    .then(({ data }) => {
                        if (data.status === 200) {
                            setOpen(true);
                            setMessageState("success");
                            setMessage(data.message);
                            setOpenActiveDialog(false);
                            setViewDetails({});
                            getAllProjects();
                        }
                        else {
                            setOpen(true);
                            setMessage(data.message);
                            setMessageState("error");
                            setLoading(false);
                        }

                    }).catch((err) => {
                        if (err.response.status === 401) {
                            refresh();
                            setOpen(tokenOpen);
                            setMessage(tokenMessage);
                            setMessageState("error");
                        } else if (err.response.status === 422) {
                            const errorMessages = Object.values(err.response.data.errors).flat();
                            setOpen(true);
                            setMessage(errorMessages);
                            setMessageState("error");
                            setLoading(false);
                        } else if (err.response.status === 400) {
                            const errorMessages = Object.values(err.response.data.errors).flat();
                            setOpen(true);
                            setMessage(errorMessages);
                            setMessageState("error");
                            setLoading(false);

                        } else {
                            setOpen(true);
                            setMessage(err.response.data.message);
                            setMessageState("error");
                            setLoading(false);
                        }
                    })
            ) :
                httpclient
                    .post(`projects`, formData)
                    .then(({ data }) => {
                        if (data.status === 200) {
                            setOpen(true);
                            setMessageState("success");
                            setMessage(data.message);
                            setOpenActiveDialog(false);
                            setViewDetails({});
                            getAllProjects();
                            props.getAllProjects();
                        } else {
                            setOpen(true);
                            setMessage(data.message);
                            setMessageState("error");
                            setLoading(false);
                        }

                    }).catch((err) => {
                        if (err.response.status === 401) {
                            refresh();
                            setOpen(tokenOpen);
                            setMessage(tokenMessage);
                            setMessageState("error");
                        } else if (err.response.status === 422) {
                            const errorMessages = Object.values(err.response.data.errors).flat();
                            setOpen(true);
                            setMessage(errorMessages);
                            setMessageState("error");
                            setLoading(false);
                        } else if (err.response.status === 400) {
                            const errorMessages = Object.values(err.response.data.errors).flat();
                            setOpen(true);
                            setMessage(errorMessages);
                            setMessageState("error");
                            setLoading(false);

                        } else {
                            setOpen(true);
                            setMessage(err.response.data.message);
                            setMessageState("error");
                            setLoading(false);
                        }

                    })
        }
    };


    const handleDelete = (row) => {
        setOpenDeleteDialog(true);
        setViewDetails(row)
    };

    const sendDelete = (call, formData) => {
        if (call.open === false) {
            setOpenDeleteDialog(false)
            setViewDetails({})
        }
        if (call.success === true) {
            httpclient
                .delete(`projects/${viewDetails.id}`, formData)
                .then(({ data }) => {
                    if (data.status === 200) {
                        setOpen(true);
                        setMessageState("success");
                        setMessage(data.message);
                        setOpenDeleteDialog(false);
                        setViewDetails({});
                        getAllProjects();
                    } else {
                        setOpen(true);
                        setMessage(data.message);
                        setMessageState("error");
                        setLoading(false);
                    }

                }).catch((err) => {
                    if (err.response.status === 401) {
                        refresh();
                        setOpen(tokenOpen);
                        setMessage(tokenMessage);
                        setMessageState("error");
                    } else if (err.response.status === 422) {
                        const errorMessages = Object.values(err.response.data.errors).flat();
                        setOpen(true);
                        setMessage(errorMessages);
                        setMessageState("error");
                        setLoading(false);
                    } else if (err.response.status === 400) {
                        const errorMessages = Object.values(err.response.data.errors).flat();
                        setOpen(true);
                        setMessage(errorMessages);
                        setMessageState("error");
                        setLoading(false);

                    } else {
                        setOpen(true);
                        setMessage(err.response.data.message);
                        setMessageState("error");
                        setLoading(false);
                    }
                })
        }
    }

    // Handle API call to update row status
    const updateRowStatus = (rowId) => {
        setRowLoading((prev) => ({ ...prev, [rowId.id]: true }));
        httpclient
            .post(`projects/${rowId.id}/status`)
            .then(({ data }) => {
                if (data.status === 200) {
                    setRowLoading((prev) => ({ ...prev, [rowId.id]: false }));
                    setOpen(true);
                    setMessageState("success");
                    setMessage(data.message);
                    setViewDetails({});
                    getAllProjects();
                    props.getAllProjects();
                } else {
                    setRowLoading((prev) => ({ ...prev, [rowId.id]: false }));
                    setOpen(true);
                    setMessageState("error");
                    setMessage(data.error || data.message);
                }
            }).catch((err) => {
                if (err.response.status === 401) {
                    refresh();
                    setOpen(tokenOpen);
                    setMessage(tokenMessage);
                    setMessageState("error");
                } else if (err.response.status === 422) {
                    const errorMessages = Object.values(err.response.data.errors).flat();
                    setOpen(true);
                    setMessage(errorMessages);
                    setMessageState("error");
                    setRowLoading((prev) => ({ ...prev, [rowId.id]: false }));
                } else if (err.response.status === 400) {
                    const errorMessages = Object.values(err.response.data.errors).flat();
                    setOpen(true);
                    setMessage(errorMessages);
                    setMessageState("error");
                    setRowLoading((prev) => ({ ...prev, [rowId.id]: false }));

                } else {
                    setOpen(true);
                    setMessage(err.response.data.message);
                    setMessageState("error");
                    setRowLoading((prev) => ({ ...prev, [rowId.id]: false }));
                }
            })
    };


    const currentChange = (value, row) => {

        if (value === "allow_update") {
            handleEdit(row);
        }

        if (value === "allow_delete") {
            handleDelete(row);
        }
    };

    const handleClose = (event, reason) => {
        if (reason === "clickaway") {
            return;
        }
        setOpen(false);
        setTokenOpen(false);
    };

    return (
        <div>
            <Grid container spacing={2}>
                <Grid item md={8} xs={12}>
                    <Header>
                        <h1>List Projects</h1>
                    </Header>
                </Grid>
                <Grid
                    item
                    md={4}
                    xs={12}
                    display="flex"
                    alignItems="center"
                    justifyContent="flex-end"
                >
                    <Button color="primary" variant="contained" onClick={hadleFilterOpen}>
                        Filter <FilterList style={{ marginLeft: "5px" }} fontSize="small" />
                    </Button>

                    {props?.permissions?.some((pre) => pre.name === "allow_create" && pre.status === 1) &&
                        <AddButton
                            color="primary"
                            variant="contained"
                            onClick={handleAddNew}
                        >
                            <Add style={{ marginRight: "5px" }} fontSize="small" /> Add Project
                        </AddButton>
                    }
                </Grid>

                {/* Filter */}
                <Grid item xs={12}>
                    <Collapse in={filterOpen}>
                        <Card>
                            <Box p={4}>
                                <Grid container spacing={2}>
                                    {/* <Grid item xs={12} md={4}>
                                        <InputLabel>Project ID</InputLabel>
                                        <TextField
                                            variant="outlined"
                                            name="project_id"
                                            value={filterData.project_id}
                                            onChange={handleChangeFilter}
                                            onKeyDown={e => { if (e.key === "Enter") handleFilter() }}
                                            fullWidth
                                        />
                                    </Grid> */}
                                    <Grid item xs={12} md={4}>
                                        <InputLabel>Project Name</InputLabel>
                                        <TextField
                                            variant="outlined"
                                            name="project_name"
                                            value={filterData.project_name}
                                            onChange={handleChangeFilter}
                                            onKeyDown={e => { if (e.key === "Enter") handleFilter() }}
                                            fullWidth
                                        />
                                    </Grid>
                                    <Grid item xs={12} md={4}>
                                        <InputLabel>Status</InputLabel>
                                        {/* <TextField
                                            variant="outlined"
                                            name="project_status"
                                            value={filterData.project_status}
                                            onChange={handleChangeFilter}
                                            onKeyDown={e => { if (e.key === "Enter") handleFilter() }}
                                            fullWidth
                                        /> */}

                                        <FormControl fullWidth>
                                            <Select
                                                name="project_status"
                                                value={filterData.project_status}
                                                onChange={handleChangeFilter}
                                                onKeyDown={e => { if (e.key === "Enter") handleFilter() }}
                                            >
                                                <MenuItem value={""}>Select</MenuItem>
                                                <MenuItem value={"1"}>Active</MenuItem>
                                                <MenuItem value={"0"}>Inactive</MenuItem>

                                            </Select>
                                        </FormControl>



                                    </Grid>



                                    <Grid item xs={12}>
                                        <Box textAlign={"right"}>
                                            <Button
                                                variant="contained"
                                                color="primary"
                                                onClick={handleFilter}
                                            >
                                                Filter{" "}
                                                <ArrowForward
                                                    fontSize="small"
                                                    style={{ marginLeft: "5px" }}
                                                />
                                            </Button>
                                        </Box>
                                    </Grid>
                                </Grid>
                            </Box>
                        </Card>
                    </Collapse>
                </Grid>

                {submittedData.project_id ||
                    submittedData.project_name ||
                    submittedData.project_status ? (
                    <Grid item xs={12}>
                        <FilteredBox>
                            <span>Filtered: </span>
                            {submittedData.project_id && (
                                <p>
                                    <span>Project ID: {submittedData.project_id}</span>
                                    <Close
                                        fontSize="small"
                                        onClick={() => handleRemove("project_id")}
                                    />
                                </p>
                            )}
                            {submittedData.project_name && (
                                <p>
                                    <span>Project Name: {submittedData.project_name}</span>
                                    <Close
                                        fontSize="small"
                                        onClick={() => handleRemove("project_name")}
                                    />
                                </p>
                            )}
                            {submittedData.project_status && (
                                <p>
                                    <span>Status: {submittedData.project_status === "1" ? "Active" : "Inactive"}</span>
                                    <Close
                                        fontSize="small"
                                        onClick={() => handleRemove("project_status")}
                                    />
                                </p>
                            )}

                        </FilteredBox>
                    </Grid>
                ) : (
                    <Box></Box>
                )}
                {/* Filter */}

                <Grid item xs={12}>
                    <TableComponent
                        name={"Project"}
                        columns={columns}
                        rows={rows}
                        sort={true}
                        handleViewMenuList={handleViewMenuList}
                        handleSort={handleSort}
                        updateRowStatus={updateRowStatus}
                        setRowLoading={setRowLoading}
                        rowLoading={rowLoading}
                        props={props}
                        options={props?.permissions}
                        currentChange={currentChange}
                        loading={loading}
                        direction={direction}
                        currentColumn={currentColumn}
                        handleChangeRowsPerPage={handleChangeRowsPerPage}
                        handleChangePage={handleChangePage}
                        page={page}
                        total={total && total}
                        fromTable={from}
                        toTable={to}
                        rowsPerPage={rowsPerPage}
                    />
                </Grid>
                <Footer overlay={overlay || props.overlayNew} />
            </Grid>



            {openDeleteDialog && <DeleteDialog name={"Project"} viewDetails={viewDetails} sendDelete={sendDelete} />}

            {openEditDialog && (
                <EditDialog
                    viewDetails={viewDetails}
                    sendEdit={sendEdit}
                />
            )}
            {openMenuListDialog && (
                <ViewMenuList
                    menuList={menuList}
                    handleCloseMenuList={handleCloseMenuList}
                />
            )}

            <Snackbar
                autoHideDuration={3000}
                anchorOrigin={{ vertical: "top", horizontal: "right" }}
                open={open || tokenOpen}
                onClose={handleClose}
            >
                <Alert
                    onClose={handleClose}
                    severity={messageState || tokenMessageState}
                    sx={{ width: "100%" }}
                >
                    {message || tokenMessage}
                </Alert>
            </Snackbar>
        </div>
    );
};

export default ListProjects;
