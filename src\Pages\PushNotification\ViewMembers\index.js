import {
    <PERSON>pp<PERSON>ar,
    Box,
    <PERSON>ton,
    CircularProgress,
    Dialog,
    DialogActions,
    DialogContent,
    DialogTitle,
    Grid,
    InputLabel,
    Skeleton,
    styled,
    TextField,
    Typography,
    useTheme
} from "@mui/material";
import moment from "moment/moment";
import { useEffect, useState } from "react";
import Tabs from "@mui/material/Tabs";
import Tab from "@mui/material/Tab";
import * as React from 'react';
import useTokenRefresh from "../../../Hooks/useTokenRefresh";
import httpclient from "../../../Utils";
import PropTypes from "prop-types";
import TableComponent from "../../../Components/TableComponent";

const StyledHeaderTitle = styled(DialogTitle)(({ theme }) => ({
    background: theme.palette.primary.main,
    color: "#fff",
    position: "relative",
    "& button": {
        position: "absolute",
        right: "15px",
        top: "15px",
        color: "#fff",
    },
}));


const SearchBox = styled(Box)(({ theme }) => ({
    "& input": {
        padding: "10px",
    },
}));

const FlexContent = styled("div")(({ theme }) => ({
    display: "flex",
    flexDirection: "row",
    fontSize: "17px",
    marginBottom: "10px",
    alignItems: "flex-start",
}));

const FlexInnerTitle = styled("div")(({ theme }) => ({
    display: "flex",
    alignItems: "center",
    justifyContent: "space-between",
    minWidth: "270px",
    maxWidth: "270px",
    fontWeight: "600",
}));


const Values = styled("div")(({ theme }) => ({
    marginLeft: "15px",
    fontWeight: "500",
    color: theme.palette.primary.dark,
}));

const AppBarTabs = styled(AppBar)(({ theme }) => ({
    background: "#fff",
    color: theme.palette.primary.dark,
}));

function TabPanel(props) {
    const { children, value, index, ...other } = props;

    return (
        <div
            role="tabpanel"
            hidden={value !== index}
            id={`full-width-tabpanel-${index}`}
            aria-labelledby={`full-width-tab-${index}`}
            {...other}
        >
            {value === index && (
                <Box sx={{ p: 3 }}>
                    <Typography>{children}</Typography>
                </Box>
            )}
        </div>
    );
}

TabPanel.propTypes = {
    children: PropTypes.node,
    index: PropTypes.number.isRequired,
    value: PropTypes.number.isRequired,
};

function a11yProps(index) {
    return {
        id: `full-width-tab-${index}`,
        "aria-controls": `full-width-tabpanel-${index}`,
    };
}

const columns = [
    { id: "sn", name: "SN" },
    { id: "lite_card_member_id", name: "LiteCard Member ID" },
    { id: "name", name: "Name" },
    { id: "email", name: "Email" },

];

const configRowPerPage = JSON.parse(localStorage.getItem("configRowPerPage"));

const ViewMembers = (props) => {
    const { getTokenRefreshed: refresh, open: tokenOpen, overlay: overlay, setOpen: setTokenOpen, message: tokenMessage, messageState: tokenMessageState } = useTokenRefresh();

    const [dialogDetails, setDialogDetails] = useState({
        open: true,
        success: false,
    });
    const theme = useTheme();
    const [value, setValue] = useState(0);
    const [open, setOpen] = useState(false);
    const [message, setMessage] = useState("");
    const [messageState, setMessageState] = useState("");
    const [loading, setLoading] = useState(false);
    const [loadingSearch, setLoadingSearch] = useState(false);
    const [loadingSearchEmail, setLoadingSearchEmail] = useState(false);
    const [direction, setDirection] = useState(false);
    const [currentColumn, setCurrentColumn] = useState("");
    const [page, setPage] = useState(1);
    const [from, setFrom] = useState(1);
    const [to, setTo] = useState(
        configRowPerPage && configRowPerPage
            ? configRowPerPage && configRowPerPage
            : 20
    );

    const [rowsPerPage, setRowsPerPage] = useState(
        configRowPerPage && configRowPerPage
            ? configRowPerPage && configRowPerPage
            : 20
    );
    const [total, setTotal] = useState("");
    const [rows, setRows] = useState([]);
    const [searchMember, setSearchMember] = useState("");
    const [searchMemberEmail, setSearchMemberEmail] = useState("");

    const handleChange = (event, newValue) => {
        setValue(newValue);
    };

    useEffect(() => {
        getAllPushNotification(searchMember, searchMemberEmail);
    }, [searchMember, searchMemberEmail]);

    useEffect(() => {
        getAllPushNotification();
    }, [props.viewDetails]);
  

    const getAllPushNotification = (searchMember, searchMemberEmail) => {
        { searchMember ? setLoadingSearch(true) : searchMemberEmail ? setLoadingSearchEmail(true) : setLoading(true) };
        httpclient.get(`request-response?requestName=lightspeed/lite-card-member-list${(searchMember !== "" && searchMember !== undefined) ? `&lite_card_member_id=${searchMember}` : ""}${(searchMemberEmail !== "" && searchMemberEmail !== undefined) ? `&email=${searchMemberEmail}` : ""}${props.viewDetails.type === "store" ? `&store_id=${props.viewDetails.ids}` : props.viewDetails.type === "state" ? `&state=${props.viewDetails.ids}` : props.viewDetails.type === "user_list" ? `&lite_card_member_id=${props.viewDetails.ids}` : `&postal_code=${props.viewDetails.ids}`}&pagination=${rowsPerPage}`).then(({ data }) => {
            if (data.status === 200) {
                setRows(data.data);
                setTotal(data.meta.total);
                setRowsPerPage(parseInt(data.meta.per_page));
                setPage(data.meta.current_page);
                setFrom(data.meta.from);
                setTo(data.meta.to);
                { searchMember ? setLoadingSearch(false) : searchMemberEmail ? setLoadingSearchEmail(false) : setLoading(false) };
            } else {
                setOpen(true);
                setMessage(data.message);
                setMessageState("error");
                { searchMember ? setLoadingSearch(false) : searchMemberEmail ? setLoadingSearchEmail(false) : setLoading(false) };
            }

        }).catch((err) => {
            if (err.response.status === 401) {
                refresh();
                setOpen(tokenOpen);
                setMessage(tokenMessage);
                setMessageState("error");
            } else if (err.response.status === 422) {
                const errorMessages = Object.values(err.response.data.errors).flat();
                setOpen(true);
                setMessage(errorMessages);
                setMessageState("error");
                setLoading(false);
            } else if (err.response.status === 400) {
                const errorMessages = Object.values(err.response.data.errors).flat();
                setOpen(true);
                setMessage(errorMessages);
                setMessageState("error");
                setLoading(false);

            } else {
                setOpen(true);
                setMessage(err.response.data.message);
                setMessageState("error");
                setLoading(false);
            }
        })
    }

    const handleSort = (column) => {
        setDirection((prevDirection) => !prevDirection);
        setCurrentColumn(column);
        setLoading(true);
        httpclient
            .get(
                `request-response?requestName=lightspeed/lite-card-member-list${props.viewDetails.type === "store" ? `&store_id=${props.viewDetails.ids}` : props.viewDetails.type === "state" ? `&state=${props.viewDetails.ids}`: props.viewDetails.type === "user_list" ? `&lite_card_member_id=${props.viewDetails.ids}` : `&postal_code=${props.viewDetails.ids}`}&sort[0]=${column}:${!direction ? "asc" : "desc"
                }&pagination=${rowsPerPage}`
            )
            .then(({ data }) => {
                if (data.status === 200) {
                    setRows(data.data);
                    setTotal(data.meta.total);
                    setRowsPerPage(parseInt(data.meta.per_page));
                    setPage(data.meta.current_page);
                    setFrom(data.meta.from);
                    setTo(data.meta.to);
                    setLoading(false);
                } else {
                    setOpen(true);
                    setMessage(data.message);
                    setMessageState("error");
                    setLoading(false);
                }

            }).catch((err) => {
                if (err.response.status === 401) {
                    refresh();
                    setOpen(tokenOpen);
                    setMessage(tokenMessage);
                    setMessageState("error");
                } else if (err.response.status === 422) {
                    const errorMessages = Object.values(err.response.data.errors).flat();
                    setOpen(true);
                    setMessage(errorMessages);
                    setMessageState("error");
                    setLoading(false);
                } else if (err.response.status === 400) {
                    const errorMessages = Object.values(err.response.data.errors).flat();
                    setOpen(true);
                    setMessage(errorMessages);
                    setMessageState("error");
                    setLoading(false);

                } else {
                    setOpen(true);
                    setMessage(err.response.data.message);
                    setMessageState("error");
                    setLoading(false);
                }
            })
    };

    const handleChangePage = (e, page) => {
        setLoading(true);
        httpclient
            .get(
                `request-response?requestName=lightspeed/lite-card-member-list${props.viewDetails.type === "store" ? `&store_id=${props.viewDetails.ids}` : props.viewDetails.type === "state" ? `&state=${props.viewDetails.ids}` : props.viewDetails.type === "user_list" ? `&lite_card_member_id=${props.viewDetails.ids}` : `&postal_code=${props.viewDetails.ids}`}&pagination=${rowsPerPage}&page=${page}`
            )
            .then(({ data }) => {
                if (data.status === 200) {
                    setRows(data.data);
                    setTotal(data.meta.total);
                    setRowsPerPage(parseInt(data.meta.per_page));
                    setPage(data.meta.current_page);
                    setFrom(data.meta.from);
                    setTo(data.meta.to);
                    setLoading(false);
                } else {
                    setOpen(true);
                    setMessage(data.message);
                    setMessageState("error");
                    setLoading(false);
                }

            }).catch((err) => {
                if (err.response.status === 401) {
                    refresh();
                    setOpen(tokenOpen);
                    setMessage(tokenMessage);
                    setMessageState("error");
                } else if (err.response.status === 422) {
                    const errorMessages = Object.values(err.response.data.errors).flat();
                    setOpen(true);
                    setMessage(errorMessages);
                    setMessageState("error");
                    setLoading(false);
                } else if (err.response.status === 400) {
                    const errorMessages = Object.values(err.response.data.errors).flat();
                    setOpen(true);
                    setMessage(errorMessages);
                    setMessageState("error");
                    setLoading(false);

                } else {
                    setOpen(true);
                    setMessage(err.response.data.message);
                    setMessageState("error");
                    setLoading(false);
                }
            })
    };

    const handleChangeRowsPerPage = (event) => {
        setRowsPerPage(+event.target.value);
        setLoading(true);

        localStorage.setItem("configRowPerPage", event.target.value);

        httpclient
            .get(
                `request-response?requestName=lightspeed/lite-card-member-list${props.viewDetails.type === "store" ? `&store_id=${props.viewDetails.ids}` : props.viewDetails.type === "state" ? `&state=${props.viewDetails.ids}` : props.viewDetails.type === "user_list" ? `&lite_card_member_id=${props.viewDetails.ids}` : `&postal_code=${props.viewDetails.ids}`}&pagination=${+event
                    .target.value}&page=${1}`
            )
            .then(({ data }) => {
                setLoading(true);
                if (data.status === 200) {
                    setRows(data.data);
                    setTotal(data.meta.total);
                    setRowsPerPage(parseInt(data.meta.per_page));
                    setFrom(data.meta.from);
                    setTo(data.meta.to);
                    setPage(data.meta.current_page);
                    setLoading(false);
                } else {
                    setOpen(true);
                    setMessage(data.message);
                    setMessageState("error");
                    setLoading(false);
                }

            }).catch((err) => {
                if (err.response.status === 401) {
                    refresh();
                    setOpen(tokenOpen);
                    setMessage(tokenMessage);
                    setMessageState("error");
                } else if (err.response.status === 422) {
                    const errorMessages = Object.values(err.response.data.errors).flat();
                    setOpen(true);
                    setMessage(errorMessages);
                    setMessageState("error");
                    setLoading(false);
                } else if (err.response.status === 400) {
                    const errorMessages = Object.values(err.response.data.errors).flat();
                    setOpen(true);
                    setMessage(errorMessages);
                    setMessageState("error");
                    setLoading(false);

                } else {
                    setOpen(true);
                    setMessage(err.response.data.message);
                    setMessageState("error");
                    setLoading(false);
                }
            })
    };

    const handleClose = () => {
        setDialogDetails({
            ...dialogDetails,
            open: false,
        });
        props.handleCloseViewMembers();
    };

    return (
        <div>
            <Dialog
                open={dialogDetails.open}
                onClose={handleClose}
                maxWidth="xl"
                fullWidth
                aria-labelledby="alert-dialog-title"
                aria-describedby="alert-dialog-description"
            >
                <StyledHeaderTitle id="alert-dialog-title">
                    {`View Members for ${props.viewDetails.type} : ${props.viewDetails.type === "state" ? props.viewDetails.ids.join(", ") :
                        props.viewDetails.type === "store" ? props.selectedNames.join(", ") : props.chips.join(", ")}
                    `}
                </StyledHeaderTitle>
                {loading ? (
                    <DialogContent>
                        <Grid container spacing={2}>
                            <Grid item xs={12} md={12}>
                                <Box p={3} sx={{ width: "100%" }}>
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                </Box>
                            </Grid>

                        </Grid>
                    </DialogContent>
                ) : (
                    <DialogContent sx={{ padding: "0" }}>
                        <AppBarTabs position="static">
                            <Tabs
                                value={value}
                                onChange={handleChange}
                                indicatorColor="secondary"
                                textColor="inherit"
                                variant="fullWidth"
                                aria-label="full width tabs example"
                            >
                                <Tab label="Member Details" {...a11yProps(0)} />
                            </Tabs>
                        </AppBarTabs>

                        <TabPanel value={value} index={0} dir={theme.direction}>
                            <Box
                                pb={3}
                                display={"flex"}
                                alignItems={"center"}
                                flexDirection={"row"}
                                justifyContent={"space-between"}
                            >
                                <Box flex={"1"}>
                                    <InputLabel>Search By LiteCard Member ID</InputLabel>
                                    <SearchBox display={"flex"}>
                                        <TextField
                                            type="search"
                                            variant="outlined"
                                            name="lite_card_member_id"
                                            value={searchMember}
                                            onChange={(e) => setSearchMember(e.target.value)}
                                            helperText={(searchMember === "" || searchMember === undefined) ? "Press any key to search." : ""}
                                            sx={{ width: "300px", marginRight: "10px" }}
                                        />
                                        {loadingSearch ? <CircularProgress size={30} /> : ""}
                                    </SearchBox>


                                </Box>
                                <Box flex={"1"}>
                                    <InputLabel>Search By Email</InputLabel>
                                    <SearchBox display={"flex"}>
                                        <TextField
                                            type="search"
                                            variant="outlined"
                                            name="email"
                                            value={searchMemberEmail}
                                            onChange={(e) => setSearchMemberEmail(e.target.value)}
                                            helperText={(searchMemberEmail === "" || searchMemberEmail === undefined) ? "Press any key to search." : ""}
                                            sx={{ width: "300px", marginRight: "10px" }}
                                        />
                                        {loadingSearchEmail ? <CircularProgress size={30} /> : ""}
                                    </SearchBox>


                                </Box>

                            </Box>
                            <Grid item xs={12}>
                                <TableComponent
                                    columns={columns}
                                    rows={rows}
                                    sort={true}
                                    handleSort={handleSort}
                                    loading={loading}
                                    handleChangeRowsPerPage={handleChangeRowsPerPage}
                                    handleChangePage={handleChangePage}
                                    direction={direction}
                                    currentColumn={currentColumn}
                                    page={page}
                                    total={total && total}
                                    fromTable={from}
                                    toTable={to}
                                    rowsPerPage={rowsPerPage}
                                />
                            </Grid>
                        </TabPanel>

                    </DialogContent>
                )}
                <DialogActions styled={{ margin: "5px 10px" }}>
                    <Button onClick={handleClose} color="error" variant="contained" autoFocus>
                        Close
                    </Button>
                </DialogActions>
            </Dialog>

        </div>
    );
};

export default ViewMembers;
