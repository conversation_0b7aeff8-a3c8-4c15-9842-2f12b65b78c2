import {
    Box,
    Button,
    Dialog,
    DialogActions,
    DialogContent,
    DialogTitle,
    Grid,
    styled,
    TextField,
} from "@mui/material";
import { useEffect, useState } from "react";
import * as React from 'react';


const StyledHeaderTitle = styled(DialogTitle)(({ theme }) => ({
    background: theme.palette.primary.main,
    color: "#fff",
    position: "relative",
    "& button": {
        position: "absolute",
        right: "15px",
        top: "15px",
        color: "#fff",
    },
}));

const EditDialogFog = (props) => {
    const [dialogDetails, setDialogDetails] = useState({
        open: true,
        success: false,
    });
    const [errors, setErrors] = useState({});
    const [formData, setFormData] = useState({
        fog_name: '',
        fog_id: '',
        type: "fog"

    });

    useEffect(() => {
        if (props.viewDetails) {
            setFormData({
                fog_name: props.viewDetails.name || '',
                fog_id: props.viewDetails.id || '',
                type: "fog"
            });
        }
    }, [props.viewDetails]);


    useEffect(() => {
        props.sendEdit(dialogDetails, formData);
    }, [dialogDetails]);


    const handleClose = () => {
        setDialogDetails({
            ...dialogDetails,
            open: false,
        });
    };

    const handleYes = () => {
        if (validate()) {
            setDialogDetails({
                ...dialogDetails,
                open: false,
                success: true,

            });
        }
    };

    const validate = () => {
        const newErrors = {};
        if (!formData.fog_name) newErrors.fog_name = 'Fog Name is required';
        // if (!formData.fog_id) newErrors.fog_id = 'Fog ID is required';

        setErrors(newErrors);
        return Object.keys(newErrors).length === 0;
    };


    const handleChange = (e) => {
        const { name, value } = e.target;
        setFormData((prevData) => ({
            ...prevData,
            [name]: value,
        }));
    };

    return (
        <div>
            <Dialog
                open={dialogDetails.open}
                onClose={handleClose}
                maxWidth="md"
                fullWidth
                aria-labelledby="alert-dialog-title"
                aria-describedby="alert-dialog-description"
            >
                <StyledHeaderTitle id="alert-dialog-title">
                    {props.viewDetails.id ? `Edit Fog` : `Add Fog`}
                </StyledHeaderTitle>
                <DialogContent>
                    <Box pt={3}>
                        {props.viewDetails.id &&
                            <TextField
                                required
                                disabled
                                label="Fog ID"
                                name="fog_id"
                                type="number"
                                value={formData.fog_id}
                                onChange={handleChange}
                                error={!!errors.fog_id}
                                helperText={errors.fog_id}
                                fullWidth
                                margin="normal"
                            />
                        }

                        <TextField
                            required
                            label="Fog Name"
                            name="fog_name"
                            value={formData.fog_name}
                            onChange={handleChange}
                            error={!!errors.fog_name}
                            helperText={errors.fog_name}
                            fullWidth
                            margin="normal"
                        />


                    </Box>
                </DialogContent>
                <DialogActions styled={{ margin: "5px 10px" }}>
                    <Button onClick={handleClose} color="error" variant="contained" autoFocus>
                        Cancel
                    </Button>
                    <Button
                        onClick={handleYes}
                        color="primary"
                        variant="contained"
                        autoFocus
                    >
                        {props.viewDetails.id ? `Edit` : `Save`}
                    </Button>
                </DialogActions>
            </Dialog>
        </div>
    );
};

export default EditDialogFog;
