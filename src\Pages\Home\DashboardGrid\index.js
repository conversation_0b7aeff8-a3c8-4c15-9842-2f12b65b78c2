
// import React, { useEffect, useState } from "react";
// import { Skeleton} from "@mui/material";
// import Mu<PERSON><PERSON><PERSON>t from "@mui/material/Alert";

// const DashboardGrid1 = (props) => {

//     const renderContent = () => {
//         if (props.loading) {
//             return (
//                 <div className="grid-block-content">
//                     {props.title.map((item) => (
//                         <>
//                             <div className="grid-item title">{item.name}</div>
//                             <div className="grid-item content">
//                                 <Skeleton variant="text" />
//                             </div>
//                         </>
//                     ))}
//                 </div>
//             );
//         }

//         return (
//             <div className="grid-block-content">
//                 {props.title.map((item) => (
//                     <>
//                         <div className="grid-item title">{item.name}</div>
//                         <div className="grid-item content">{props.productStatus[item.id]}</div>
//                     </>
//                 ))}
//             </div>
//         );
//     };


//     return (

//         <div className="grid-block">
//             <div className="grid-block-header">
//                 Total Shopify Data Count
//             </div>
//             {renderContent()}
//             <style jsx>{`
//         .grid-block {
//           border: 0.5px solid #gray;
//           box-shadow:0px 5px 20px 0px rgba(0,0,0,0.07);
//           padding: 0px;
//           background-color: #fff;
//           border-radius: 5px;


//         }

//         .grid-block-header {
//           font-size: 18px;
//           font-weight: bold;
//           padding:10px;
//           margin-top: 0.3px;
//           background-color:#281E50 ;
//           color: #fff;
//           border-radius: 3px;        
//           height: 100%;
//           min-height:70px;
//           display: flex; 
//           align-items: center;
//         }

//         .grid-block-content {
//           display: grid;
//           width: 100%;
//           grid-template-columns: 1fr auto;
//           gap: 1px solid #ccc;
//           font-family: "Trebechut" sans serief;
//           font-size: 14px;
//           font-weight: bold;

//         }
//         .grid-block-content-traffic {
//           display: grid;
//           width: 100%;
//           grid-template-columns: 1fr 1fr 1fr auto;
//           gap: 1px solid #ccc;
//           font-family: "Trebechut" sans-serif;
//           font-size: 14px;
//           font-weight: bold;

//         }


//         .grid-item.title {
//           border-right: 1px solid #f1f1f1;
//           border-bottom: 1px solid #f1f1f1;
//           background-color: #ffffff;
//           padding: 16px;
//         }
//         .grid-item.content {
//           border-bottom: 1px solid #f1f1f1;
//           background-color: #ffffff;
//           padding: 16px;
//           min-width: 120px;
//         }
//         .grid-item.content1 {
//           border-bottom: 1px solid #f1f1f1;
//           background-color: #ffffff;
//           padding: 16px;
//           min-width: 120px;
//         }
//         .grid-item.content-traffic {
//           border-bottom: 1px solid #f1f1f1;
//           border-left: 1px solid #f1f1f1;
//           background-color: #ffffff;
//           padding: 14px;
//           min-width: 120px;

//         }


//       `}</style>

//         </div>
//     );
// };

// export default DashboardGrid1;

import React, { useEffect, useState } from "react";
import { Box, Card, CardContent, Skeleton, styled, Typography } from "@mui/material";
import { Button } from "rsuite";
import Highcharts from 'highcharts';
import HighchartsReact from 'highcharts-react-official';

import Exporting from "highcharts/modules/exporting";
import ExportData from "highcharts/modules/export-data";
import { useNavigate } from "react-router-dom";

if (typeof Highcharts === "object") {
  if (!Highcharts.Chart.prototype.exportChart) Exporting(Highcharts);
  if (!Highcharts.Chart.prototype.downloadCSV) ExportData(Highcharts);
}

const StatCard = styled(Card)({
  padding: "16px",
  borderRadius: "12px",
  boxShadow: "0 2px 8px rgba(0, 0, 0, 0.15)",
  minWidth: "280px",
  maxWidth: "320px",
  display: "flex",
  flexDirection: "column",
  justifyContent: "space-between",
  alignItems: "flex-start",
});

const StyledButton = styled(Button)(({ theme }) => ({
  backgroundColor: theme.palette.primary.main,
  color: "#FFF",
  textTransform: "none",
  fontSize: "12px",
  padding: "6px 12px",
  borderRadius: "6px",
  marginTop: "8px",
  "&:hover": {
    backgroundColor: "#f5f5f5",
  },
}));

const DashboardGrid = ({ loading, productStatus, startDate, endDate }) => {
  const navigate = useNavigate();
  const countData = productStatus?.countData || {};
  const chartData = productStatus?.chartData || {};

  const countKeys = Object.keys(countData);
  const chartKeys = Object.keys(chartData);

  const formatLabel = (key) =>
    'Total ' +
    key
      .replace(/([A-Z])/g, ' $1')
      .replace(/^./, (str) => str.toUpperCase());
  
  const stats = countKeys.map((key) => (
    {
    label: formatLabel(key),
    value: countData[key]?.[`total${key.charAt(0).toUpperCase() + key.slice(1).slice(0, -1)}`] || 0,
    redirectUrl: countData[key]?.redirect_path || null,
    redirectButton: countData[key]?.redirect_button_enabled
  }));


  // const stats = countKeys.map((key) => ({
  //   label: `Total ${key.charAt(0).toUpperCase() + key.slice(1)}`,
  //   value: countData[key]?.[`total${key.charAt(0).toUpperCase() + key.slice(1).slice(0, -1)}`] || 0,
  //   redirectUrl: countData[key]?.redirect_path || null,
  //   redirectButton: countData[key]?.redirect_button_enabled
  // }));

  const charts = chartKeys.map((key) => ({
    label: key.charAt(0).toUpperCase() + key.slice(1),
    value: chartData[key],
  }));

  const navigateTo = (url, startDate, endDate) => {
    const filterData = {
      startDate: startDate,
      endDate: endDate
    }
    navigate(url, { state: filterData });
  };

  return (
    <div>
      <Box
        display="flex"
        justifyContent="center"
        flexWrap="wrap"
        gap={10}
        mt={2}
      >
        {stats.map((stat, index) => (
          <StatCard key={index}>
            <CardContent sx={{ textAlign: "left", paddingBottom: "8px" }}>
              <Typography variant="body2" color="textSecondary">
                {stat.label}
              </Typography>
              <Typography variant="h5" fontWeight="bold">
                {loading ? "Loading..." : stat.value ?? "N/A"}
              </Typography>
            </CardContent>
            {stat.redirectButton === 1 &&
              <StyledButton size="small" onClick={() => navigateTo(stat.redirectUrl, startDate, endDate)}>View Detailed Breakdown</StyledButton>
            }
          </StatCard>
        ))}
      </Box>
      <Box mt={2} display="flex" justifyContent="center" flexWrap="wrap" style={{ gap: "10px" }}>
        {loading ? (
          <div
            style={{
              position: "relative",
              width: "100%",
              height: "180px",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              background: "rgba(255, 255, 255, 0.8)",
              zIndex: 10,
            }}
          >
            <span>
              <strong>Loading Chart...</strong>
            </span>
          </div>
        ) : (
          charts.map((chart, index) => {
            // console.log("chart", chart);
            const options = {
              chart: {
                type: chart.value?.type || 'line',
              },
              title: {
                text: chart.value?.title || chart.label,
              },
              xAxis: {
                categories: chart.value?.categories || [],
              },
              yAxis: {
                title: {
                  text: 'Count (Numbers)',
                },
              },
              series: chart.value?.series || [],
              tooltip: {
                shared: false,
                valueSuffix: '',
              },
              credits: {
                enabled: false,
              },
              colors: chart.value?.colors || [],
              exporting: {
                enabled: true,
                buttons: {
                  contextButton: {
                    menuItems: chart.value?.exportButtons || [],
                    symbol: chart.value?.exportIcon,
                    symbolSize: chart.value?.exportIconSize,
                    symbolX: chart.value?.exportIconAxisX,
                    symbolY: chart.value?.exportIconAxisY,
                  },
                },
              },
            };

            return (
              <Box key={index} height="400px" display="flex" justifyContent="center">
                <HighchartsReact
                  highcharts={Highcharts}
                  options={options}
                />
              </Box>
            );
          })
        )}
      </Box>
    </div>
  );
};

export default DashboardGrid;
