{"name": "centralsynccare", "version": "0.1.0", "private": true, "dependencies": {"@emotion/react": "^11.11.4", "@emotion/styled": "^11.11.5", "@mui/icons-material": "^5.15.20", "@mui/lab": "^5.0.0-alpha.138", "@mui/material": "^5.16.0", "@mui/x-date-pickers": "^7.23.2", "@mui/x-date-pickers-pro": "^7.23.3", "@mui/x-tree-view": "^7.22.1", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "axios": "^1.7.2", "dayjs": "^1.11.13", "fslightbox-react": "^1.7.6", "highcharts": "^12.1.2", "highcharts-react-official": "^3.2.1", "html-react-parser": "^5.1.16", "moment": "^2.30.1", "react": "^18.3.1", "react-dom": "^18.3.1", "react-google-recaptcha": "^3.1.0", "react-helmet": "^6.1.0", "react-router-dom": "^6.23.1", "react-scripts": "5.0.1", "rsuite": "^5.76.2", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}