import React, { useEffect, useState } from "react";

import {
    Box,
    Button,
    Card,
    Collapse,
    FormControl,
    Grid,
    InputLabel,
    MenuItem,
    Select,
    styled,
    TextField,
    Snackbar,
    Autocomplete,
    Checkbox,
} from "@mui/material";
import { <PERSON>d, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, FilterList } from "@mui/icons-material";
import httpclient from "../../../Utils";
import { useNavigate } from "react-router-dom";
import <PERSON><PERSON><PERSON><PERSON>t from "@mui/material/Alert";
import TableComponent from "../../../Components/TableComponent";
import EditDialog from "../../../Components/EditDialog";
import DeleteDialog from "../../../Components/DeleteDialog";
import useTokenRefresh from "../../../Hooks/useTokenRefresh";
import EditDialogUser from "../../../Components/EditDialogUser";
import Footer from "../../../Components/Footer";
import EditDialogDeveloper from "../../../Components/EditDialogDeveloper";
import CheckBoxOutlineBlankIcon from '@mui/icons-material/CheckBoxOutlineBlank';
import CheckBoxIcon from '@mui/icons-material/CheckBox';
import ViewProjectList from "./ViewProjectList";
// import DeactivateDialog from "../DeactivateDialog";
// import ResetDialog from "../ResetDialog";
// import DeleteDialog from "../DeleteDialog";

const icon = <CheckBoxOutlineBlankIcon fontSize="small" />;
const checkedIcon = <CheckBoxIcon fontSize="small" />;

const Alert = React.forwardRef(function Alert(props, ref) {
    return <MuiAlert elevation={6} ref={ref} variant="filled" {...props} />;
});

const login = localStorage.getItem("user");
const loginData = JSON.parse(login);

const columns = [
    { id: "sn", name: "SN" },
    { id: "full_name", name: "Full Name" },
    { id: "email", name: "Email" },
    { id: "mobile", name: "Mobile" },
    { id: "current_project", name: "Current Project" },
    // loginData && loginData.is_system_admin === 1 &&
    // { id: "is_system_admin", name: "System Admin?" },
    { id: "status", name: "Status" },
    { id: "projectList", name: "Project List" },
    { id: "actions", name: "Actions" },
].filter(Boolean);

const adminColumns = [
    { id: "userID", name: "ID" },
    { id: "role_id", name: "First Name" },
    { id: "full_name", name: "Last Name" },
    { id: "mobile", name: "User Name" },
    { id: "UserLevel", name: "User Level" },
    { id: "userActive", name: "Active" },
]

const superOptions = [
    { id: "edit", name: "Edit", action: "handleEdit" },
    { id: "deactivate", name: "Deactivate", action: "handleDeactivate" },
    { id: "reset", name: "Reset Password", action: "handleResetPassword" },
    { id: "delete", name: "Delete", action: "handleDelete" },
];

const adminOptions = [
    { id: "edit", name: "Edit", action: "handleEdit" },
    { id: "reset", name: "Reset Password", action: "handleResetPassword" },
]

const FilteredBox = styled(Box)(({ theme }) => ({
    background: "#f9f9f9",
    padding: "5px 10px",
    borderRadius: "5px",
    "& p": {
        margin: "0",
        marginRight: "10px",
        display: "inline-block",
        background: "#dedede",
        borderRadius: "10px",
        padding: "2px 5px",
    },
    "& svg": {
        fontSize: "15px",
        cursor: "pointer",
        position: "relative",
        top: "3px",
        background: theme.palette.primary.dark,
        color: "#fff",
        borderRadius: "50%",
        padding: "2px",
        marginLeft: "2px",
    },
}));

const Header = styled("div")(({ theme }) => ({
    "& h1": {
        color: theme.palette.primary.dark,
        margin: "0",
    },
}));

const AddButton = styled(Button)(({ theme }) => ({
    marginLeft: "10px",
    "& svg": {
        fontSize: "15px",
    },
}));

const configRowPerPage = JSON.parse(localStorage.getItem("configRowPerPage"));



const AssociatedDeveloper = (props) => {

    const { getTokenRefreshed: refresh, open: tokenOpen, overlay: overlay, setOpen: setTokenOpen, message: tokenMessage, messageState: tokenMessageState } = useTokenRefresh();

    const navigate = useNavigate();
    const [openResetDialog, setOpenResetDialog] = useState(false);
    const [viewDetails, setViewDetails] = useState({});
    const [openActiveDialog, setOpenActiveDialog] = useState(false);
    const [openDeleteDialog, setOpenDeleteDialog] = useState(false);
    const [openEditDialog, setOpenEditDialog] = useState(false);
    const [rows, setRows] = useState([]);
    const [projects, setProjects] = useState([]);
    const [open, setOpen] = useState(false);
    const [message, setMessage] = useState("");
    const [messageState, setMessageState] = useState("");
    const [projectList, setProjectList] = useState([]);
    const [openProjectListDialog, setOpenProjectListDialog] = useState(false);
    const [loading, setLoading] = useState(false);
    const [rowLoading, setRowLoading] = useState({});
    const [direction, setDirection] = useState(false);
    const [currentColumn, setCurrentColumn] = useState("");
    const [page, setPage] = useState(1);
    const [from, setFrom] = useState(1);
    const [to, setTo] = useState(
        configRowPerPage && configRowPerPage
            ? configRowPerPage && configRowPerPage
            : 20
    );

    const [rowsPerPage, setRowsPerPage] = useState(
        configRowPerPage && configRowPerPage
            ? configRowPerPage && configRowPerPage
            : 20
    );
    const [total, setTotal] = useState("");
    const [filterOpen, setFilterOpen] = useState(false);
    const [projectNames, setProjectNames] = useState([]);
    const [filterData, setFilterData] = useState({
        role_id: "",
        role_name: "",
        project: "",
        projectName: "",
        email: "",
        status: "",
        full_name: "",
        mobile: "",
        remove: false,
    });

    const [submittedData, setSubmittedData] = useState({
        role_id: "",
        role_name: "",
        project: "",
        projectName: "",
        email: "",
        status: "",
        full_name: "",
        mobile: "",
        submit: false,
    });

    // useEffect(() => {
    //   getAllAssociatedDeveloper();
    // }, []);

    useEffect(() => {
        if (
            filterData.role_id === "" &&
            filterData.role_name === "" &&
            filterData.project === "" &&
            filterData.projectName === "" &&
            filterData.email === "" &&
            filterData.status === "" &&
            filterData.full_name === "" &&
            filterData.mobile === ""
        ) {
            setSubmittedData({
                ...submittedData,
                submit: false,
            });
        }
        if (filterData.role_id === " ") filterData.role_id = "";
        if (filterData.role_name === " ") filterData.role_name = "";
        if (filterData.project === " ") filterData.project = "";
        if (filterData.projectName === " ") filterData.projectName = "";
        if (filterData.email === " ") filterData.email = "";
        if (filterData.status === " ") filterData.status = "";
        if (filterData.full_name === " ") filterData.full_name = "";
        if (filterData.mobile === " ") filterData.mobile = "";

        filterData.remove === true && handleFilter();
    }, [filterData]);

    useEffect(() => {
        let userStorage = JSON.parse(localStorage.getItem("user_filter"));
        userStorage !== null && setFilterData(userStorage);

        userStorage == null
            ? getAllAssociatedDeveloper()
            : userStorage.role_id == "" &&
                userStorage.role_name == "" &&
                userStorage.project == "" &&
                userStorage.projectName == "" &&
                userStorage.email == "" &&
                userStorage.status == "" &&
                userStorage.full_name == "" &&
                userStorage.mobile == "" &&

                userStorage.removed == false
                ? getAllAssociatedDeveloper()
                : console.log("project-associated-developers!");
    }, []);

    const getAllAssociatedDeveloper = () => {
        setLoading(true);
        httpclient.get(`project-associated-developers?pagination=${rowsPerPage}`).then(({ data }) => {
            if (data.status === 200) {
                setRows(data.data);
                setProjects(data.projects);
                setTotal(data.meta.total);
                setRowsPerPage(parseInt(data.meta.per_page));
                setPage(data.meta.current_page);
                setFrom(data.meta.from);
                setTo(data.meta.to);
                setLoading(false);
            } else {
                setOpen(true);
                setMessage(data.message);
                setMessageState("error");
                setLoading(false);
            }

        }).catch((err) => {
            if (err.response.status === 401) {
                refresh();
                setOpen(tokenOpen);
                setMessage(tokenMessage);
                setMessageState("error");
            } else if (err.response.status === 422) {
                const errorMessages = Object.values(err.response.data.errors).flat();
                setOpen(true);
                setMessage(errorMessages);
                setMessageState("error");
                setLoading(false);
            } else if (err.response.status === 400) {
                const errorMessages = Object.values(err.response.data.errors).flat();
                setOpen(true);
                setMessage(errorMessages);
                setMessageState("error");
                setLoading(false);

            } else {
                setOpen(true);
                setMessage(err.response.data.message);
                setMessageState("error");
                setLoading(false);
            }
        })

    };

    const hadleFilterOpen = () => {
        setFilterOpen((prev) => !prev);
    };

    const handleChangeFilter = (e) => {
        const { name, value } = e.target;
        setFilterData({
            ...filterData,
            [name]: value,
            remove: false,
        });
    };

    const handleFilter = () => {
        setSubmittedData({
            ...submittedData,
            role_id: filterData.role_id,
            role_name: filterData.role_name,
            project: filterData.project,
            projectName: filterData.projectName,
            email: filterData.email,
            status: filterData.status,
            full_name: filterData.full_name,
            mobile: filterData.mobile,

            submit: true,
        });
        filterData.remove = true;
        localStorage.setItem("user_filter", JSON.stringify(filterData));
        setLoading(true);
        if (
            filterData.role_id ||
            filterData.role_name ||
            filterData.project ||
            filterData.projectName ||
            filterData.email ||
            filterData.status ||
            filterData.full_name ||
            filterData.mobile
        ) {
            const idParams = filterData.project !== "" ? filterData.project
                .map((id, index) => `filters[project_id][$in][${index}]=${id}`)
                .join("&") : `filters[project_id][$in][0]=`;
            httpclient
                .get(
                    `project-associated-developers?${idParams}&filters[full_name][$contains]=${filterData.full_name
                    //}&filters[role_id][$eq]=${filterData.role_id
                    }&filters[status][$eq]=${filterData.status
                    }&filters[email][$eq]=${filterData.email
                    }&filters[mobile][$eq]=${filterData.mobile}&pagination=${rowsPerPage}&page=${1}`
                )
                .then(({ data }) => {
                    if (data.status === 200) {
                        setRows(data.data);
                        setProjects(data.projects);
                        setTotal(data.meta.total);
                        setRowsPerPage(data.meta.per_page);
                        setPage(data.meta.current_page);
                        setFrom(data.meta.from);
                        setTo(data.meta.to);
                        setLoading(false);
                    } else {
                        setOpen(true);
                        setMessage(data.message);
                        setMessageState("error");
                        setLoading(false);
                    }

                }).catch((err) => {
                    if (err.response.status === 401) {
                        refresh();
                        setOpen(tokenOpen);
                        setMessage(tokenMessage);
                        setMessageState("error");
                    } else if (err.response.status === 422) {
                        const errorMessages = Object.values(err.response.data.errors).flat();
                        setOpen(true);
                        setMessage(errorMessages);
                        setMessageState("error");
                        setLoading(false);
                    } else if (err.response.status === 400) {
                        const errorMessages = Object.values(err.response.data.errors).flat();
                        setOpen(true);
                        setMessage(errorMessages);
                        setMessageState("error");
                        setLoading(false);

                    } else {
                        setOpen(true);
                        setMessage(err.response.data.message);
                        setMessageState("error");
                        setLoading(false);
                    }
                })

        } else {
            getAllAssociatedDeveloper();
        }
    };

    const handleRemove = (data) => {
        if (data === "role_id") {
            filterData.role_name = "";
            submittedData.role_name = "";
        }
        if (data === "project") {
            filterData.projectName = "";
            submittedData.projectName = "";
            setProjectNames([]);
        }
        setFilterData({
            ...filterData,
            [data]: "",
            remove: true,
        });

        setSubmittedData({
            ...submittedData,
            [data]: "",
        });
    };

    const handleSort = (column) => {
        setDirection((prevDirection) => !prevDirection);
        setCurrentColumn(column);
        setLoading(true);
        const idParams = filterData.project !== "" ? filterData.project
            .map((id, index) => `filters[project_id][$in][${index}]=${id}`)
            .join("&") : `filters[project_id][$in][0]=`;
        submittedData.submit
            ? httpclient
                .get(
                    `project-associated-developers?${idParams}&filters[full_name][$contains]=${filterData.full_name
                    //}&filters[role_id][$eq]=${filterData.role_id
                    }&filters[status][$eq]=${filterData.status
                    }&filters[email][$eq]=${filterData.email
                    }&filters[mobile][$eq]=${filterData.mobile
                    }&sort[0]=${column}:${!direction ? "asc" : "desc"
                    }&pagination=${rowsPerPage}&page=${page}`
                )
                .then(({ data }) => {
                    if (data.status === 200) {
                        setRows(data.data);
                        setTotal(data.meta.total);
                        setRowsPerPage(parseInt(data.meta.per_page));
                        setPage(data.meta.current_page);
                        setFrom(data.meta.from);
                        setTo(data.meta.to);
                        setLoading(false);
                    } else {
                        setOpen(true);
                        setMessage(data.message);
                        setMessageState("error");
                        setLoading(false);
                    }

                }).catch((err) => {
                    if (err.response.status === 401) {
                        refresh();
                        setOpen(tokenOpen);
                        setMessage(tokenMessage);
                        setMessageState("error");
                    } else if (err.response.status === 422) {
                        const errorMessages = Object.values(err.response.data.errors).flat();
                        setOpen(true);
                        setMessage(errorMessages);
                        setMessageState("error");
                        setLoading(false);
                    } else if (err.response.status === 400) {
                        const errorMessages = Object.values(err.response.data.errors).flat();
                        setOpen(true);
                        setMessage(errorMessages);
                        setMessageState("error");
                        setLoading(false);

                    } else {
                        setOpen(true);
                        setMessage(err.response.data.message);
                        setMessageState("error");
                        setLoading(false);
                    }
                })
            : httpclient
                .get(
                    `project-associated-developers?sort[0]=${column}:${!direction ? "asc" : "desc"
                    }&pagination=${rowsPerPage}`
                )
                .then(({ data }) => {
                    if (data.status === 200) {
                        setRows(data.data);
                        setTotal(data.meta.total);
                        setRowsPerPage(parseInt(data.meta.per_page));
                        setPage(data.meta.current_page);
                        setFrom(data.meta.from);
                        setTo(data.meta.to);
                        setLoading(false);
                    } else {
                        setOpen(true);
                        setMessage(data.message);
                        setMessageState("error");
                        setLoading(false);
                    }

                }).catch((err) => {
                    if (err.response.status === 401) {
                        refresh();
                        setOpen(tokenOpen);
                        setMessage(tokenMessage);
                        setMessageState("error");
                    } else if (err.response.status === 422) {
                        const errorMessages = Object.values(err.response.data.errors).flat();
                        setOpen(true);
                        setMessage(errorMessages);
                        setMessageState("error");
                        setLoading(false);
                    } else if (err.response.status === 400) {
                        const errorMessages = Object.values(err.response.data.errors).flat();
                        setOpen(true);
                        setMessage(errorMessages);
                        setMessageState("error");
                        setLoading(false);

                    } else {
                        setOpen(true);
                        setMessage(err.response.data.message);
                        setMessageState("error");
                        setLoading(false);
                    }
                })
    };

    const handleChangePage = (e, page) => {
        setLoading(true);
        const idParams = filterData.project !== "" ? filterData.project
            .map((id, index) => `filters[project_id][$in][${index}]=${id}`)
            .join("&") : `filters[project_id][$in][0]=`;
        submittedData.submit
            ? httpclient
                .get(
                    `project-associated-developers?${idParams}&filters[full_name][$contains]=${filterData.full_name
                    //}&filters[role_id][$eq]=${filterData.role_id
                    }&filters[status][$eq]=${filterData.status
                    }&filters[email][$eq]=${filterData.email
                    }&filters[mobile][$eq]=${filterData.mobile
                    }${currentColumn ? `&sort[0]=${currentColumn}:${direction ? "asc" : "desc"}` : ''
                    }&pagination=${rowsPerPage}&page=${page}`
                )
                .then(({ data }) => {
                    if (data.status === 200) {
                        setRows(data.data);
                        setTotal(data.meta.total);
                        setRowsPerPage(parseInt(data.meta.per_page));
                        setPage(data.meta.current_page);
                        setFrom(data.meta.from);
                        setTo(data.meta.to);
                        setLoading(false);
                    } else {
                        setOpen(true);
                        setMessage(data.message);
                        setMessageState("error");
                        setLoading(false);
                    }

                }).catch((err) => {
                    if (err.response.status === 401) {
                        refresh();
                        setOpen(tokenOpen);
                        setMessage(tokenMessage);
                        setMessageState("error");
                    } else if (err.response.status === 422) {
                        const errorMessages = Object.values(err.response.data.errors).flat();
                        setOpen(true);
                        setMessage(errorMessages);
                        setMessageState("error");
                        setLoading(false);
                    } else if (err.response.status === 400) {
                        const errorMessages = Object.values(err.response.data.errors).flat();
                        setOpen(true);
                        setMessage(errorMessages);
                        setMessageState("error");
                        setLoading(false);

                    } else {
                        setOpen(true);
                        setMessage(err.response.data.message);
                        setMessageState("error");
                        setLoading(false);
                    }
                })
            : httpclient
                .get(
                    `project-associated-developers?pagination=${rowsPerPage}&page=${page}`
                )
                .then(({ data }) => {
                    if (data.status === 200) {
                        setRows(data.data);
                        setTotal(data.meta.total);
                        setRowsPerPage(parseInt(data.meta.per_page));
                        setPage(data.meta.current_page);
                        setFrom(data.meta.from);
                        setTo(data.meta.to);
                        setLoading(false);
                    } else {
                        setOpen(true);
                        setMessage(data.message);
                        setMessageState("error");
                        setLoading(false);
                    }

                }).catch((err) => {
                    if (err.response.status === 401) {
                        refresh();
                        setOpen(tokenOpen);
                        setMessage(tokenMessage);
                        setMessageState("error");
                    } else if (err.response.status === 422) {
                        const errorMessages = Object.values(err.response.data.errors).flat();
                        setOpen(true);
                        setMessage(errorMessages);
                        setMessageState("error");
                        setLoading(false);
                    } else if (err.response.status === 400) {
                        const errorMessages = Object.values(err.response.data.errors).flat();
                        setOpen(true);
                        setMessage(errorMessages);
                        setMessageState("error");
                        setLoading(false);

                    } else {
                        setOpen(true);
                        setMessage(err.response.data.message);
                        setMessageState("error");
                        setLoading(false);
                    }
                })
    };

    const handleChangeRowsPerPage = (event) => {
        setRowsPerPage(+event.target.value);
        setLoading(true);

        localStorage.setItem("configRowPerPage", event.target.value);
        const idParams = filterData.project !== "" ? filterData.project
            .map((id, index) => `filters[project_id][$in][${index}]=${id}`)
            .join("&") : `filters[project_id][$in][0]=`;
        submittedData.submit
            ? httpclient
                .get(
                    `project-associated-developers?${idParams}&filters[full_name][$contains]=${filterData.full_name
                    //}&filters[role_id][$eq]=${filterData.role_id
                    }&filters[status][$eq]=${filterData.status
                    }&filters[email][$eq]=${filterData.email
                    }&filters[mobile][$eq]=${filterData.mobile
                    }${currentColumn ? `&sort[0]=${currentColumn}:${direction ? "asc" : "desc"}` : ''
                    }&pagination=${+event.target.value}&page=${page}`
                )
                .then(({ data }) => {
                    setLoading(true);
                    if (data.status === 200) {
                        setRows(data.data);
                        setTotal(data.meta.total);
                        setRowsPerPage(parseInt(data.meta.per_page));
                        setPage(data.meta.current_page);
                        setFrom(data.meta.from);
                        setTo(data.meta.to);
                        setLoading(false);
                    } else {
                        setOpen(true);
                        setMessage(data.message);
                        setMessageState("error");
                        setLoading(false);
                    }

                }).catch((err) => {
                    if (err.response.status === 401) {
                        refresh();
                        setOpen(tokenOpen);
                        setMessage(tokenMessage);
                        setMessageState("error");
                    } else if (err.response.status === 422) {
                        const errorMessages = Object.values(err.response.data.errors).flat();
                        setOpen(true);
                        setMessage(errorMessages);
                        setMessageState("error");
                        setLoading(false);
                    } else if (err.response.status === 400) {
                        const errorMessages = Object.values(err.response.data.errors).flat();
                        setOpen(true);
                        setMessage(errorMessages);
                        setMessageState("error");
                        setLoading(false);

                    } else {
                        setOpen(true);
                        setMessage(err.response.data.message);
                        setMessageState("error");
                        setLoading(false);
                    }
                })
            : httpclient
                .get(
                    `project-associated-developers?pagination=${+event
                        .target.value}&page=${1}`
                )
                .then(({ data }) => {
                    setLoading(true);
                    if (data.status === 200) {
                        setRows(data.data);
                        setTotal(data.meta.total);
                        setRowsPerPage(parseInt(data.meta.per_page));
                        setFrom(data.meta.from);
                        setTo(data.meta.to);
                        setPage(data.meta.current_page);
                        setLoading(false);
                    } else {
                        setOpen(true);
                        setMessage(data.message);
                        setMessageState("error");
                        setLoading(false);
                    }

                }).catch((err) => {
                    if (err.response.status === 401) {
                        refresh();
                        setOpen(tokenOpen);
                        setMessage(tokenMessage);
                        setMessageState("error");
                    } else if (err.response.status === 422) {
                        const errorMessages = Object.values(err.response.data.errors).flat();
                        setOpen(true);
                        setMessage(errorMessages);
                        setMessageState("error");
                        setLoading(false);
                    } else if (err.response.status === 400) {
                        const errorMessages = Object.values(err.response.data.errors).flat();
                        setOpen(true);
                        setMessage(errorMessages);
                        setMessageState("error");
                        setLoading(false);

                    } else {
                        setOpen(true);
                        setMessage(err.response.data.message);
                        setMessageState("error");
                        setLoading(false);
                    }
                })
    };


    const handleAddNew = () => {
        setOpenEditDialog(true)
    };

    const handleEdit = (row) => {
        setOpenEditDialog(true)
        setViewDetails(row);
    };

    const handleViewProjectList = (row) => {
        setOpenProjectListDialog(true)
        setProjectList(row.project_list);
    };
    const handleCloseProjectList = () => {
        setOpenProjectListDialog(false)
        setProjectList([]);
    };

    const sendEdit = (call, formData) => {
        if (call.open === false) {

            setOpenEditDialog(false);
            setViewDetails({});
        }
        if (call.success === true) {
            viewDetails.id ? (
                httpclient
                    .put(`project-associated-developers/${viewDetails.id}`, formData)
                    .then(({ data }) => {
                        if (data.status === 200) {
                            setOpen(true);
                            setMessageState("success");
                            setMessage(data.message);
                            setOpenActiveDialog(false);
                            setViewDetails({});
                            getAllAssociatedDeveloper();
                        }
                        else {
                            setOpen(true);
                            setMessage(data.message);
                            setMessageState("error");
                            setLoading(false);
                        }

                    }).catch((err) => {
                        if (err.response.status === 401) {
                            refresh();
                            setOpen(tokenOpen);
                            setMessage(tokenMessage);
                            setMessageState("error");
                        } else if (err.response.status === 422) {
                            const errorMessages = Object.values(err.response.data.errors).flat();
                            setOpen(true);
                            setMessage(errorMessages);
                            setMessageState("error");
                            setLoading(false);
                        } else if (err.response.status === 400) {
                            const errorMessages = Object.values(err.response.data.errors).flat();
                            setOpen(true);
                            setMessage(errorMessages);
                            setMessageState("error");
                            setLoading(false);

                        } else {
                            setOpen(true);
                            setMessage(err.response.data.message);
                            setMessageState("error");
                            setLoading(false);
                        }
                    })
            ) :
                httpclient
                    .post(`project-associated-developers`, formData)
                    .then(({ data }) => {
                        if (data.status === 200) {
                            setOpen(true);
                            setMessageState("success");
                            setMessage(data.message);
                            setOpenActiveDialog(false);
                            setViewDetails({});
                            getAllAssociatedDeveloper();
                        } else {
                            setOpen(true);
                            setMessage(data.message);
                            setMessageState("error");
                            setLoading(false);
                        }

                    }).catch((err) => {
                        if (err.response.status === 401) {
                            refresh();
                            setOpen(tokenOpen);
                            setMessage(tokenMessage);
                            setMessageState("error");
                        } else if (err.response.status === 422) {
                            const errorMessages = Object.values(err.response.data.errors).flat();
                            setOpen(true);
                            setMessage(errorMessages);
                            setMessageState("error");
                            setLoading(false);
                        } else if (err.response.status === 400) {
                            const errorMessages = Object.values(err.response.data.errors).flat();
                            setOpen(true);
                            setMessage(errorMessages);
                            setMessageState("error");
                            setLoading(false);

                        } else {
                            setOpen(true);
                            setMessage(err.response.data.message);
                            setMessageState("error");
                            setLoading(false);
                        }

                    })
        }
    };


    const handleDelete = (row) => {
        setOpenDeleteDialog(true);
        setViewDetails(row)
    };

    const sendDelete = (call, formData) => {
        if (call.open === false) {
            setOpenDeleteDialog(false)
            setViewDetails({})
        }
        if (call.success === true) {
            httpclient
                .delete(`project-associated-developers/${viewDetails.id}`, formData)
                .then(({ data }) => {
                    if (data.status === 200) {
                        setOpen(true);
                        setMessageState("success");
                        setMessage(data.message);
                        setOpenDeleteDialog(false);
                        setViewDetails({});
                        getAllAssociatedDeveloper();
                    } else {
                        setOpen(true);
                        setMessage(data.message);
                        setMessageState("error");
                        setLoading(false);
                    }

                }).catch((err) => {
                    if (err.response.status === 401) {
                        refresh();
                        setOpen(tokenOpen);
                        setMessage(tokenMessage);
                        setMessageState("error");
                    } else if (err.response.status === 422) {
                        const errorMessages = Object.values(err.response.data.errors).flat();
                        setOpen(true);
                        setMessage(errorMessages);
                        setMessageState("error");
                        setLoading(false);
                    } else if (err.response.status === 400) {
                        const errorMessages = Object.values(err.response.data.errors).flat();
                        setOpen(true);
                        setMessage(errorMessages);
                        setMessageState("error");
                        setLoading(false);

                    } else {
                        setOpen(true);
                        setMessage(err.response.data.message);
                        setMessageState("error");
                        setLoading(false);
                    }
                })
        }
    }

    // Handle API call to update row status
    const updateRowStatus = (rowId) => {
        setRowLoading((prev) => ({ ...prev, [rowId.id]: true }));
        httpclient
            .post(`project-associated-developers/${rowId.id}/status`)
            .then(({ data }) => {
                if (data.status === 200) {
                    setRowLoading((prev) => ({ ...prev, [rowId.id]: false }));
                    setOpen(true);
                    setMessageState("success");
                    setMessage(data.message);
                    setViewDetails({});
                    getAllAssociatedDeveloper();
                } else {
                    setRowLoading((prev) => ({ ...prev, [rowId.id]: false }));
                    setOpen(true);
                    setMessageState("error");
                    setMessage(data.error || data.message);
                }
            }).catch((err) => {
                if (err.response.status === 401) {
                    refresh();
                    setOpen(tokenOpen);
                    setMessage(tokenMessage);
                    setMessageState("error");
                } else if (err.response.status === 422) {
                    const errorMessages = Object.values(err.response.data.errors).flat();
                    setOpen(true);
                    setMessage(errorMessages);
                    setMessageState("error");
                    setRowLoading((prev) => ({ ...prev, [rowId.id]: false }));
                } else if (err.response.status === 400) {
                    const errorMessages = Object.values(err.response.data.errors).flat();
                    setOpen(true);
                    setMessage(errorMessages);
                    setMessageState("error");
                    setRowLoading((prev) => ({ ...prev, [rowId.id]: false }));

                } else {
                    setOpen(true);
                    setMessage(err.response.data.message);
                    setMessageState("error");
                    setRowLoading((prev) => ({ ...prev, [rowId.id]: false }));
                }
            }).finally(() => {
                setRowLoading((prev) => ({ ...prev, [rowId.id]: false }));
            });
    };

    const currentChange = (value, row) => {

        if (value === "allow_update") {
            handleEdit(row);
        }

        if (value === "allow_delete") {
            handleDelete(row);
        }
    };

    const handleChangeRole = (value) => {

        setFilterData({
            ...filterData,
            role_id: value !== null ? value.id : "",
            role_name: value !== null ? value.name : "",
            remove: false,
        });
    };

    const handleChangeProject = (value) => {
        setProjectNames(value);
        const ids = value.map((item) => item.id);
        const names = value.map((item) => item.name).join(", ");

        setFilterData({
            ...filterData,
            project: ids,
            projectName: names,
            remove: false,
        });
    };

    const handleChange = (e) => {
        const { name, value } = e.target;
        if (name === "project") {
            setFilterData({
                ...filterData,
                projectName: value,
                remove: false,
            });
        }
        if (name === "role_id") {
            setFilterData({
                ...filterData,
                role_name: value,
                remove: false,
            });
        }

    };

    const handleClose = (event, reason) => {
        if (reason === "clickaway") {
            return;
        }
        setOpen(false);
        setTokenOpen(false);
    };



    return (
        <div>
            <Grid container spacing={2}>
                <Grid item md={8} xs={12}>
                    <Header>
                        <h1>List Associated Developer</h1>
                    </Header>
                </Grid>
                <Grid
                    item
                    md={4}
                    xs={12}
                    display="flex"
                    alignItems="center"
                    justifyContent="flex-end"
                >
                    <Button color="primary" variant="contained" onClick={hadleFilterOpen}>
                        Filter <FilterList style={{ marginLeft: "5px" }} fontSize="small" />
                    </Button>

                    {props?.permissions?.some((pre) => pre.name === "allow_create" && pre.status === 1) &&
                        <AddButton
                            color="primary"
                            variant="contained"
                            onClick={handleAddNew}
                        >
                            <Add style={{ marginRight: "5px" }} fontSize="small" /> Add Developer
                        </AddButton>
                    }
                </Grid>

                {/* Filter */}
                <Grid item xs={12}>
                    <Collapse in={filterOpen}>
                        <Card>
                            <Box p={4}>
                                <Grid container spacing={2}>

                                    {/* <Grid item xs={12} md={4}>
                                        <InputLabel>Roles</InputLabel>
                                        <Autocomplete
                                            disablePortal
                                            id="role_id"
                                            options={roles}
                                            onChange={(event, newValue) => {
                                                handleChangeRole(newValue);
                                            }}
                                            inputValue={filterData.role_name}
                                            getOptionLabel={(option) => option.name}
                                            renderOption={(props, option, index) => { return <li {...props} key={option.role_name}>{option.name}</li> }}
                                            renderInput={(params) => (
                                                <TextField
                                                    {...params}
                                                    onChange={handleChange}
                                                    onKeyDown={(e) => {
                                                        if (e.key === "Enter") handleFilter();
                                                    }}
                                                    value={filterData.role_name}
                                                    variant="outlined"
                                                    name="role_id"
                                                // label="Company"
                                                />
                                            )}
                                        />
                                    </Grid> */}

                                    <Grid item xs={12} md={4}>
                                        <InputLabel>Project</InputLabel>
                                        <Autocomplete
                                            options={projects}
                                            onChange={(event, newValue) => {
                                                handleChangeProject(newValue);
                                            }}
                                            multiple
                                            id="checkboxes-tags-demo"
                                            disableCloseOnSelect
                                            value={projectNames}
                                            getOptionLabel={(option) => option.name}
                                            renderOption={(props, option, { selected }) => (
                                                <li {...props}>
                                                    <Checkbox
                                                        icon={icon}
                                                        checkedIcon={checkedIcon}
                                                        style={{ marginRight: 8 }}
                                                        checked={selected}
                                                    />
                                                    {option.name}
                                                </li>
                                            )}
                                            renderInput={(params) => (
                                                <TextField
                                                    {...params}
                                                    onChange={handleChange}
                                                    onKeyDown={(e) => {
                                                        if (e.key === "Enter") handleFilter();
                                                    }}
                                                    value={filterData.projectName}
                                                    variant="outlined"
                                                    name="project"
                                                // label="Company"
                                                />
                                            )}
                                        />

                                    </Grid>

                                    <Grid item xs={12} md={4}>
                                        <InputLabel>Full Name</InputLabel>
                                        <TextField
                                            variant="outlined"
                                            name="full_name"
                                            value={filterData.full_name}
                                            onChange={handleChangeFilter}
                                            onKeyDown={e => { if (e.key === "Enter") handleFilter() }}
                                            fullWidth
                                        />
                                    </Grid>
                                    <Grid item xs={12} md={4}>
                                        <InputLabel>Status</InputLabel>
                                        <FormControl fullWidth>
                                            <Select
                                                value={filterData.status}
                                                name="status"
                                                onChange={handleChangeFilter}
                                            >
                                                <MenuItem value={""}>Select</MenuItem>
                                                <MenuItem value={"1"}>Active</MenuItem>
                                                <MenuItem value={"0"}>Inactive</MenuItem>

                                            </Select>
                                        </FormControl>

                                    </Grid>
                                    <Grid item xs={12} md={4}>
                                        <InputLabel>Email</InputLabel>
                                        <TextField
                                            variant="outlined"
                                            name="email"
                                            value={filterData.email}
                                            onChange={handleChangeFilter}
                                            onKeyDown={e => { if (e.key === "Enter") handleFilter() }}
                                            fullWidth
                                        />
                                    </Grid>
                                    <Grid item xs={12} md={4}>
                                        <InputLabel>Mobile</InputLabel>
                                        <TextField
                                            variant="outlined"
                                            name="mobile"
                                            value={filterData.mobile}
                                            onChange={handleChangeFilter}
                                            onKeyDown={e => { if (e.key === "Enter") handleFilter() }}
                                            fullWidth
                                        />
                                    </Grid>



                                    <Grid item xs={12}>
                                        <Box textAlign={"right"}>
                                            <Button
                                                variant="contained"
                                                color="primary"
                                                onClick={handleFilter}
                                            >
                                                Filter{" "}
                                                <ArrowForward
                                                    fontSize="small"
                                                    style={{ marginLeft: "5px" }}
                                                />
                                            </Button>
                                        </Box>
                                    </Grid>
                                </Grid>
                            </Box>
                        </Card>
                    </Collapse>
                </Grid>

                {submittedData.role_id ||
                    submittedData.email ||
                    submittedData.status ||
                    submittedData.project ||
                    submittedData.full_name ||
                    submittedData.mobile ? (
                    <Grid item xs={12}>
                        <FilteredBox>
                            <span>Filtered: </span>
                            {submittedData.project && (
                                <p>
                                    <span>Project: {submittedData.projectName}</span>
                                    <Close
                                        fontSize="small"
                                        onClick={() => handleRemove("project")}
                                    />
                                </p>
                            )}
                            {submittedData.role_id && (
                                <p>
                                    <span>Role: {submittedData.role_name}</span>
                                    <Close
                                        fontSize="small"
                                        onClick={() => handleRemove("role_id")}
                                    />
                                </p>
                            )}
                            {submittedData.email && (
                                <p>
                                    <span>Email: {submittedData.email}</span>
                                    <Close
                                        fontSize="small"
                                        onClick={() => handleRemove("email")}
                                    />
                                </p>
                            )}
                            {submittedData.status && (
                                <p>
                                    <span>Status: {submittedData.status === "1" ? "Active" : "Inactive"}</span>
                                    <Close
                                        fontSize="small"
                                        onClick={() => handleRemove("status")}
                                    />
                                </p>
                            )}

                            {submittedData.full_name && (
                                <p>
                                    <span>Name: {submittedData.full_name}</span>
                                    <Close
                                        fontSize="small"
                                        onClick={() => handleRemove("full_name")}
                                    />
                                </p>
                            )}
                            {submittedData.mobile && (
                                <p>
                                    <span>Status: {submittedData.mobile}</span>
                                    <Close
                                        fontSize="small"
                                        onClick={() => handleRemove("mobile")}
                                    />
                                </p>
                            )}

                        </FilteredBox>
                    </Grid>
                ) : (
                    <Box></Box>
                )}
                {/* Filter */}

                <Grid item xs={12}>
                    <TableComponent
                        name={"User"}
                        columns={columns}
                        rows={rows}
                        sort={true}
                        handleSort={handleSort}
                        updateRowStatus={updateRowStatus}
                        setRowLoading={setRowLoading}
                        rowLoading={rowLoading}
                        props={props}
                        options={props?.permissions}
                        currentChange={currentChange}
                        loading={loading}
                        direction={direction}
                        currentColumn={currentColumn}
                        handleChangeRowsPerPage={handleChangeRowsPerPage}
                        handleViewProjectList={handleViewProjectList}
                        handleChangePage={handleChangePage}
                        page={page}
                        total={total && total}
                        fromTable={from}
                        toTable={to}
                        rowsPerPage={rowsPerPage}
                    />
                </Grid>
                <Footer overlay={overlay || props.overlayNew} />
            </Grid>



            {openDeleteDialog && <DeleteDialog name={"Associate Developer"} viewDetails={viewDetails} sendDelete={sendDelete} />}

            {openEditDialog && (
                <EditDialogDeveloper
                    viewDetails={viewDetails}
                    projects={projects}
                    sendEdit={sendEdit}
                />
            )}

            {openProjectListDialog && (
                <ViewProjectList
                    projectList={projectList}
                    handleCloseProjectList={handleCloseProjectList}
                />
            )}

            <Snackbar
                autoHideDuration={3000}
                anchorOrigin={{ vertical: "top", horizontal: "right" }}
                open={open || tokenOpen}
                onClose={handleClose}
            >
                <Alert
                    onClose={handleClose}
                    severity={messageState || tokenMessageState}
                    sx={{ width: "100%" }}
                >
                    {message || tokenMessage}
                </Alert>
            </Snackbar>
        </div>
    );
};

export default AssociatedDeveloper;
