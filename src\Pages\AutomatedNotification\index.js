import { Box, Button, Grid, <PERSON>nack<PERSON>, styled, Card, Collapse, TextField, InputLabel, FormControl, Select, MenuItem, Chip, Autocomplete, Checkbox, Dialog, DialogTitle, DialogContent, DialogActions, Typography, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper, TablePagination, CircularProgress, IconButton, Switch, Menu, Tooltip } from '@mui/material';
import React, { useEffect, useState, useCallback } from 'react';

import { Add, ArrowForward, FilterList, Close, KeyboardArrowUp, KeyboardArrowDown, UnfoldMore, Edit, Delete, Visibility, Tune } from '@mui/icons-material';
import httpclient from '../../Utils';
import MuiAlert from "@mui/material/Alert";
import EditAutomatedNotification from '../../Components/EditAutomatedNotification';
import DeleteDialog from '../../Components/DeleteDialog';
import moment from 'moment';
import CheckBoxOutlineBlankIcon from '@mui/icons-material/CheckBoxOutlineBlank';
import CheckBoxIcon from '@mui/icons-material/CheckBox';

// Styled components for table
const StyledTableCell = styled(TableCell)(({ theme }) => ({
    fontWeight: 'bold',
    backgroundColor: 'primary.main',
    '&.sortable': {
        cursor: 'pointer',
        userSelect: 'none',
    },
}));

const Alert = React.forwardRef(function Alert(props, ref) {
    return <MuiAlert elevation={6} ref={ref} variant="filled" {...props} />;
});

const Header = styled("div")(({ theme }) => ({
    "& h1": {
        color: theme.palette.primary.dark,
        margin: "0",
    },
}));

const AddButton = styled(Button)(({ theme }) => ({
    marginLeft: "10px",
    "& svg": {
        fontSize: "15px",
    },
}));

const checkboxIcon = <CheckBoxOutlineBlankIcon fontSize="small" />;
const checkboxCheckedIcon = <CheckBoxIcon fontSize="small" />;

const AutomatedPushNotification = (props) => {
    const permissions = props?.permissions


    const [rows, setRows] = useState([]);
    const [openDeleteDialog, setOpenDeleteDialog] = useState(false);
    const [openEditDialog, setOpenEditDialog] = useState(false);
    const [dialogKey, setDialogKey] = useState(0); // Force dialog re-render
    const [viewDetails, setViewDetails] = useState({});
    const [open, setOpen] = useState(false);
    const [message, setMessage] = useState("");
    const [messageState, setMessageState] = useState("success");

    // Add state for message view modal
    const [openMessageModal, setOpenMessageModal] = useState(false);
    const [messageDetails, setMessageDetails] = useState({});

    // Add state for OptionMenu
    const [anchorEl, setAnchorEl] = useState(null);
    const [selectedRow, setSelectedRow] = useState(null);

    const [loading, setLoading] = useState(false);
    const [rowLoading, setRowLoading] = useState({});
    const [isAddNew, setIsAddNew] = useState(false);
    const [notificationTypes, setNotificationTypes] = useState([]);
    const [showActiveFilters, setShowActiveFilters] = useState(false);

    // Helper function to migrate old filter format to new format
    const migrateFilterFormat = (savedFilters) => {
        if (!savedFilters) return { notifyTypeIds: [], status: '' };

        const parsed = JSON.parse(savedFilters);

        // If old format with notifyTypeId (single), convert to notifyTypeIds (array)
        if (parsed.notifyTypeId && !parsed.notifyTypeIds) {
            return {
                notifyTypeIds: parsed.notifyTypeId ? [parsed.notifyTypeId] : [],
                status: parsed.status || ''
            };
        }

        // Ensure notifyTypeIds is always an array
        return {
            notifyTypeIds: parsed.notifyTypeIds || [],
            status: parsed.status || ''
        };
    };

    // Add filters state - similar to History component
    const [filters, setFilters] = useState(() => {
        const savedFilters = localStorage.getItem('automated_notification_filter');
        return migrateFilterFormat(savedFilters);
    });

    // Add activeFilters state to track applied filters
    const [activeFilters, setActiveFilters] = useState(() => {
        const savedFilters = localStorage.getItem('automated_notification_filter');
        return migrateFilterFormat(savedFilters);
    });

    // Add filter UI state
    const [filterOpen, setFilterOpen] = useState(false);

    // Add pagination states
    const [page, setPage] = useState(0);
    const [total, setTotal] = useState(0);
    const [from, setFrom] = useState(0);
    const [to, setTo] = useState(0);

    const configRowPerPage = localStorage.getItem("configRowPerPage");
    const [rowsPerPage, setRowsPerPage] = useState(
        configRowPerPage ? parseInt(configRowPerPage) : 20
    );

    // Multiple column sorting state
    const [sortConfig, setSortConfig] = useState([]);

    const userData = JSON.parse(localStorage.getItem("user"));

    const columns = [
        { id: "title", name: "Title", sortable: true, apiField: "title" },
        { id: "notify_type", name: "Notification Type", sortable: true, apiField: "notify_type_id" },
        { id: "status", name: "Status", sortable: false },
        { id: "message", name: "Message", sortable: false }, // Excluded from sorting as requested
        { id: "last_pushed_date", name: "Last Pushed Date", sortable: true, apiField: "last_pushed_date" },
        { id: "created_at", name: "Created At", sortable: true, apiField: "created_at" },
        { id: "actions", name: "Actions", sortable: false },
    ];



    useEffect(() => {
        // Check if there are any active filters in localStorage
        const savedFilters = localStorage.getItem('automated_notification_filter');
        if (savedFilters) {
            const migratedFilters = migrateFilterFormat(savedFilters);
            // Show filter chips if any filter is active
            if (migratedFilters.notifyTypeIds?.length > 0 || migratedFilters.status) {
                setShowActiveFilters(true);
                setActiveFilters(migratedFilters);
                setFilters(migratedFilters); // Also update the current filters
                // Save the migrated format back to localStorage
                localStorage.setItem('automated_notification_filter', JSON.stringify(migratedFilters));
            }
        }

        fetchNotificationTypes();
    }, []); // Run only once on component mount


    // Fetch notification types for the dropdown
    const fetchNotificationTypes = async () => {
        try {
            const response = await httpclient.get("request-response?requestName=lightspeed/notification/types");
            if (response.data?.data) {
                setNotificationTypes(response.data.data);
            }
        } catch (error) {
            console.error("Failed to fetch notification types", error);
        }
    };

    const getNotificationList = useCallback(async (page = 0, rowsPerPage = 20) => {
        try {
            setLoading(true);
            let url = `request-response?requestName=lightspeed/automated/notifications&page=${page + 1}&per_page=${rowsPerPage}`;

            // Use activeFilters (from localStorage) instead of current filters
            const params = [];

            if (activeFilters.notifyTypeIds?.length > 0) {
                activeFilters.notifyTypeIds.forEach((typeId, index) => {
                    params.push(`filters[notify_type_id][$in][${index}]=${encodeURIComponent(typeId)}`);
                });
            }

            if (activeFilters.status) {
                params.push(`filters[status][$eq]=${activeFilters.status}`);
            }

            // Add sorting parameters (multiple column sorting)
            if (sortConfig.length > 0) {
                sortConfig.forEach((sort, index) => {
                    params.push(`sort[${index}]=${sort.field}:${sort.direction}`);
                });
            }

            if (params.length) url += `&${params.join('&')}`;


            const response = await httpclient.get(url); // Use .get() method explicitly



            const apiData = response.data?.data || [];
            const meta = response.data?.meta || {};
            console.log(response?.data);

            const formattedData = apiData.map((item) => ({
                id: item.id,
                title: item.title,
                notify_type: item.notify_type?.name || item.notify_type,
                notify_type_id: item.notify_type_id,
                message: item.message,
                status: item.status,
                last_pushed_date: item.last_pushed_date === '' ? item.last_pushed_date : moment(item.last_pushed_date).format("YYYY-MM-DD hh:mm A"),
                created_at: item.created_at === '' ? item.created_at : moment(item.created_at).format("YYYY-MM-DD hh:mm A"),
                actions: "Edit | Delete"
            }));

            setRows(formattedData);
            setTotal(meta.total || formattedData.length);
            setFrom(meta.from || 1);
            setTo(meta.to || formattedData.length);
            setPage(meta.current_page ? meta.current_page - 1 : 0);
        } catch (error) {
            console.error("Failed to fetch notifications", error);
            setOpen(true);
            setMessage("Failed to fetch notifications");
            setMessageState("error");
        } finally {
            setLoading(false);
        }
    }, [activeFilters, sortConfig]);

    // Separate useEffect to fetch data after activeFilters are set
    useEffect(() => {
        getNotificationList();
    }, [activeFilters, getNotificationList]); // Run when activeFilters change

    // Handle sorting (multiple column sorting)
    const handleSort = (columnId) => {
        const column = columns.find(col => col.id === columnId);
        if (!column || !column.sortable) return;

        setSortConfig(prevConfig => {
            const existingIndex = prevConfig.findIndex(sort => sort.field === column.apiField);

            if (existingIndex >= 0) {
                // Column already sorted, toggle direction or remove
                const newConfig = [...prevConfig];
                if (newConfig[existingIndex].direction === 'asc') {
                    newConfig[existingIndex].direction = 'desc';
                } else {
                    // Remove this sort
                    newConfig.splice(existingIndex, 1);
                }
                return newConfig;
            } else {
                // Add new sort
                return [...prevConfig, { field: column.apiField, direction: 'asc', column: columnId }];
            }
        });
    };

    // Filter functions
    const handleFilterOpen = () => {
        setFilterOpen(!filterOpen);
    };

    const handleFilter = () => {

        // Save to localStorage and update activeFilters
        localStorage.setItem('automated_notification_filter', JSON.stringify(filters));
        setActiveFilters({ ...filters });

        // Show chips only if there are active filters
        setShowActiveFilters(
            filters.notifyTypeIds?.length > 0 ||
            filters.status
        );

        // Reset page to 0 when filtering
        setPage(0);
        getNotificationList(0, rowsPerPage);
    };

    const handleRemoveFilter = (key, typeId = null) => {

        // Update both current filters and active filters
        const updated = { ...filters };

        if (key === 'notifyTypeIds' && typeId) {
            // Remove specific notification type
            updated.notifyTypeIds = (updated.notifyTypeIds || []).filter(id => id !== typeId);
        } else if (key === 'notifyTypeIds') {
            // Remove all notification types
            updated.notifyTypeIds = [];
        } else {
            // Remove other filters (like status)
            updated[key] = '';
        }

        setFilters(updated);
        setActiveFilters(updated);
        localStorage.setItem('automated_notification_filter', JSON.stringify(updated));

        // Hide chips if all filters cleared
        if ((updated.notifyTypeIds || []).length === 0 && !updated.status) {
            setShowActiveFilters(false);
        }

        // Reset page to 0 when removing filters
        setPage(0);
        getNotificationList(0, rowsPerPage);
    };

    // Add pagination handlers
    const handleChangePage = (e, newPage) => {
        setPage(newPage);
        getNotificationList(newPage, rowsPerPage);
    };

    const handleChangeRowsPerPage = (event) => {
        const newRowsPerPage = parseInt(event.target.value, 10);
        setRowsPerPage(newRowsPerPage);
        setPage(0);
        localStorage.setItem("configRowPerPage", newRowsPerPage);
        getNotificationList(0, newRowsPerPage);
    };

    const handleAddNew = () => {
        // Close any other open modals first
        setOpenDeleteDialog(false);
        setOpenMessageModal(false);

        // Force dialog re-render by incrementing key
        setDialogKey(prev => prev + 1);

        // Reset states immediately
        setIsAddNew(true);
        setViewDetails({});
        setOpenEditDialog(true);
    };

    const handleEdit = (row) => {
        // Close any other open modals first
        setOpenDeleteDialog(false);
        setOpenMessageModal(false);

        // Reset states immediately
        setIsAddNew(false); // Ensure this is set to false for edit
        setViewDetails(row);
        setOpenEditDialog(true);
    };

    const sendEdit = async (call, formData) => {
        if (call.open === false) {
            setOpenEditDialog(false);
            setViewDetails({});
            return;
        }
        if (call.success === true) {
            try {
                if (isAddNew) {
                    const response = await httpclient.post(
                        `request-response?requestName=lightspeed/automated/notification/create`,
                        formData
                    );
                    if (response.status === 200) {
                        getNotificationList();
                        setOpen(true);
                        setMessageState("success");
                        setMessage(response.data?.message || "Added successfully");
                        setViewDetails({});
                        setOpenEditDialog(false);
                    } else {
                        setOpen(true);
                        setMessage(response.data?.message || "An error occurred");
                        setMessageState("error");
                        setLoading(false);
                        throw new Error(response.data?.message || "Failed to add notification");
                    }
                } else if (viewDetails.id) {
                    const response = await httpclient.put(
                        `request-response?requestName=lightspeed/automated/notification/update/${viewDetails.id}`,
                        formData
                    );
                    if (response.status === 200) {
                        getNotificationList(); // Fixed typo
                        setOpen(true);
                        setMessageState("success");
                        setMessage(response.data?.message || "Updated successfully");
                        setViewDetails({});
                        setOpenEditDialog(false);
                    } else {
                        setOpen(true);
                        setMessage(response.data?.message || "An error occurred");
                        setMessageState("error");
                        setLoading(false);
                        throw new Error(response.data?.message || "Failed to update notification");
                    }
                }
            } catch (err) {
                setOpen(true);
                setMessage(err?.response?.data?.message || "An error occurred");
                setMessageState("error");
                setLoading(false);
                setOpenEditDialog(false); // Close modal on failure
                throw err; // Re-throw for handleYes to catch
            }
        }
    };

    const handleDelete = (row) => {
        // Close any other open modals first
        setOpenEditDialog(false);
        setOpenMessageModal(false);

        setOpenDeleteDialog(true);
        setViewDetails(row);
    };

    const sendDelete = (call) => {
        if (call.open === false) {
            setOpenDeleteDialog(false);
            setViewDetails({});
        }
        if (call.success === true) {
            httpclient
                .delete(`request-response?requestName=lightspeed/delete/automated-notification/${viewDetails.id}`, { data: { deleted_by: userData?.user_name } })
                .then((response) => {
                    if (response.status === 200) {
                        setOpen(true);
                        setMessageState("success");
                        setMessage(response?.data?.message);
                        setOpenDeleteDialog(false);
                        setViewDetails({});
                        getNotificationList();
                    } else {
                        setOpen(true);
                        setMessage(response?.data?.message);
                        setMessageState("error");
                        setLoading(false);
                    }
                }).catch((err) => {
                    setOpen(true);
                    setMessage(err?.response?.data?.message || "An error occurred");
                    setMessageState("error");
                    setLoading(false);
                });
        }
    };

    const handleClose = (event, reason) => {
        if (reason === "clickaway") {
            return;
        }
        setOpen(false);
    };

    const updateRowStatus = async (row) => {
        try {
            setRowLoading((prev) => ({ ...prev, [row.id]: true }));

            // Toggle the status (assuming status is 0 or 1)
            const newStatus = row.status === 1 ? 0 : 1;

            const payload = {
                status: newStatus,
                notify_type_id: row.notify_type_id || '',
                title: row.title || '',
                message: row.message || '',
                updated_by: userData?.user_name || '',
            };

            // Make API call to update status
            const response = await httpclient.put(
                `request-response?requestName=lightspeed/automated/notification/update/${row.id}`,
                payload
            );

            if (response.status === 200) {
                // Show success message
                setOpen(true);
                setMessageState("success");
                setMessage(response.data?.message || "Status updated successfully");

                // Refresh the notification list
                await getNotificationList();
            } else {
                // Show error message
                setOpen(true);
                setMessage(response.data?.message || "Failed to update status");
                setMessageState("error");
            }
        } catch (error) {
            // Handle error
            setOpen(true);
            setMessage(error?.response?.data?.message || "An error occurred");
            setMessageState("error");
        } finally {
            setRowLoading((prev) => ({ ...prev, [row.id]: false }));
        }
    };

    // OptionMenu functions
    const handleMenuClick = (event, row) => {
        setAnchorEl(event.currentTarget);
        setSelectedRow(row);
    };

    const handleMenuClose = () => {
        setAnchorEl(null);
        setSelectedRow(null);
    };

    const handleMenuAction = (action) => {
        if (selectedRow) {
            switch (action) {
                case 'edit':
                    handleEdit(selectedRow);
                    break;
                case 'delete':
                    handleDelete(selectedRow);
                    break;
                default:
                    break;
            }
        }
        handleMenuClose();
    };

    // Handle message view modal (now handled inline in table)
    // const handleNewView = (row) => {
    //     setMessageDetails(row);
    //     setOpenMessageModal(true);
    // };

    // Removed unused currentChange function



    return (
        <Box>
            <Grid container spacing={2} alignItems="center" justifyContent="space-between">
                <Grid item xs={12} md={8}>
                    <Header>
                        <h1>List Automated Push Notification</h1>
                    </Header>
                </Grid>

                <Grid item xs={12} md={4}>
                    <Box
                        display="flex"
                        flexDirection={{ xs: "column", sm: "row" }}
                        justifyContent={{ xs: "flex-start", md: "flex-end" }}
                        alignItems={{ xs: "stretch", sm: "center" }}
                        gap={1}
                    >
                        <Button
                            color="primary"
                            variant="contained"
                            onClick={handleFilterOpen}
                            fullWidth={{ xs: true, sm: false }}
                        >
                            Filter <FilterList sx={{ ml: 1 }} fontSize="small" />
                        </Button>

                        {permissions?.some((pre) => pre.name === "allow_create" && pre.status === 1) &&
                            <AddButton
                                color="primary"
                                variant="contained"
                                onClick={handleAddNew}
                                fullWidth={{ xs: true, sm: false }}
                            >
                                <Add sx={{ mr: 1 }} fontSize="small" /> Add Notification
                            </AddButton>
                        }
                    </Box>
                </Grid>

                {/* Filter Section */}
                <Grid item xs={12}>
                    <Collapse in={filterOpen}>
                        <Card>
                            <Box p={4}>
                                <Grid container spacing={2}>
                                    <Grid item xs={12} md={6}>
                                        <InputLabel>Notification Type</InputLabel>
                                        <Autocomplete
                                            options={notificationTypes}
                                            multiple
                                            disableCloseOnSelect
                                            getOptionLabel={(option) => option.name}
                                            renderOption={(props, option, { selected }) => (
                                                <li {...props}>
                                                    <Checkbox icon={checkboxIcon} checkedIcon={checkboxCheckedIcon} checked={selected} />
                                                    {option.name}
                                                </li>
                                            )}
                                            value={notificationTypes.filter(type => filters.notifyTypeIds?.includes(type.id)) || []}
                                            onChange={(e, newValue) => setFilters({ ...filters, notifyTypeIds: newValue.map(type => type.id) })}
                                            renderInput={(params) => <TextField {...params} variant="outlined" />}
                                        />
                                    </Grid>
                                    <Grid item xs={12} md={6}>
                                        <InputLabel>Status</InputLabel>
                                        <FormControl fullWidth>
                                            <Select
                                                name="status"
                                                value={filters.status || ''}
                                                onChange={(e) => setFilters({ ...filters, status: e.target.value })}
                                            >
                                                <MenuItem value="">Select</MenuItem>
                                                <MenuItem value="1">Active</MenuItem>
                                                <MenuItem value="0">Inactive</MenuItem>
                                            </Select>
                                        </FormControl>
                                    </Grid>
                                    <Grid item xs={12}>
                                        <Box textAlign="right">
                                            <Button
                                                variant="contained"
                                                color="primary"
                                                onClick={handleFilter}
                                            >
                                                Filter <ArrowForward style={{ marginLeft: "5px" }} fontSize="small" />
                                            </Button>
                                        </Box>
                                    </Grid>
                                </Grid>
                            </Box>
                        </Card>
                    </Collapse>
                </Grid>

                {/* Filter Chips */}
                {showActiveFilters && (
                    <Grid item xs={12}>
                        <Box display="flex" flexWrap="wrap" gap={1} mt={2} mb={2}>
                            {activeFilters.notifyTypeIds?.length > 0 && activeFilters.notifyTypeIds.map(typeId => {
                                const type = notificationTypes.find(t => t.id === parseInt(typeId));
                                return (
                                    <Chip
                                        key={typeId}
                                        label={`Notification Type: ${type?.name || typeId}`}
                                        onDelete={() => handleRemoveFilter('notifyTypeIds', typeId)}
                                    />
                                );
                            })}
                            {activeFilters.status && (
                                <Chip
                                    label={`Status: ${activeFilters.status === "1" ? "Active" : "Inactive"}`}
                                    onDelete={() => handleRemoveFilter('status')}
                                />
                            )}
                        </Box>
                    </Grid>
                )}

                <Grid item xs={12}>
                    <Card>
                        <TableContainer component={Paper}>
                            <Table>
                                <TableHead >
                                    <TableRow>
                                        {columns.map((column) => (
                                            <StyledTableCell
                                                sx={{ backgroundColor: 'primary.main' }}
                                                key={column.id}
                                                className={column.sortable ? 'sortable' : ''}
                                                onClick={() => column.sortable && handleSort(column.id)}
                                            >
                                                <Box display="flex" alignItems="center" color="white">
                                                    {column.name}
                                                    {column.sortable && (
                                                        <Box ml={1}>
                                                            {(() => {
                                                                const sortItem = sortConfig.find(sort => sort.column === column.id);
                                                                if (sortItem) {
                                                                    return sortItem.direction === 'asc' ?
                                                                        <KeyboardArrowUp fontSize="small" /> :
                                                                        <KeyboardArrowDown fontSize="small" />;
                                                                }
                                                                return <UnfoldMore fontSize="small" />;
                                                            })()}
                                                        </Box>
                                                    )}
                                                </Box>
                                            </StyledTableCell>
                                        ))}
                                    </TableRow>
                                </TableHead>
                                <TableBody>
                                    {loading ? (
                                        <TableRow>
                                            <TableCell colSpan={columns.length} align="center">
                                                <CircularProgress />
                                            </TableCell>
                                        </TableRow>
                                    ) : rows.length === 0 ? (
                                        <TableRow>
                                            <TableCell colSpan={columns.length} align="center">
                                                No data available
                                            </TableCell>
                                        </TableRow>
                                    ) : (
                                        rows.map((row) => (
                                            <TableRow key={row.id}>
                                                {columns.map((column) => (
                                                    <TableCell key={column.id}>
                                                        {column.id === 'message' ? (
                                                            <IconButton
                                                                onClick={() => {
                                                                    setMessageDetails(row);
                                                                    setOpenMessageModal(true);
                                                                }}
                                                                size="small"
                                                            >
                                                                <Visibility />
                                                            </IconButton>
                                                        ) : column.id === 'status' ? (
                                                            <Box display="flex" alignItems="center">
                                                                <Switch
                                                                    checked={row.status === 1}
                                                                    onChange={() => updateRowStatus(row)}
                                                                    disabled={rowLoading[row.id]}
                                                                    size="small"
                                                                />
                                                                <span style={{ marginLeft: '8px' }}>
                                                                    {row.status === 1 ? "Active" : "Inactive"}
                                                                </span>
                                                                {rowLoading[row.id] && (
                                                                    <CircularProgress size={16} style={{ marginLeft: '8px' }} />
                                                                )}
                                                            </Box>
                                                        ) : column.id === 'actions' ? (
                                                            <Box>
                                                                <Tooltip title="Actions">
                                                                    <IconButton
                                                                        aria-label="more"
                                                                        onClick={(event) => handleMenuClick(event, row)}
                                                                        size="small"
                                                                    >
                                                                        <Tune />
                                                                    </IconButton>
                                                                </Tooltip>
                                                                <Menu
                                                                    anchorEl={anchorEl}
                                                                    open={Boolean(anchorEl) && selectedRow?.id === row.id}
                                                                    onClose={handleMenuClose}
                                                                    slotProps={{
                                                                        paper: {
                                                                            style: {
                                                                                maxHeight: 48 * 4.5,
                                                                                width: '20ch',
                                                                            },
                                                                        },
                                                                    }}
                                                                >
                                                                    <MenuItem disabled>
                                                                        <em><b>Select Action</b></em>
                                                                    </MenuItem>
                                                                    <MenuItem disabled={permissions?.some((pre) => pre.name === "allow_update" && pre.status === 1) ? false : true} onClick={() => handleMenuAction('edit')}>
                                                                        Update Notification
                                                                    </MenuItem>
                                                                    <MenuItem disabled={permissions?.some((pre) => pre.name === "allow_delete" && pre.status === 1) ? false : true} onClick={() => handleMenuAction('delete')}>
                                                                        Delete Notification
                                                                    </MenuItem>
                                                                </Menu>
                                                            </Box>
                                                        ) : (
                                                            row[column.id]
                                                        )}
                                                    </TableCell>
                                                ))}
                                            </TableRow>
                                        ))
                                    )}
                                </TableBody>
                            </Table>
                        </TableContainer>

                        {/* Pagination */}
                        <TablePagination
                            component="div"
                            count={total}
                            page={page}
                            onPageChange={handleChangePage}
                            rowsPerPage={rowsPerPage}
                            onRowsPerPageChange={handleChangeRowsPerPage}
                            rowsPerPageOptions={[5, 10, 20, 50]}
                            labelDisplayedRows={({ from, to, count }) =>
                                `${from}-${to} of ${count !== -1 ? count : `more than ${to}`}`
                            }
                        />
                    </Card>
                </Grid>
            </Grid>

            {/* Existing dialogs and snackbar */}
            {openDeleteDialog && <DeleteDialog name={"Notification"} viewDetails={viewDetails} sendDelete={sendDelete} />}

            {/* Add new and Edit Dialog */}
            {openEditDialog && (
                <EditAutomatedNotification
                    key={`${isAddNew ? 'add' : 'edit'}-${viewDetails?.id || 'new'}-${dialogKey}`}
                    open={openEditDialog}
                    onClose={() => {
                        setOpenEditDialog(false);
                        setViewDetails({});
                    }}
                    viewDetails={viewDetails}
                    sendEdit={sendEdit}
                    isAddNew={isAddNew}
                />
            )}

            {/* Message View Modal */}
            {openMessageModal && (
                <Dialog
                    open={openMessageModal}
                    onClose={() => setOpenMessageModal(false)}
                    maxWidth="md"
                    fullWidth
                >
                    <DialogTitle
                        sx={{
                            bgcolor: "primary.main",
                            color: "primary.contrastText",
                            px: 3,
                            py: 2,
                        }}
                    >
                        <Typography id="modal-title" variant="h6" fontWeight="bold">
                            Message Details - {messageDetails.title}
                        </Typography>

                    </DialogTitle>
                    <DialogContent>
                        <Typography variant="body1" sx={{ mt: 2, whiteSpace: 'pre-wrap' }}>
                            {messageDetails.message}
                        </Typography>
                    </DialogContent>
                    <DialogActions>
                        <Button onClick={() => setOpenMessageModal(false)} color="primary">
                            Close
                        </Button>
                    </DialogActions>
                </Dialog>
            )}

            <Snackbar
                autoHideDuration={3000}
                anchorOrigin={{ vertical: "top", horizontal: "right" }}
                open={open}
                onClose={handleClose}
            >
                <Alert
                    onClose={handleClose}
                    severity={messageState}
                    sx={{ width: "100%" }}
                >
                    {message}
                </Alert>
            </Snackbar>
        </Box>
    )
}

export default AutomatedPushNotification