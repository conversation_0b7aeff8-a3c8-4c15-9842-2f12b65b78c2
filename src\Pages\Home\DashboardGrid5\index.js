import React, { useEffect, useState } from "react";
import { Skeleton } from "@mui/material";

const titleMap = {
  customers: [
    { id: "totalCustomer", name: "Total Customers" },
    { id: "weeklyCustomer", name: "Last 7 Days" },
    { id: "todayCustomer", name: "Today" },
  ],
  items: [
    { id: "totalItems", name: "Total Items" },
  ],
  loyaltyCustomers: [
    { id: "totalLoyaltyCustomer", name: "Total Loyalty Customers" },
    { id: "weeklyLoyaltyCustomer", name: "Last 7 Days" },
    { id: "todayLoyaltyCustomer", name: "Today" },
  ],
  orders: [
    { id: "totalOrder", name: "Total Orders" },
    { id: "weeklyOrder", name: "Last 7 Days" },
    { id: "todayOrder", name: "Today" },
  ],
  products: [
    { id: "totalProduct", name: "Total Products" },
    { id: "onlineProduct", name: "Online Products" },
    { id: "offlineProduct", name: "Offline Products" },
  ],
};

const DashboardGrid5 = ({ loading, productStatus }) => {
  const renderSection = (sectionKey) => {
    const sectionData = productStatus && productStatus[sectionKey];
    const titles = titleMap[sectionKey] || [];

    return (
      <div className="grid-section">
        <div className="section-header">{sectionKey === "loyaltyCustomers" ? "Loyalty Customers" : sectionKey.charAt(0).toUpperCase() + sectionKey.slice(1)}</div>
        <div className="grid-block-content">
          {titles.map((item) => (
            <React.Fragment key={item.id}>
              <div className="grid-item title">{item.name}</div>
              <div className="grid-item content">
                {loading ? <Skeleton variant="text" /> : sectionData ? sectionData[item.id] ?? "N/A" : null}
              </div>
            </React.Fragment>
          ))}
        </div>
      </div>
    );
  };

  return (
    <div className="grid-block">
      {/* <div className="grid-block-header">Dynamic 365</div> */}
      <div className="dashboard-grid">
        {Object.keys(productStatus ? productStatus : titleMap).map((sectionKey) => (
          renderSection(sectionKey)
        ))}
      </div>

      <style jsx>{`
            .grid-block {
              border: 0.5px solid #gray;
              box-shadow: 0px 5px 20px 0px rgba(0,0,0,0.07);
              padding: 0px;
              background-color: #fff;
              border-radius: 5px;
            }
              
            .dashboard-grid {
             display: flex;
             justify-content: space-between;
             gap: 20px;
            }
    
            .grid-block-header {
              font-size: 18px;
              font-weight: bold;
              padding: 10px;
              margin-top: 0.3px;
              background-color: #281E50;
              color: #fff;
              border-radius: 3px;
              height: 100%;
              min-height: 70px;
              display: flex;
              align-items: center;
            }
    
            .grid-section {
              margin-top: 10px;
            }
    
            .section-header {
              font-size: 16px;
              font-weight: bold;
              padding: 10px;
              margin-top:-10px;
              background-color: #f5f5f5;
              color: black;
              border-radius: 3px;
            }
    
            .grid-block-content {
              display: grid;
              width: 100%;
              grid-template-columns: 1fr auto;
              gap: 1px solid #ccc;
              font-family: "Trebuchet MS", sans-serif;
              font-size: 14px;
              font-weight: bold;
            }
    
            .grid-item.title {
              border-right: 1px solid #f1f1f1;
              border-bottom: 1px solid #f1f1f1;
              background-color: #ffffff;
              padding: 16px;
            }
    
            .grid-item.content {
              border-bottom: 1px solid #f1f1f1;
              background-color: #ffffff;
              padding: 16px;
              min-width: 120px;
              text-align:right;
            }
          `}</style>
    </div>
  );
};

export default DashboardGrid5;
