import { Check, Clear, Close, Download, Sync, Visibility } from "@mui/icons-material";
import {
    AppBar,
    Box,
    Button,
    CircularProgress,
    Dialog,
    DialogActions,
    DialogContent,
    DialogTitle,
    Grid,
    IconButton,
    Skeleton,
    styled,
    useTheme,
    Snackbar
} from "@mui/material";
import moment from "moment";
import React, { useEffect, useState } from "react";
import Tabs from "@mui/material/Tabs";
import Tab from "@mui/material/Tab";
import Typography from "@mui/material/Typography";
import PropTypes from "prop-types";
//import BasicTable from "../../../../Components/BasicTable";
import FsLightbox from "fslightbox-react";

import MuiAlert from "@mui/material/Alert";
//import ViewPolicyDialog from "../../price_policy/ViewPolicyDialog";
import parse from "html-react-parser";
import BasicTable from "../../../../Components/BasicTable";


const Alert = React.forwardRef(function Alert(props, ref) {
    return <MuiAlert elevation={6} ref={ref} variant="filled" {...props} />;
});

const StyledHeaderTitle = styled(DialogTitle)(({ theme }) => ({
    background: theme.palette.primary.main,
    color: "#fff",
    position: "relative",
    "& button": {
        position: "absolute",
        right: "15px",
        top: "15px",
        color: "#fff",
    },
}));

const FlexContent = styled("div")(({ theme }) => ({
    display: "flex",
    flexDirection: "row",
    fontSize: "17px",
    marginBottom: "10px",
    alignItems: "flex-start",
}));

const FlexInnerTitle = styled("div")(({ theme }) => ({
    display: "flex",
    alignItems: "center",
    justifyContent: "space-between",
    minWidth: "249px",
    maxWidth: "250px",
    fontWeight: "600",
}));

const FlexContent2 = styled("div")(({ theme }) => ({
    display: "flex",
    flexDirection: "row",
    fontSize: "17px",
}));

const FlexInnerTitle2 = styled("div")(({ theme }) => ({
    display: "flex",
    fontWeight: "600",
    gap: "5px",
    marginRight: "5px",
}));

const BoxDiv = styled("div")(({ theme }) => ({
    textAlign: "center",
}));

const Values = styled("div")(({ theme }) => ({
    marginLeft: "10px",
    fontWeight: "500",
    color: theme.palette.primary.dark,
}));

const ImageDiv = styled("div")(({ theme }) => ({
    display: "flex",
    alignItems: "center",
    width: "100%",
    flexWrap: "wrap",
    marginBottom: "10px",
}));

const ImageCell = styled("div")(({ theme }) => ({
    margin: "10px",
    width: "280px",
    borderRadius: "5px",
    overflow: "hidden",
    "& img": {
        width: "250px",
        height: "250px",
        objectFit: "cover",
        transition: "0.5s",
        boxShadow: theme.palette.primary.shadow,
        marginBottom: "10px",
        overflow: "hidden",
    },
    "& img:hover": {
        transform: "scale(1.1)",
    },
}));

const price_policyBox = styled(Box)(({ theme }) => ({
    display: "flex",
    marginBottom: "15px",
    "& h5": {
        margin: "5px",
    },
}));

const AppBarTabs = styled(AppBar)(({ theme }) => ({
    background: "#fff",
    color: theme.palette.primary.dark,
}));

function TabPanel(props) {
    const { children, value, index, ...other } = props;

    return (
        <div
            role="tabpanel"
            hidden={value !== index}
            id={`full-width-tabpanel-${index}`}
            aria-labelledby={`full-width-tab-${index}`}
            {...other}
        >
            {value === index && (
                <Box sx={{ p: 3 }}>
                    <Typography>{children}</Typography>
                </Box>
            )}
        </div>
    );
}

TabPanel.propTypes = {
    children: PropTypes.node,
    index: PropTypes.number.isRequired,
    value: PropTypes.number.isRequired,
};

function a11yProps(index) {
    return {
        id: `full-width-tab-${index}`,
        "aria-controls": `full-width-tabpanel-${index}`,
    };
}

const orderColumns = [
    { id: "sn", name: "SN" },
    { id: "erply_product", name: "Item Name" },
    { id: "clientCode", name: "Client Code" },
    { id: "amount", name: "Quantity" },
    { id: "clientCode", name: "Client Code" },
    { id: "price", name: "Total Price" },
    { id: "updated_at", name: "Updated Date" },

];

const ViewInventoryWriteOffDetail = (props) => {
    const theme = useTheme();
    const [value, setValue] = useState(0);
    const [open, setOpen] = useState(false);
    const [message, setMessage] = useState("");
    const [messageState, setMessageState] = useState("");
    const [togglerLanding, setTogglerLanding] = useState(false);
    const [imgIndex1, setImgIndex1] = useState(0);

    const handleChange = (event, newValue) => {
        setValue(newValue);
    };


    const [dialogDetails, setDialogDetails] = useState({
        open: true,
    });

    useEffect(() => {
        props.sendDetails(dialogDetails);
    }, [props, dialogDetails]);

    const handleImageTogglerLanding = (index) => {
        setImgIndex1(index);
        setTogglerLanding((prev) => !prev);
    };

    const handleClose = () => {
        setDialogDetails({
            ...dialogDetails,
            open: false,
        });
    };

    const displayText = (descriptionTexts) => {
        const textIsEmpty = descriptionTexts === null || descriptionTexts === "";
        return !textIsEmpty ? (
            parse(descriptionTexts)
        ) : (
            "-"
        );
    }


    const handleCloseSnack = (event, reason) => {
        if (reason === "clickaway") {
            return;
        }
        setOpen(false);
    };

    return (
        <div>
            <Dialog
                open={dialogDetails.open}
                onClose={handleClose}
                maxWidth="xl"
                fullWidth
                aria-labelledby="alert-dialog-title"
                aria-describedby="alert-dialog-description"
            >
                <StyledHeaderTitle id="alert-dialog-title">
                    <div>
                        View Inventory WriteOff Details{" "}
                        {"(" + "#" +
                            //   (props.viewDetails.handle || "-") +
                            //   "/" +
                            (props.viewDetails.inventoryWriteOffID || "-") +
                            ")"}
                    </div>
                    <IconButton onClick={handleClose}>
                        <Close />
                    </IconButton>
                </StyledHeaderTitle>
                {props.singleLoading ? (
                    <DialogContent>
                        <Grid container spacing={2}>
                            <Grid item xs={12} md={6}>
                                <Box p={3} sx={{ width: "100%" }}>
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                </Box>
                            </Grid>
                            <Grid item xs={12} md={6}>
                                <Box p={3} sx={{ width: "100%" }}>
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                </Box>
                            </Grid>
                        </Grid>
                    </DialogContent>
                ) : (
                    <DialogContent sx={{ padding: "0" }}>
                        <AppBarTabs position="static">
                            <Tabs
                                value={value}
                                onChange={handleChange}
                                indicatorColor="secondary"
                                textColor="inherit"
                                variant="fullWidth"
                                aria-label="full width tabs example"
                            >
                                <Tab label="Details" {...a11yProps(0)} />
                                <Tab label="Inventory WriteOff Items" {...a11yProps(1)} />


                            </Tabs>
                        </AppBarTabs>

                        <TabPanel value={value} index={0} dir={theme.direction}>
                            <Box>
                                <Grid container spacing={2}>
                                    {/* Left Side */}
                                    <Grid item xs={12} md={6}>
                                        <FlexContent>
                                            <FlexInnerTitle>
                                                <span>Inventory WriteOff ID</span> <span> : </span>
                                            </FlexInnerTitle>
                                            <Values>{props.viewDetails.inventoryWriteOffID || "-"}</Values>
                                        </FlexContent>
                                        <FlexContent>
                                            <FlexInnerTitle>
                                                <span>NetSuite ID</span> <span> : </span>
                                            </FlexInnerTitle>
                                            <Values>{props.viewDetails.netsuiteID || "-"}</Values>
                                        </FlexContent>

                                        <FlexContent>
                                            <FlexInnerTitle>
                                                <span>Comments</span> <span> : </span>
                                            </FlexInnerTitle>
                                            <Values>{props.viewDetails.comments || "-"}</Values>
                                        </FlexContent>

                                        <FlexContent>
                                            <FlexInnerTitle>
                                                <span>Client Code</span> <span> : </span>
                                            </FlexInnerTitle>
                                            <Values>{props.viewDetails.clientCode || "-"}</Values>
                                        </FlexContent>
                                        <FlexContent>
                                            <FlexInnerTitle>
                                                <span>Warehouse</span> <span> : </span>
                                            </FlexInnerTitle>
                                            <Values>{props.viewDetails.erply_warehouse?.name || "-"}</Values>
                                        </FlexContent>
                                        {/* <FlexContent>
                                            <FlexInnerTitle>
                                                <span>Supplier</span> <span> : </span>
                                            </FlexInnerTitle>
                                            <Values>{props.viewDetails.erply_supplier?.groupName || "-"}</Values>
                                        </FlexContent> */}
                                        <FlexContent>
                                            <FlexInnerTitle>
                                                <span>Reason Code</span> <span> : </span>
                                            </FlexInnerTitle>
                                            <Values>{props.viewDetails.erply_reason_codes?.name || "-"}</Values>
                                        </FlexContent>


                                    </Grid>

                                    {/* Left Side */}

                                    {/* Right Side */}
                                    <Grid item xs={12} md={6}>

                                        <FlexContent>
                                            <FlexInnerTitle>
                                                <span>Warehouse</span> <span> : </span>
                                            </FlexInnerTitle>
                                            <Values>{props.viewDetails.erply_warehouse?.name || "-"}</Values>
                                        </FlexContent>

                                        {props.viewDetails.pendingProcess === 1 &&
                                            <FlexContent>
                                                <FlexInnerTitle>
                                                    <span>Pending Process</span> <span> : </span>
                                                </FlexInnerTitle>
                                                <Values><Check color='primary' fontSize='small' /></Values>
                                            </FlexContent>
                                        }

                                        <FlexContent>
                                            <FlexInnerTitle>
                                                <span>Added Date</span> <span> : </span>
                                            </FlexInnerTitle>
                                            <Values>{moment(props.viewDetails.added).format("ddd, DD MMM YYYY, h:mm a") || "-"}</Values>
                                        </FlexContent>

                                        <FlexContent>
                                            <FlexInnerTitle>
                                                <span>Last Modified Date</span> <span> : </span>
                                            </FlexInnerTitle>
                                            <Values>
                                                {moment(props.viewDetails.lastModified).format(
                                                    "ddd, DD MMM YYYY, h:mm a"
                                                ) || "-"}
                                            </Values>
                                        </FlexContent>
                                    </Grid>
                                    {/* Right Side */}

                                    {/* <Grid item xs={12}>
                                        <FlexContent>
                                            <FlexInnerTitle>
                                                <span>Notes</span> <span> : </span>
                                            </FlexInnerTitle>
                                            <Values>
                                            
                                                {props.viewDetails.note && displayText(props.viewDetails.note)}
                                            </Values>
                                        </FlexContent>
                                    </Grid> */}
                                </Grid>
                            </Box>
                        </TabPanel>

                        <TabPanel value={value} index={1} dir={theme.direction}>

                            <BasicTable
                                columns={orderColumns}
                                rows={props.viewDetails.erply_inventory_write_off_items}
                            />

                        </TabPanel>
                    </DialogContent>
                )}
                <DialogActions>
                    <Button onClick={handleClose} variant="outlined" color="primary">
                        Close
                    </Button>
                </DialogActions>
            </Dialog>

            {/* {openPolicyDialog && (
        <ViewPolicyDialog
          singleLoading={singleLoading}
          viewDetails={viewDetails}
          sendDetails={sendDetails}
        />
      )} */}

            <Snackbar
                autoHideDuration={3000}
                anchorOrigin={{ vertical: "top", horizontal: "right" }}
                open={open}
                onClose={handleCloseSnack}
            >
                <Alert
                    onClose={handleCloseSnack}
                    severity={messageState}
                    sx={{ width: "100%" }}
                >
                    {message}
                </Alert>
            </Snackbar>
        </div>
    );
};

export default ViewInventoryWriteOffDetail;
