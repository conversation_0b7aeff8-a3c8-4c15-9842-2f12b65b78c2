import React, { useEffect, useState } from 'react';
import {
  Box,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Typography,
  Paper,
  CircularProgress,
  Chip,
  Snackbar,
  Collapse,
  Card,
  Grid,
  InputLabel,
  TextField,
} from '@mui/material';
import dayjs from 'dayjs';
import httpclient from '../../../Utils';
import MuiAlert from "@mui/material/Alert";
import { FilterList } from '@mui/icons-material';

const Alert = React.forwardRef(function Alert(props, ref) {
  return <MuiAlert elevation={6} ref={ref} variant="filled" {...props} />;
});

export default function LoyaltyCustomersByStoreReport() {
  const [data, setData] = useState([]);
  const [totalCustomers, setTotalCustomers] = useState(0);
  const [loading, setLoading] = useState(true);
  const [open, setOpen] = useState(false);
  const [message, setMessage] = useState("");
  const [messageState, setMessageState] = useState("success");
  const [startDate, setStartDate] = useState('2025-04-01');
  const [endDate, setEndDate] = useState(dayjs().format('YYYY-MM-DD'));
  const [activeFilters, setActiveFilters] = useState({});
  const [filterOpen, setFilterOpen] = useState(false);

  const fetchData = async (filters = {}) => {
    try {
      setLoading(true);
      let url = 'request-response?requestName=lightspeed/report/loyalty-customer-report';
      const params = [];

      // Use provided filters or active filters
      const filtersToUse = Object.keys(filters).length > 0 ? filters : activeFilters;

      if (filtersToUse.startDate) {
        params.push(`start_date=${filtersToUse.startDate}`);
      }
      if (filtersToUse.endDate) {
        params.push(`end_date=${filtersToUse.endDate}`);
      }

      if (params.length > 0) {
        url += `&${params.join("&")}`;
      }

      const response = await httpclient.get(url);

      if (response?.data?.status === 200) {
        const { results, totalCustomers } = response.data.data;
        setData(results);
        setTotalCustomers(totalCustomers);
      } else {
        setData([]);
        setTotalCustomers(0);
        setMessage(response.data?.message || "An error occurred");
        setMessageState("error");
        setOpen(true);
      }
    } catch (error) {
      console.error('API error:', error);
      setData([]);
      setTotalCustomers(0);
      setMessage(error?.response?.data?.message || "An error occurred");
      setMessageState("error");
      setOpen(true);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    const initialFilters = {
      startDate: startDate,
      endDate: endDate
    };
    
    // Set active filters to show chips
    setActiveFilters(initialFilters);
    
    // Fetch data with initial filters
    fetchData(initialFilters);
  }, []);

  const handleFilter = () => {
    // Check if both dates are selected
    if (!startDate || !endDate) {
      setMessageState("error");
      setMessage("Please select both Start Date and End Date.");
      setOpen(true);
      return;
    }

    // Format dates consistently
    const formattedStart = startDate ? dayjs(startDate).format('YYYY-MM-DD') : '';
    const formattedEnd = endDate ? dayjs(endDate).format('YYYY-MM-DD') : '';

    // Additional validation to ensure dates are valid
    if (!formattedStart || !formattedEnd) {
      setMessageState("error");
      setMessage("Invalid date format. Please select valid dates.");
      setOpen(true);
      return;
    }

    // Create filters object
    const filters = {
      startDate: formattedStart,
      endDate: formattedEnd,
    };



    // Apply filters
    setActiveFilters(filters);
    fetchData(filters);
  };

  const handleRemoveFilter = (key) => {
    const updatedFilters = { ...activeFilters };
    delete updatedFilters[key];

    if (key === 'startDate') setStartDate(null);
    if (key === 'endDate') setEndDate(null);

    setActiveFilters(updatedFilters);
    fetchData(updatedFilters);
  };

  const handleExport = () => {
    const csvContent = [
      ['S.N', 'Store', 'Total Customers'],
      ...data.map((item, index) => [index + 1, item.manualStoreCode.trim(), item.total]),
      ['', 'Total Customers', totalCustomers],
    ].map((row) => row.join(',')).join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const link = document.createElement('a');
    link.href = window.URL.createObjectURL(blob);
    link.download = 'loyalty_customers_by_store.csv';
    link.click();
  };

  const handleClose = (event, reason) => {
    if (reason === "clickaway") return;
    setOpen(false);
  };

  return (
    <Box>
      <Grid container alignItems="center" justifyContent="space-between" mb={2}>
        <Grid item>
          <Typography variant="h4" fontWeight="bold">
            Loyalty Customers By Store
          </Typography>
        </Grid>
        <Grid item>
          <Box sx={{ display: 'flex', gap: 2 }}>
            <Button color="primary" variant="contained" onClick={() => setFilterOpen(!filterOpen)}>
              Filter <FilterList sx={{ ml: 1 }} fontSize="small" />
            </Button>
            <Button variant="contained" onClick={handleExport}>
              Export as CSV
            </Button>
          </Box>
        </Grid>
      </Grid>

      <Collapse in={filterOpen} sx={{ mb: 2 }}>
        <Card>
          <Box p={3}>
            <Grid container spacing={2} alignItems="center">
              <Grid item xs={12} md={4}>
                <InputLabel>Start Date</InputLabel>
                <TextField 
                  type="date" 
                  fullWidth 
                  variant="outlined" 
                  value={startDate || ''} 
                  onChange={(e) => setStartDate(e.target.value)} 
                  InputLabelProps={{ shrink: true }}
                />
              </Grid>
              <Grid item xs={12} md={4}>
                <InputLabel>End Date</InputLabel>
                <TextField 
                  type="date" 
                  fullWidth 
                  variant="outlined" 
                  value={endDate || ''} 
                  onChange={(e) => setEndDate(e.target.value)} 
                  InputLabelProps={{ shrink: true }}
                />
              </Grid>
              <Grid item xs={12} md={4}>
                <Button variant="contained" onClick={handleFilter} sx={{ mt: 2,ml:4 }}>
                  Filter
                </Button>
              </Grid>
            </Grid>
          </Box>
        </Card>
      </Collapse>

      {Object.keys(activeFilters).length > 0 && (
        <Box sx={{ display: 'flex', gap: 1, mb: 4, mt: 4, flexWrap: 'wrap' }}>
          {activeFilters.startDate && (
            <Chip label={`Start Date: ${activeFilters.startDate}`} onDelete={() => handleRemoveFilter('startDate')} />
          )}
          {activeFilters.endDate && (
            <Chip label={`End Date: ${activeFilters.endDate}`} onDelete={() => handleRemoveFilter('endDate')} />
          )}
        </Box>
      )}

      {loading ? (
        <Box sx={{ textAlign: 'center', py: 4 }}>
          <CircularProgress />
        </Box>
      ) : (
        <TableContainer component={Paper}>
          <Table>
            <TableHead sx={{ backgroundColor: '#f3f4f6' }}>
              <TableRow>
                <TableCell><strong>S.N</strong></TableCell>
                <TableCell><strong>Store</strong></TableCell>
                <TableCell><strong>Total Customers</strong></TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {data.map((item, index) => (
                <TableRow key={item.id}>
                  <TableCell>{index + 1}</TableCell>
                  <TableCell>{item.manualStoreCode.trim()}</TableCell>
                  <TableCell>{item.total}</TableCell>
                </TableRow>
              ))}
              <TableRow>
                <TableCell />
                <TableCell><strong>Total Customers</strong></TableCell>
                <TableCell><strong>{totalCustomers}</strong></TableCell>
              </TableRow>
            </TableBody>
          </Table>
        </TableContainer>
      )}

      <Snackbar autoHideDuration={3000} anchorOrigin={{ vertical: "top", horizontal: "right" }} open={open} onClose={handleClose}>
        <Alert onClose={handleClose} severity={messageState} sx={{ width: "100%" }}>
          {message}
        </Alert>
      </Snackbar>
    </Box>
  );
}
