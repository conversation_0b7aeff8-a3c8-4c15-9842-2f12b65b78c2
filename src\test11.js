import { Close } from "@mui/icons-material";
import { Autocomplete, Button, CircularProgress, FormControl, FormControlLabel, Grid, IconButton, InputLabel, MenuItem, Radio, RadioGroup, Select, TextField } from "@mui/material";
import React, { useState } from "react";

const OutputDetails = (props) => {
    const [categoryValue, setCategoryValue] = useState([]);
    const [productValue, setProductValue] = useState([]);
    return (
        <div>
            <h4>Output Details</h4>
            {props.outputDetails.map((detail, index) => (
                <Grid container spacing={2}>

                    <Grid item xs={12} md={6}>
                        <>
                            <div key={index} style={{ marginBottom: "20px" }}>

                                {index > 0 && (
                                    <div style={{ marginBottom: "10px" }}>
                                        <h4 style={{ margin: 0, fontWeight: "inherit" }}>Condition</h4>

                                        <FormControl>
                                            <RadioGroup
                                                row
                                                aria-labelledby={`condition-radio-buttons-${index}`}
                                                name={`condition-${index}`}
                                                value={detail.condition}
                                                onChange={(e) =>
                                                    props.updateOutputDetail(index, "condition", e.target.value)
                                                }
                                            >
                                                <FormControlLabel value="and" control={<Radio />} label="AND" />
                                                <FormControlLabel value="or" control={<Radio />} label="OR" />
                                            </RadioGroup>
                                        </FormControl>
                                    </div>
                                )}

                                <div>
                                    <FormControl fullWidth>
                                        <InputLabel>{props.outputListLoading ? "Please Wait.." : "Output Type"}</InputLabel>
                                        <Select
                                            value={detail.input_output_id}
                                            label={props.outputListLoading ? "Please Wait.." : "Output Type"}
                                            onChange={(e) =>
                                                props.updateOutputDetail(index, "input_output_id", e.target.value)
                                            }
                                            renderValue={(selected) => {
                                                const selectedItem = props.outputData.find(item => item.id === selected);
                                                return selectedItem ? selectedItem.name : selected;
                                            }}

                                        >
                                            <MenuItem value="">Select Output</MenuItem>
                                            {!props.view && props.outputData.map((item) => (
                                                <MenuItem
                                                    key={item.id}
                                                    value={item.id}
                                                    disabled={props.outputDetails.some(
                                                        (output, idx) => output.input_output_id === item.id && idx !== index
                                                    )}>
                                                    {item.name}
                                                </MenuItem>
                                            ))}
                                        </Select>
                                        {props.outputListLoading && (
                                            <CircularProgress
                                                size={24}
                                                style={{
                                                    position: "absolute",
                                                    top: "50%",
                                                    right: 16,
                                                    marginTop: -12,
                                                    marginRight: 10,
                                                }}
                                            />
                                        )}
                                    </FormControl>
                                </div>


                            </div>
                        </>
                    </Grid>
                    <Grid item xs={12} md={6}>
                        <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>
                            {detail.input_output_id && (
                                <div style={{ marginTop: index > 0 ? "75px" : "0px" }}>
                                    {(() => {
                                        const selectedOutput = props.outputData.find(
                                            (item) => item.id === parseInt(detail.input_output_id)
                                        );
                                        // const [categoryValue, setCategoryValue] = useState(detail.categoryValue || []);
                                        //     const [productValue, setProductValue] = useState(detail.productValue || []);

                                        // Function to handle update and sync both category & product values
                                        const handleUpdate = (newCategoryValue, newProductValue) => {
                                            setCategoryValue(newCategoryValue);
                                            setProductValue(newProductValue);

                                            // Ensure both values are passed
                                            props.updateOutputDetail(index, "value", newCategoryValue, newProductValue);
                                        };

                                        if (selectedOutput?.input_type === "text" && selectedOutput.is_value_required) {
                                            return (
                                                <div>
                                                    <TextField
                                                        fullWidth
                                                        sx={{ minWidth: "400px" }}
                                                        required
                                                        label="Output Value"
                                                        placeholder={selectedOutput.symbol || ""}
                                                        value={detail.value}
                                                        onChange={(e) =>
                                                            props.updateOutputDetail(index, "value", e.target.value)
                                                        }
                                                    />
                                                </div>
                                            );
                                        }

                                        if (selectedOutput?.input_type === "list" && selectedOutput?.input_output_code === "GFIFCOLI" && selectedOutput.is_value_required) {
                                            // Local state for controlled inputs

                                            return (
                                                <div>
                                                    {/* <FormControl fullWidth>
                                                        <InputLabel>{props.productListLoading ? "Please Wait.." : "Select Value"}</InputLabel>
                                                        <Select
                                                            label={props.productListLoading ? "Please Wait.." : "Select Value"}
                                                            onChange={(e) =>
                                                                props.updateOutputDetail(index, "value", e.target.value)
                                                            }
                                                            value={detail.value}
                                                            sx={{ minWidth: "400px" }}
                                                        >
                                                            <MenuItem value="">Select from list</MenuItem>
                                                            {props.productList.map((item) => (
                                                                <MenuItem value={String(item.id)}>
                                                                    {item.name}
                                                                </MenuItem>
                                                            ))}
                                                        </Select>
                                                        {props.productListLoading && (
                                                            <CircularProgress
                                                                size={24}
                                                                style={{
                                                                    position: "absolute",
                                                                    top: "50%",
                                                                    right: 16,
                                                                    marginTop: -12,
                                                                    marginRight: 10,
                                                                }}
                                                            />
                                                        )}
                                                    </FormControl> */}

                                                    {/* <FormControl fullWidth>
                                                        <Autocomplete
                                                            options={props.productList || []}
                                                            getOptionLabel={(option) => option.name || ""}
                                                            isOptionEqualToValue={(option, value) => option.id == value.id}
                                                            value={detail.value ? props.productList.find(item => item.id == detail.value) : null}
                                                            onChange={(event, newValue) =>
                                                                props.updateOutputDetail(index, "value", newValue ? newValue.id : "")
                                                            }
                                                            loading={props.productListLoading}
                                                            renderInput={(params) => (
                                                                <TextField
                                                                    {...params}
                                                                    label={props.productListLoading ? "Please Wait..." : "Select Value"}
                                                                    sx={{ minWidth: "400px" }}
                                                                    InputProps={{
                                                                        ...params.InputProps,
                                                                        endAdornment: (
                                                                            <>
                                                                                {props.productListLoading ? (
                                                                                    <CircularProgress color="inherit" size={20} />
                                                                                ) : null}
                                                                                {params.InputProps.endAdornment}
                                                                            </>
                                                                        ),
                                                                    }}
                                                                />
                                                            )}
                                                        />
                                                    </FormControl> */}
                                                    {/* <FormControl fullWidth>
                                                        <Autocomplete
                                                            multiple
                                                            options={!props.view ? props.productCategoryList || [] : []}
                                                            getOptionLabel={(option) => option.name || ""}
                                                            isOptionEqualToValue={(option, value) => option.id == value.id}
                                                            value={detail.value
                                                                ? props.productCategoryList.filter(item => detail.value.includes(item.id)) // Ensure selected items match
                                                                : []
                                                            }
                                                            onChange={(event, newValue) =>
                                                                props.updateOutputDetail(index, "value", newValue.map(item => item.id)) // Store array of IDs
                                                            }
                                                            loading={props.productCategoryLoading}
                                                            renderInput={(params) => (
                                                                <TextField
                                                                    {...params}
                                                                    label={props.productCategoryLoading ? "Please Wait..." : "Select Category"}
                                                                    sx={{ minWidth: "400px" }}
                                                                    InputProps={{
                                                                        ...params.InputProps,
                                                                        endAdornment: (
                                                                            <>
                                                                                {props.productCategoryLoading ? (
                                                                                    <CircularProgress color="inherit" size={20} />
                                                                                ) : null}
                                                                                {params.InputProps.endAdornment}
                                                                            </>
                                                                        ),
                                                                    }}
                                                                />
                                                            )}
                                                        />
                                                    </FormControl>
                                                    <FormControl fullWidth>
                                                        <Autocomplete
                                                            multiple
                                                            options={!props.view ? props.productList || [] : []}
                                                            getOptionLabel={(option) => option.name || ""}
                                                            isOptionEqualToValue={(option, value) => option.id == value.id}
                                                            value={detail.value
                                                                ? props.productList.filter(item => detail.value.includes(item.id)) // Ensure selected items match
                                                                : []
                                                            }
                                                            onChange={(event, newValue) =>
                                                                props.updateOutputDetail(index, "value", newValue.map(item => item.id)) // Store array of IDs
                                                            }
                                                            loading={props.productListLoading}
                                                            renderInput={(params) => (
                                                                <TextField
                                                                    {...params}
                                                                    label={props.productListLoading ? "Please Wait..." : "Select Values"}
                                                                    sx={{ minWidth: "400px" }}
                                                                    InputProps={{
                                                                        ...params.InputProps,
                                                                        endAdornment: (
                                                                            <>
                                                                                {props.productListLoading ? (
                                                                                    <CircularProgress color="inherit" size={20} />
                                                                                ) : null}
                                                                                {params.InputProps.endAdornment}
                                                                            </>
                                                                        ),
                                                                    }}
                                                                />
                                                            )}
                                                        />
                                                    </FormControl> */}
                                                    <Grid container spacing={2}>
                                                        {/* Category Selection */}
                                                        <Grid item xs={4}>
                                                            <FormControl fullWidth>
                                                                <Autocomplete
                                                                    multiple
                                                                    options={props.productCategoryList || []}
                                                                    getOptionLabel={(option) => option.name || ""}
                                                                    isOptionEqualToValue={(option, value) => option.id === value.id}
                                                                    value={categoryValue}
                                                                    onChange={(event, newValue) => handleUpdate(newValue.map((item) => item.id), productValue)}
                                                                    loading={props.productCategoryLoading}
                                                                    renderInput={(params) => (
                                                                        <TextField
                                                                            {...params}
                                                                            label={props.productCategoryLoading ? "Please Wait..." : "Select Category"}
                                                                            sx={{ minWidth: "400px" }}
                                                                            InputProps={{
                                                                                ...params.InputProps,
                                                                                endAdornment: (
                                                                                    <>
                                                                                        {props.productCategoryLoading ? <CircularProgress color="inherit" size={20} /> : null}
                                                                                        {params.InputProps.endAdornment}
                                                                                    </>
                                                                                ),
                                                                            }}
                                                                        />
                                                                    )}
                                                                />
                                                            </FormControl>
                                                        </Grid>

                                                        {/* Product Selection */}
                                                        <Grid item xs={4}>
                                                            <FormControl fullWidth>
                                                                <Autocomplete
                                                                    multiple
                                                                    options={props.productList || []}
                                                                    getOptionLabel={(option) => option.name || ""}
                                                                    isOptionEqualToValue={(option, value) => option.id === value.id}
                                                                    value={productValue}
                                                                    onChange={(event, newValue) => handleUpdate(categoryValue, newValue.map((item) => item.id))}
                                                                    loading={props.productListLoading}
                                                                    renderInput={(params) => (
                                                                        <TextField
                                                                            {...params}
                                                                            label={props.productListLoading ? "Please Wait..." : "Select Products"}
                                                                            sx={{ minWidth: "400px" }}
                                                                            InputProps={{
                                                                                ...params.InputProps,
                                                                                endAdornment: (
                                                                                    <>
                                                                                        {props.productListLoading ? <CircularProgress color="inherit" size={20} /> : null}
                                                                                        {params.InputProps.endAdornment}
                                                                                    </>
                                                                                ),
                                                                            }}
                                                                        />
                                                                    )}
                                                                />
                                                            </FormControl>
                                                        </Grid>
                                                    </Grid>

                                                </div>
                                            );
                                        }

                                        if (selectedOutput?.input_type === "list" && selectedOutput.is_value_required) {
                                            return (
                                                <div>
                                                    <FormControl fullWidth>
                                                        <Autocomplete
                                                            multiple
                                                            options={!props.view ? props.productList || [] : []}
                                                            getOptionLabel={(option) => option.name || ""}
                                                            isOptionEqualToValue={(option, value) => option.id == value.id}
                                                            value={detail.value
                                                                ? props.productList.filter(item => detail.value.includes(item.id)) // Ensure selected items match
                                                                : []
                                                            }
                                                            onChange={(event, newValue) =>
                                                                props.updateOutputDetail(index, "value", newValue.map(item => item.id)) // Store array of IDs
                                                            }
                                                            loading={props.productListLoading}
                                                            renderInput={(params) => (
                                                                <TextField
                                                                    {...params}
                                                                    label={props.productListLoading ? "Please Wait..." : "Select Values"}
                                                                    sx={{ minWidth: "400px" }}
                                                                    InputProps={{
                                                                        ...params.InputProps,
                                                                        endAdornment: (
                                                                            <>
                                                                                {props.productListLoading ? (
                                                                                    <CircularProgress color="inherit" size={20} />
                                                                                ) : null}
                                                                                {params.InputProps.endAdornment}
                                                                            </>
                                                                        ),
                                                                    }}
                                                                />
                                                            )}
                                                        />
                                                    </FormControl>

                                                </div>
                                            );
                                        }
                                    })()}
                                </div>
                            )}

                            {!props.view && props.outputDetails.length > 1 && (
                                <IconButton
                                    style={{ marginLeft: "20px" }}
                                    onClick={() => props.removeOutputDetail(index)}
                                >
                                    <Close color={"error"} />
                                </IconButton>
                            )}
                        </div>
                    </Grid>
                </Grid>
            ))}

            {!props.view && <Button variant={"contained"} onClick={props.addOutputDetail} disabled={props.isAddButtonDisabled}>+ Add Output</Button>}
        </div>

    );
};

export default OutputDetails;
