import * as React from "react";
import { useState, useEffect } from "react";
import { styled, useTheme } from "@mui/material/styles";
import Box from "@mui/material/Box";
import MuiDrawer from "@mui/material/Drawer";
import MuiAppBar from "@mui/material/AppBar";
import Toolbar from "@mui/material/Toolbar";
import List from "@mui/material/List";
import CssBaseline from "@mui/material/CssBaseline";
import Divider from "@mui/material/Divider";
import IconButton from "@mui/material/IconButton";
import MenuIcon from "@mui/icons-material/Menu";
import ListItem from "@mui/material/ListItem";
import ListItemButton from "@mui/material/ListItemButton";
import ListItemIcon from "@mui/material/ListItemIcon";
import ListItemText from "@mui/material/ListItemText";
import {
  ChevronLeft,
  ChevronRight,
  Dashboard,
  Dns,
  People,
  Inventory,
  KeyboardArrowDown,
  KeyboardArrowUp,
  Groups,
  ViewStream,
  Settings,
  GppMaybe,
  Adjust,
  LocalShipping,
  Sync,
  Navigation,
  ShoppingCart,
  UTurnRight,
  ThreeSixty,
} from "@mui/icons-material";
import { Link, Outlet, useLocation, useNavigate } from "react-router-dom";
import {
  Avatar,
  ClickAwayListener,
  Collapse,
  Grow,
  MenuItem,
  MenuList,
  Paper,
  Popper,
  SwipeableDrawer,
  Snackbar,
  Button,
  CircularProgress,
} from "@mui/material";
import MuiAlert from "@mui/material/Alert";
import httpclient from "../../Utils";

import Img from "../Images/synccare.png"
import Footer from "../Footer";
import { useTimer } from "../Context/TimerContext";

const Alert = React.forwardRef(function Alert(props, ref) {
  return <MuiAlert elevation={6} ref={ref} variant="filled" {...props} />;
});


const drawerWidth = 320;

const openedMixin = (theme) => ({
  width: drawerWidth,

  backgroundColor: theme.palette.primary.dark,
  transition: theme.transitions.create("width", {
    easing: theme.transitions.easing.sharp,
    duration: theme.transitions.duration.enteringScreen,
  }),
  overflowX: "hidden",
});

const closedMixin = (theme) => ({
  transition: theme.transitions.create("width", {
    easing: theme.transitions.easing.sharp,
    duration: theme.transitions.duration.leavingScreen,
  }),
  overflowX: "hidden",

  width: "0px",

  backgroundColor: theme.palette.primary.dark,
});

const DrawerHeader = styled("div")(({ theme }) => ({
  position: "relative",
  display: "flex",
  alignItems: "center",
  padding: theme.spacing(0, 1),
  marginLeft: "20px",
  // necessary for content to be below app bar
  ...theme.mixins.toolbar,
  "& img": {
    width: "200px",
    // height: "50px",
    marginTop: "7px",
  },
  "& a": {
    textDecoration: "none",
    color: theme.palette.primary.dark,
    color: theme.palette.primary.light,
    display: "flex",
    alignItems: "center",
  },
  "& h3": {
    letterSpacing: "1px",
    fontSize: "25px",
    margin: "0",
  },
  "& small": {
    fontSize: "13px",
    position: "relative",
    top: "10px",
    left: "-3px",
  },
}));

const AppBar = styled(MuiAppBar, {
  shouldForwardProp: (prop) => prop !== "open",
})(({ theme, open }) => ({
  backgroundColor: theme.palette.primary.main,
  zIndex: theme.zIndex.drawer + 1,
  transition: theme.transitions.create(["width", "margin"], {
    easing: theme.transitions.easing.sharp,
    duration: theme.transitions.duration.leavingScreen,
  }),
  ...(open && {
    marginLeft: drawerWidth,
    width: `calc(100% - ${drawerWidth}px)`,
    transition: theme.transitions.create(["width", "margin"], {
      easing: theme.transitions.easing.sharp,
      duration: theme.transitions.duration.enteringScreen,
    }),
    [theme.breakpoints.down("md")]: {
      width: "100%",
      zIndex: theme.zIndex.drawer - 1,
    },
  }),
}));

const Drawer = styled(MuiDrawer, {
  shouldForwardProp: (prop) => prop !== "open",
})(({ theme, open }) => ({
  width: drawerWidth,
  backgroundColor: theme.palette.primary.dark,

  flexShrink: 0,
  whiteSpace: "nowrap",
  boxSizing: "border-box",
  ...(open && {
    ...openedMixin(theme),
    "& .MuiDrawer-paper": openedMixin(theme),
  }),
  ...(!open && {
    ...closedMixin(theme),
    "& .MuiDrawer-paper": closedMixin(theme),
  }),
}));

const Swipeable = styled(SwipeableDrawer)(({ theme }) => ({
  "& .MuiPaper-root": {
    backgroundColor: theme.palette.primary.dark,
  },
}));



const MenuListButton = styled(ListItemButton)(({ theme }) => ({
  opacity: "0.7",
  transition: "0.3s",
  fontWeight: "400",
  margin: "5px 8px",
  borderRadius: "10px",

  color: theme.palette.primary.light,
  "& svg": {
    color: theme.palette.primary.light,
    fontSize: "22px",
  },
  "& span": {
    fontWeight: "600",
    fontSize: "15px",
  },
  "&:hover": {
    opacity: "1",
  },
  "&:active": {
    opacity: "1",
  },
  "&:focus": {
    opacity: "1",
  },
}));

const AvatarDiv = styled("div")(({ theme }) => ({
  display: "flex",
  flexDirection: "row",
  alignItems: "center",
  "& span": {
    color: theme.palette.primary.light,
    fontSize: "18px",
    marginRight: "10px",
    [theme.breakpoints.down("md")]: {
      display: "none",
    },
  },
}));

const MultiLevelList = styled(List)(({ theme }) => ({
  marginLeft: "30px",
  // borderLeft: `2px solid ${theme.palette.primary.light}`,
  borderLeft: `2px solid ${theme.palette.primary.dark}`,
}));

const menuList = [

  { menu: "Dashboard", link: "/", icon: <Dashboard /> },
  {
    menu: "Comapnies",
    link: "/projects",
    icon: <ShoppingCart />,
    multiLevel: false,

  },

  {
    menu: "Roles",
    link: "/roles",
    icon: <ShoppingCart />,
    multiLevel: false,

  },

  {
    menu: "Users",
    link: "/users",
    icon: <ShoppingCart />,
    multiLevel: false,
    // multiMenus: [
    //   { menuName: "Products", link: "/test/subtest" },
    //   { menuName: "Productsss", link: "/test/subtest1" },
    // ],
  },
]

const CompanyDiv = styled("div")(({ theme }) => ({
  fontSize: "20px",
  fontWeight: "700",
  letterSpacing: "1px",
  "& span": {
    fontFamily: "Poppins",
    fontSize: "12px",
    fontWeight: "600",
  }
}));


export default function MiniDrawer({ children, menuList, projects, handleMoveProject }) {
  const seconds = useTimer();
  const theme = useTheme();
  const location = useLocation();
  const anchorRef = React.useRef(null);
  const [open, setOpen] = React.useState(true);
  const [mobileOpen, setMobileOpen] = React.useState(true);
  const [openPopper, setOpenPopper] = React.useState(false);
  const [currMenuName, setCurrMenuName] = React.useState("");
  const [menuLevelOpen, setMenuLevelOpen] = React.useState(false);
  const [screenWidth, setScreenWidth] = React.useState(window.innerWidth);
  const navigate = useNavigate();
  var loginData = localStorage.getItem("user");
  var loginValue = JSON.parse(loginData);


  React.useEffect(() => {
    resize();
    window.addEventListener("resize", resize);
    return () => {
      window.removeEventListener("resize", resize);
    };
  });

  const resize = () => {
    setScreenWidth(window.innerWidth);
  };

  const handleDrawerOpen = () => {
    setOpen((prev) => !prev);
  };

  const handleDrawerMobileOpen = () => {
    setMobileOpen(true);
  };

  const handleDrawerMobileClose = () => {
    setMobileOpen(false);
  };

  const handleLogout = () => {
    localStorage.clear();
    sessionStorage.clear();
    setOpenPopper(false);
    navigate("/login");
  };




  const handlePopperOpen = () => {
    setOpenPopper((prev) => !prev);
  };

  const handleClosePopper = (event) => {
    if (anchorRef.current && anchorRef.current.contains(event.target)) {
      return;
    }

    setOpenPopper(false);
  };

  const handleListKeyDown = (event) => {
    if (event.key === "Tab") {
      event.preventDefault();
      setOpenPopper(false);
    } else if (event.key === "Escape") {
      setOpenPopper(false);
    }
  };

  const MenuDiv = styled("div")(({ theme }) => ({
    display: "none",
    [theme.breakpoints.down("md")]: {
      display: mobileOpen ? "none" : "block",
    },
  }));

  const DesktopMenuDiv = styled("div")(({ theme }) => ({
    display: "block",
    [theme.breakpoints.down("md")]: {
      display: "none",
    },
  }));


  const handleNavigation = (menu) => {
    navigate(`${menu.path}`);

  }

  const handleMenuDown = (name) => {
    setCurrMenuName(name);
    if (name !== currMenuName) {
      setMenuLevelOpen(true);
    } else if (name === currMenuName) {
      setMenuLevelOpen((prev) => !prev);
    } else {
      setMenuLevelOpen(false);
    }
  };

  return (
    <Box sx={{ display: "flex" }}>
      <CssBaseline />

      {/* NAVBAR */}
      <AppBar position="fixed" open={screenWidth > 768 ? open : mobileOpen}>
        <Toolbar
          sx={{
            display: "flex",
            alignItems: "center",
            justifyContent: "space-between",
          }}
        >
          <DesktopMenuDiv>
            <IconButton onClick={handleDrawerOpen}>
              {open ? (
                <ChevronLeft fontSize="large" style={{ color: "#fff" }} />
              ) : (
                <MenuIcon fontSize="large" style={{ color: "#fff" }} />
              )}
            </IconButton>
          </DesktopMenuDiv>

          <MenuDiv>
            <IconButton onClick={handleDrawerMobileOpen}>
              <MenuIcon fontSize="large" style={{ color: "#fff" }} />
            </IconButton>
          </MenuDiv>

          <CompanyDiv>
            {loginValue.company_name}
            <span style={{ marginLeft: "12px" }}>Refreshing Dashboard On: <strong>00:{String(seconds).padStart(2, '0')}</strong></span>
          </CompanyDiv>


          <AvatarDiv>
            <span>
              {loginValue.full_name}
            </span>
            <Avatar
              alt={loginValue.full_name}
              sx={{
                width: 43,
                height: 43,
                cursor: "pointer",
                border: `2px solid #fafafa`,
              }}
              onClick={handlePopperOpen}
              ref={anchorRef}
            />

            <Popper
              open={openPopper}
              anchorEl={anchorRef.current}
              role={undefined}
              placement="bottom-start"
              transition
              disablePortal
            >
              {({ TransitionProps, placement }) => (
                <Grow
                  {...TransitionProps}
                  style={{
                    transformOrigin:
                      placement === "bottom-start" ? "left top" : "left bottom",
                  }}
                >
                  <Paper>
                    <ClickAwayListener onClickAway={handleClosePopper}>
                      <MenuList
                        autoFocusItem={openPopper}
                        id="composition-menu"
                        aria-labelledby="composition-button"
                        onKeyDown={handleListKeyDown}
                      > {loginValue && loginValue.is_project_shift_user === 1 ? (
                        <>
                          <MenuItem><strong>{loginValue.company_name}</strong></MenuItem>
                          <MenuItem><i>Switch Project</i><ThreeSixty sx={{ marginLeft: "5px" }} /></MenuItem>
                          {projects?.map((p) => (
                            <MenuItem key={p.id} disabled={p.name === loginValue.company_name} onClick={() => handleMoveProject(p.project_id)}>
                              {p.name}
                            </MenuItem>
                          ))}
                        </>
                      ) : null}
                        <MenuItem sx={{ borderTop: "2px solid black" }} onClick={handleLogout}>Logout</MenuItem>

                      </MenuList>
                    </ClickAwayListener>
                  </Paper>
                </Grow>
              )}
            </Popper>
          </AvatarDiv>

        </Toolbar>
      </AppBar>
      {/* NAVBAR */}

      {/* SIDEBAR OR DRAWER */}
      {screenWidth > 768 ? (
        <Drawer open={open} variant="permanent">
          <DrawerHeader>
            <Link to="/">
              {/* <h3>Sync Care</h3> */}
              <img src={Img} alt="synccare_logo" />
              {/*<small>{Global.version}</small> */}
            </Link>
          </DrawerHeader>
          <Divider />

          <List>
            {menuList.map((list, index) => (
              <React.Fragment key={`${list.name}-${index}`}>
                <ListItem
                  disablePadding
                  sx={{ display: "block" }}
                >
                  <Link to={list.path} style={{ textDecoration: "none" }}>
                    <MenuListButton
                      sx={{
                        minHeight: 48,
                        justifyContent: open ? "initial" : "center",
                        px: 2.5,
                        background:
                          location.pathname === list.path
                            ? theme.palette.secondary.main
                            : "transparent",
                        opacity:
                          location.pathname === list.path ? "1" : "0.7",
                      }}
                      onClick={() =>
                        handleMenuDown(list.sub_menu ? list.name : "")
                      }
                    >
                      {/* <ListItemIcon
                          sx={{
                            minWidth: 0,
                            mr: open ? 3 : "auto",
                            justifyContent: "center",
                          }}
                        >
                          <img src={list.icon} width="40px"/>
                        </ListItemIcon> */}
                      <ListItemText
                        primary={list.name}
                        sx={{ opacity: open ? 1 : 0 }}
                      />
                      {list.sub_menu ? (
                        currMenuName === list.name ? (
                          <KeyboardArrowDown />
                        ) : (
                          <KeyboardArrowUp />
                        )
                      ) : null}
                    </MenuListButton>
                  </Link>
                </ListItem>

                {list.sub_menu && currMenuName === list.name && (
                  <Collapse in={menuLevelOpen} timeout="auto" unmountOnExit>
                    <Divider />
                    <MultiLevelList>
                      {list.sub_menu.map((sub, subIndex) => (
                        <ListItem disablePadding sx={{ display: "block" }} key={sub.name}>
                          <Link
                            to={sub.path}
                            style={{ textDecoration: "none" }}
                          >
                            <MenuListButton
                              sx={{
                                minHeight: 48,
                                justifyContent: open ? "initial" : "center",
                                px: 2.5,
                                background:
                                  location.pathname === sub.path
                                    ? theme.palette.secondary.main
                                    : "transparent",
                                opacity:
                                  location.pathname === sub.path
                                    ? "1"
                                    : "0.7",
                              }}
                            >
                              <ListItemText
                                primary={sub.name}
                                sx={{ opacity: open ? 1 : 0 }}
                              />
                              <ChevronRight />
                            </MenuListButton>
                          </Link>
                        </ListItem>
                      ))}
                    </MultiLevelList>
                  </Collapse>
                )}
              </React.Fragment>
            ))}



          </List>

          {/* {menuList.map((menu, index) => (
            <ListItem key={menu.id} disablePadding>
              <MenuListButton onClick={() => handleNavigation(menu)} >
                <ListItemIcon>
                 
                </ListItemIcon>
                <ListItemText primary={menu.name} />
              </MenuListButton>
            </ListItem>
          ))} */}


        </Drawer>
      ) : (
        <Swipeable
          anchor="left"
          open={mobileOpen}
          onOpen={handleDrawerMobileOpen}
          onClose={handleDrawerMobileClose}
        >
          <DrawerHeader>
            <Link to="/">
              <img src={Img} alt="synccare_logo" />
            </Link>
          </DrawerHeader>
          <Divider />
          <List>
            {menuList.map((list, index) => (
              <React.Fragment key={`${list.name}-${index}`}>
                <ListItem
                  disablePadding
                  sx={{ display: "block" }}
                >
                  <Link to={list.path} style={{ textDecoration: "none" }}>
                    <MenuListButton
                      sx={{
                        minHeight: 48,
                        justifyContent: open ? "initial" : "center",
                        px: 2.5,
                        background:
                          location.pathname === list.path
                            ? theme.palette.secondary.main
                            : "transparent",
                        opacity:
                          location.pathname === list.path ? "1" : "0.7",
                      }}
                      onClick={() =>
                        handleMenuDown(list.sub_menu ? list.name : "")
                      }
                    >
                      <ListItemIcon
                        sx={{
                          minWidth: 0,
                          mr: open ? 1.6 : "auto",
                          justifyContent: "center",
                        }}
                      >
                      </ListItemIcon>
                      <ListItemText
                        primary={list.name}
                        sx={{ opacity: open ? 1 : 0 }}
                      />
                      {list.sub_menu ? (
                        currMenuName === list.name ? (
                          <KeyboardArrowDown />
                        ) : (
                          <KeyboardArrowUp />
                        )
                      ) : null}
                    </MenuListButton>
                  </Link>
                </ListItem>

                {list.sub_menu && currMenuName === list.name && (
                  <Collapse in={menuLevelOpen} timeout="auto" unmountOnExit>
                    <Divider />
                    <MultiLevelList>
                      {list.sub_menu.map((sub, subIndex) => (
                        <ListItem disablePadding sx={{ display: "block" }} key={sub.name}>
                          <Link
                            to={sub.path}
                            style={{ textDecoration: "none" }}
                          >
                            <MenuListButton
                              sx={{
                                minHeight: 48,
                                justifyContent: open ? "initial" : "center",
                                px: 2.5,
                                background:
                                  location.pathname === sub.path
                                    ? theme.palette.secondary.main
                                    : "transparent",
                                opacity:
                                  location.pathname === sub.path
                                    ? "1"
                                    : "0.7",
                              }}
                            >
                              <ListItemText
                                primary={sub.name}
                                sx={{ opacity: open ? 1 : 0 }}
                              />
                              <ChevronRight />
                            </MenuListButton>
                          </Link>
                        </ListItem>
                      ))}
                    </MultiLevelList>
                  </Collapse>
                )}
              </React.Fragment>
            ))}



          </List>
          {/* {menuList.map((menu, index) => (
            <ListItem key={menu.id} disablePadding>
              <MenuListButton onClick={() => handleNavigation(menu)} >
                <ListItemIcon>
       
                </ListItemIcon>
                <ListItemText primary={menu.name} />
              </MenuListButton>
            </ListItem>
          ))} */}

        </Swipeable>
      )}
      <Box
        component="main"
        sx={{
          flexGrow: 1,
          p: 3,
          height: "100% !important",
          overflowX: "hidden",
        }}
      >
        <DrawerHeader />
        {children}
      </Box>
    </Box>
  );
}
