import {
    Box,
    Button,
    Dialog,
    DialogActions,
    DialogContent,
    DialogTitle,
    Grid,
    styled,
    TextField,
} from "@mui/material";
import { useEffect, useState } from "react";
import * as React from 'react';


const StyledHeaderTitle = styled(DialogTitle)(({ theme }) => ({
    background: theme.palette.primary.main,
    color: "#fff",
    position: "relative",
    "& button": {
        position: "absolute",
        right: "15px",
        top: "15px",
        color: "#fff",
    },
}));

const EditDialogCountry = (props) => {
    const [dialogDetails, setDialogDetails] = useState({
        open: true,
        success: false,
    });
    const [errors, setErrors] = useState({});
    const [formData, setFormData] = useState({
        country_name: '',
        country_id: '',
        type:"country"
        
    });

    useEffect(() => {
        if (props.viewDetails) {
            setFormData({
                country_name: props.viewDetails.name || '',
                country_id: props.viewDetails.id || '',
                 type:"country"
            });
        }
    }, [props.viewDetails]);


    useEffect(() => {
        props.sendEdit(dialogDetails, formData);
    }, [dialogDetails]);


    const handleClose = () => {
        setDialogDetails({
            ...dialogDetails,
            open: false,
        });
    };

    const handleYes = () => {
        if (validate()) {
            setDialogDetails({
                ...dialogDetails,
                open: false,
                success: true,

            });
        }
    };

    const validate = () => {
        const newErrors = {};
        if (!formData.country_name) newErrors.country_name = 'Country Name is required';
        // if (!formData.country_id) newErrors.country_id = 'Country ID is required';
   
        setErrors(newErrors);
        return Object.keys(newErrors).length === 0;
    };


    const handleChange = (e) => {
        const { name, value } = e.target;
        setFormData((prevData) => ({
            ...prevData,
            [name]: value,
        }));
    };

    return (
        <div>
            <Dialog
                open={dialogDetails.open}
                onClose={handleClose}
                maxWidth="md"
                fullWidth
                aria-labelledby="alert-dialog-title"
                aria-describedby="alert-dialog-description"
            >
                <StyledHeaderTitle id="alert-dialog-title">
                    {props.viewDetails.id ? `Edit Country` : `Add Country`}
                </StyledHeaderTitle>
                <DialogContent>
                    <Box pt={3}>
                    {props.viewDetails.id && 
                    <TextField
                            required
                            disabled
                            label="Country ID"
                            name="country_id"
                            type="number"
                            value={formData.country_id}
                            onChange={handleChange}
                            error={!!errors.country_id}
                            helperText={errors.country_id}
                            fullWidth
                            margin="normal"
                        />
                    }
                        
                        <TextField
                            required
                            label="Country Name"
                            name="country_name"
                            value={formData.country_name}
                            onChange={handleChange}
                            error={!!errors.country_name}
                            helperText={errors.country_name}
                            fullWidth
                            margin="normal"
                        />
                        
                        
                    </Box>
                </DialogContent>
                <DialogActions styled={{ margin: "5px 10px" }}>
                    <Button onClick={handleClose} color="error" variant="contained" autoFocus>
                        Cancel
                    </Button>
                    <Button
                        onClick={handleYes}
                        color="primary"
                        variant="contained"
                        autoFocus
                    >
                        {props.viewDetails.id ? `Edit` : `Save`}
                    </Button>
                </DialogActions>
            </Dialog>
        </div>
    );
};

export default EditDialogCountry;
