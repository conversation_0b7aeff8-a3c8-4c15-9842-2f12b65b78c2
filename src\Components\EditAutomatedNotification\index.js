import React, { useEffect, useState } from 'react';
import { <PERSON>, Button, Dialog, <PERSON>alogActions, DialogContent, FormControl, Switch, TextField, MenuItem, Select, InputLabel, CircularProgress, DialogTitle } from '@mui/material';
import { styled } from '@mui/material/styles';
import httpclient from '../../Utils';

const StyledHeaderTitle = styled(DialogTitle)(({ theme }) => ({
    background: theme.palette.primary.main,
    color: "#fff",
}));

const EditAutomatedNotification = ({ open, onClose, sendEdit, isAddNew, viewDetails }) => {
    const userData = JSON.parse(localStorage.getItem("user"));
    const [dialogDetails, setDialogDetails] = useState({
        success: false,
    });
    const [validationErrors, setValidationErrors] = useState({});
    const [notificationTypes, setNotificationTypes] = useState([]);
    const [loadingTypes, setLoadingTypes] = useState(false);
    const [isSaving, setIsSaving] = useState(false);

    const [formData, setFormData] = useState({
        title: "",
        message: "",
        notify_type: "",
        notify_type_id: "",
        status: 0,
        created_by: userData?.user_name || "",
        updated_by: userData?.user_name || "",
    });

    useEffect(() => {
        console.log('Resetting form data for:', isAddNew ? 'ADD' : 'EDIT', viewDetails);
        setFormData({
            title: viewDetails?.title || "",
            message: viewDetails?.message || "",
            notify_type: viewDetails?.notify_type || "",
            notify_type_id: viewDetails?.notify_type_id || "",
            status: viewDetails?.status !== undefined ? viewDetails.status : (isAddNew ? 0 : 1),
            [isAddNew ? "created_by" : "updated_by"]: userData?.user_name || "",
        });
        setValidationErrors({});
        if (isAddNew) {
            fetchNotificationTypes();
        }
    }, [isAddNew, viewDetails, userData?.user_name]);

    const fetchNotificationTypes = async () => {
        setLoadingTypes(true);
        try {
            const response = await httpclient.get("request-response?requestName=lightspeed/notification/types");
            if (response.data?.data) {
                setNotificationTypes(response.data.data);
            }
        } catch (error) {
            console.error("Failed to fetch notification types", error);
        } finally {
            setLoadingTypes(false);
        }
    };

    const handleYes = async () => {
        let errors = {};
        if (!formData.title.trim()) errors.title = "Title is required.";
        if (!formData.message.trim()) errors.message = "Message is required.";
        if (isAddNew && !formData.notify_type_id) errors.notify_type_id = "Notification type is required.";

        if (Object.keys(errors).length > 0) {
            setValidationErrors(errors);
            return;
        }

        setIsSaving(true);
        try {
            await sendEdit({ ...dialogDetails, success: true }, formData);
            setDialogDetails({ success: true });
            setIsSaving(false);
            onClose(); // Close dialog on success
        } catch (error) {
            console.error("Failed to save notification:", error);
            setValidationErrors({
                api: error?.response?.data?.message || "Failed to save notification. Please try again.",
            });
            setIsSaving(false);
        }
    };

    const handleChange = (e) => {
        const { name, value } = e.target;
        setFormData((prevData) => ({
            ...prevData,
            [name]: value,
        }));
        validateField(name, value);
    };

    const validateField = (name, value) => {
        let errors = { ...validationErrors };
        if (name === "title" && !value.trim()) {
            errors.title = "Title is required.";
        } else if (name === "title") {
            delete errors.title;
        }
        if (name === "message" && !value.trim()) {
            errors.message = "Message is required.";
        } else if (name === "message") {
            delete errors.message;
        }
        if (name === "notify_type_id" && !value && isAddNew) {
            errors.notify_type_id = "Notification type is required.";
        } else if (name === "notify_type_id") {
            delete errors.notify_type_id;
        }
        setValidationErrors(errors);
    };

    const handleNotificationTypeChange = (e) => {
        const selectedTypeId = e.target.value;
        const selectedType = notificationTypes.find(type => type.id === selectedTypeId);
        setFormData({
            ...formData,
            notify_type_id: selectedTypeId,
            notify_type: selectedType ? selectedType.name : ''
        });
        validateField("notify_type_id", selectedTypeId);
    };

    return (
        <Dialog
            open={open}
            onClose={onClose}
            maxWidth="md"
            fullWidth
            aria-labelledby="alert-dialog-title"
            aria-describedby="alert-dialog-description"
        >
            <StyledHeaderTitle id="alert-dialog-title">
                {isAddNew ? "Add Automated Notification" : "Edit Automated Notification"}
            </StyledHeaderTitle>
            <DialogContent>
                <Box pt={3}>
                    <Box p={3} sx={{ boxShadow: "0px 4px 6px rgba(0, 0, 0, 0.1)" }}>
                        <h5 style={{ margin: 0 }}><i>{"Fields with * are mandatory"}</i></h5>
                        <Box display="flex" flexDirection="column" gap={2}>
                            {isAddNew ? (
                                <FormControl fullWidth margin="normal" error={!!validationErrors.notify_type_id}>
                                    <InputLabel id="notification-type-label">Notification Type *</InputLabel>
                                    <Select
                                        labelId="notification-type-label"
                                        id="notify_type_id"
                                        name="notify_type_id"
                                        value={formData.notify_type_id}
                                        onChange={handleNotificationTypeChange}
                                        label="Notification Type *"
                                        disabled={loadingTypes}
                                    >
                                        {loadingTypes ? (
                                            <MenuItem value="">
                                                <CircularProgress size={20} /> Loading...
                                            </MenuItem>
                                        ) : (
                                            notificationTypes.map((type) => (
                                                <MenuItem key={type.id} value={type.id}>
                                                    {type.name}
                                                </MenuItem>
                                            ))
                                        )}
                                    </Select>
                                    {validationErrors.notify_type_id && (
                                        <div className="error-text">{validationErrors.notify_type_id}</div>
                                    )}
                                </FormControl>
                            ) : (
                                <TextField
                                    label="Notification Type"
                                    name="notification_type"
                                    value={formData.notify_type}
                                    fullWidth
                                    margin="normal"
                                    disabled
                                    InputProps={{
                                        readOnly: true,
                                    }}
                                />
                            )}
                            <TextField
                                required
                                label="Title"
                                name="title"
                                value={formData.title}
                                onChange={handleChange}
                                fullWidth
                                margin="normal"
                                error={!!validationErrors.title}
                                helperText={validationErrors.title}
                            />
                            <TextField
                                required
                                label="Type message here"
                                name="message"
                                value={formData.message}
                                onChange={handleChange}
                                fullWidth
                                margin="normal"
                                multiline
                                rows={3}
                                error={!!validationErrors.message}
                                helperText={validationErrors.message}
                            />
                            <FormControl fullWidth>
                                <Box display="flex" alignItems="center">
                                    <Switch
                                        checked={formData.status === 1}
                                        onChange={(e) =>
                                            setFormData((prev) => ({
                                                ...prev,
                                                status: e.target.checked ? 1 : 0,
                                            }))
                                        }
                                    />
                                    <span>{formData.status === 1 ? "Active" : "Inactive"}</span>
                                </Box>
                            </FormControl>
                        </Box>
                    </Box>
                </Box>
            </DialogContent>
            <DialogActions sx={{ margin: "5px 10px" }}>
                <Button
                    onClick={onClose}
                    color="error"
                    variant="contained"
                    disabled={isSaving}
                >
                    Cancel
                </Button>
                <Button
                    onClick={handleYes}
                    color="primary"
                    variant="contained"
                    disabled={isSaving}
                >
                    {isSaving ? (
                        <>
                            <CircularProgress size={20} sx={{ mr: 1, color: "white" }} />
                            {isAddNew ? "Saving..." : "Updating..."}
                        </>
                    ) : (
                        isAddNew ? "Save" : "Update"
                    )}
                </Button>
            </DialogActions>
        </Dialog>
    );
};

export default EditAutomatedNotification;