// import logo from './logo.svg';
// import './App.css';

// function App() {
//   return (
//     <div className="App">
//       <header className="App-header">
//         <img src={logo} className="App-logo" alt="logo" />
//         <p>
//           Edit <code>src/App.js</code> and save to reload.
//         </p>
//         <a
//           className="App-link"
//           href="https://reactjs.org"
//           target="_blank"
//           rel="noopener noreferrer"
//         >
//           Learn React
//         </a>
//       </header>
//     </div>
//   );
// }

// export default App;
import './App.css';
import { ThemeProvider } from '@mui/material';
import Routing from './Routing';
import Theme from './Theme/Theme';
import HelmetMetaData from './Components/HelmetMetaData';
import { GlobalThemeProvider } from './Components/Context/TimerContext';

function App() {
  return (
    <>
      <GlobalThemeProvider>
        <HelmetMetaData />
        <ThemeProvider theme={Theme}>
          <Routing />
        </ThemeProvider>
      </GlobalThemeProvider>
    </>

  );
}

export default App;