import { Check, Clear, Close, Download, Sync, Visibility } from "@mui/icons-material";
import {
    AppBar,
    Box,
    Button,
    CircularProgress,
    Dialog,
    DialogActions,
    DialogContent,
    DialogTitle,
    Grid,
    IconButton,
    Skeleton,
    styled,
    useTheme,
    Snackbar
} from "@mui/material";
import moment from "moment";
import React, { useEffect, useState } from "react";
import Tabs from "@mui/material/Tabs";
import Tab from "@mui/material/Tab";
import Typography from "@mui/material/Typography";
import PropTypes from "prop-types";
//import BasicTable from "../../../../Components/BasicTable";
import FsLightbox from "fslightbox-react";

import MuiAlert from "@mui/material/Alert";
//import ViewPolicyDialog from "../../price_policy/ViewPolicyDialog";
import parse from "html-react-parser";
import BasicTable from "../../../../Components/BasicTable";


const Alert = React.forwardRef(function Alert(props, ref) {
    return <MuiAlert elevation={6} ref={ref} variant="filled" {...props} />;
});

const StyledHeaderTitle = styled(DialogTitle)(({ theme }) => ({
    background: theme.palette.primary.main,
    color: "#fff",
    position: "relative",
    "& button": {
        position: "absolute",
        right: "15px",
        top: "15px",
        color: "#fff",
    },
}));

const FlexContent = styled("div")(({ theme }) => ({
    display: "flex",
    flexDirection: "row",
    fontSize: "17px",
    marginBottom: "10px",
    alignItems: "flex-start",
}));

const FlexInnerTitle = styled("div")(({ theme }) => ({
    display: "flex",
    alignItems: "center",
    justifyContent: "space-between",
    minWidth: "249px",
    maxWidth: "250px",
    fontWeight: "600",
}));

const FlexInnerTitle1 = styled("div")(({ theme }) => ({
    display: "flex",
    alignItems: "center",
    justifyContent: "space-between",
    minWidth: "200px",
    maxWidth: "200px",
    fontWeight: "600",
}));

const FlexContent2 = styled("div")(({ theme }) => ({
    display: "flex",
    flexDirection: "row",
    fontSize: "17px",
}));

const FlexInnerTitle2 = styled("div")(({ theme }) => ({
    display: "flex",
    fontWeight: "600",
    gap: "5px",
    marginRight: "5px",
}));

const BoxDiv = styled("div")(({ theme }) => ({
    textAlign: "center",
}));

const Values = styled("div")(({ theme }) => ({
    marginLeft: "10px",
    fontWeight: "500",
    color: theme.palette.primary.dark,
}));

const ImageDiv = styled("div")(({ theme }) => ({
    display: "flex",
    alignItems: "center",
    width: "100%",
    flexWrap: "wrap",
    marginBottom: "10px",
}));

const ImageCell = styled("div")(({ theme }) => ({
    margin: "10px",
    width: "280px",
    borderRadius: "5px",
    overflow: "hidden",
    "& img": {
        width: "250px",
        height: "250px",
        objectFit: "cover",
        transition: "0.5s",
        boxShadow: theme.palette.primary.shadow,
        marginBottom: "10px",
        overflow: "hidden",
    },
    "& img:hover": {
        transform: "scale(1.1)",
    },
}));

const price_policyBox = styled(Box)(({ theme }) => ({
    display: "flex",
    marginBottom: "15px",
    "& h5": {
        margin: "5px",
    },
}));

const AppBarTabs = styled(AppBar)(({ theme }) => ({
    background: "#fff",
    color: theme.palette.primary.dark,
}));

function TabPanel(props) {
    const { children, value, index, ...other } = props;

    return (
        <div
            role="tabpanel"
            hidden={value !== index}
            id={`full-width-tabpanel-${index}`}
            aria-labelledby={`full-width-tab-${index}`}
            {...other}
        >
            {value === index && (
                <Box sx={{ p: 3 }}>
                    <Typography>{children}</Typography>
                </Box>
            )}
        </div>
    );
}

TabPanel.propTypes = {
    children: PropTypes.node,
    index: PropTypes.number.isRequired,
    value: PropTypes.number.isRequired,
};

function a11yProps(index) {
    return {
        id: `full-width-tab-${index}`,
        "aria-controls": `full-width-tabpanel-${index}`,
    };
}

const linesColumns = [
    { id: "sn", name: "SN" },
    { id: "product_name", name: "Item Name" },
    { id: "item_price", name: "Item Price" },
    { id: "quantity", name: "Quantity" },
    { id: "paid_amount", name: "Paid Amount" },
];

const ViewOrderDetail = (props) => {
    const theme = useTheme();
    const [value, setValue] = useState(0);
    const [open, setOpen] = useState(false);
    const [message, setMessage] = useState("");
    const [messageState, setMessageState] = useState("");
    const [togglerLanding, setTogglerLanding] = useState(false);
    const [imgIndex1, setImgIndex1] = useState(0);

    const handleChange = (event, newValue) => {
        setValue(newValue);
    };


    const [dialogDetails, setDialogDetails] = useState({
        open: true,
    });

    useEffect(() => {
        props.sendDetails(dialogDetails);
    }, [props, dialogDetails]);

    const handleImageTogglerLanding = (index) => {
        setImgIndex1(index);
        setTogglerLanding((prev) => !prev);
    };

    const handleClose = () => {
        setDialogDetails({
            ...dialogDetails,
            open: false,
        });
    };

    const displayText = (descriptionTexts) => {
        const textIsEmpty = descriptionTexts === null || descriptionTexts === "";
        return !textIsEmpty ? (
            parse(descriptionTexts)
        ) : (
            "-"
        );
    }


    const handleCloseSnack = (event, reason) => {
        if (reason === "clickaway") {
            return;
        }
        setOpen(false);
    };

    return (
        <div>
            <Dialog
                open={dialogDetails.open}
                onClose={handleClose}
                maxWidth="xl"
                fullWidth
                aria-labelledby="alert-dialog-title"
                aria-describedby="alert-dialog-description"
            >
                <StyledHeaderTitle id="alert-dialog-title">
                    <div>
                        View Order Details{" "}
                        {"(" +
                            //   (props.viewDetails.handle || "-") +
                            //   "/" +
                            (props.viewDetails.id || "-") +
                            ")"}
                    </div>
                    <IconButton onClick={handleClose}>
                        <Close />
                    </IconButton>
                </StyledHeaderTitle>
                {props.singleLoading ? (
                    <DialogContent>
                        <Grid container spacing={2}>
                            <Grid item xs={12} md={6}>
                                <Box p={3} sx={{ width: "100%" }}>
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                </Box>
                            </Grid>
                            <Grid item xs={12} md={6}>
                                <Box p={3} sx={{ width: "100%" }}>
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                </Box>
                            </Grid>
                        </Grid>
                    </DialogContent>
                ) : (
                    <DialogContent sx={{ padding: "0" }}>
                        <AppBarTabs position="static">
                            <Tabs
                                value={value}
                                onChange={handleChange}
                                indicatorColor="secondary"
                                textColor="inherit"
                                variant="fullWidth"
                                aria-label="full width tabs example"
                            >
                                <Tab label="Details" {...a11yProps(0)} />
                                <Tab label="Order Lines" {...a11yProps(1)} />
                                <Tab label="Customer Detail" {...a11yProps(1)} />

                            </Tabs>
                        </AppBarTabs>

                        <TabPanel value={value} index={0} dir={theme.direction}>
                            <Box>
                                <Grid container spacing={2}>
                                    {/* Left Side */}
                                    <Grid item xs={12} md={6}>
                                        <FlexContent>
                                            <FlexInnerTitle>
                                                <span>Order ID</span> <span> : </span>
                                            </FlexInnerTitle>
                                            <Values>{props.viewDetails.id || "-"}</Values>
                                        </FlexContent>
                                        <FlexContent>
                                            <FlexInnerTitle>
                                                <span>Order Type</span> <span> : </span>
                                            </FlexInnerTitle>
                                            <Values>{props.viewDetails.order_type || "-"}</Values>
                                        </FlexContent>

                                        <FlexContent>
                                            <FlexInnerTitle>
                                                <span>Order Status</span> <span> : </span>
                                            </FlexInnerTitle>
                                            <Values>{props.viewDetails.status || "-"}</Values>
                                        </FlexContent>
                                        <FlexContent>
                                            <FlexInnerTitle>
                                                <span>Company ID</span> <span> : </span>
                                            </FlexInnerTitle>
                                            <Values>{props.viewDetails.company_id || "-"}</Values>
                                        </FlexContent>
                                        <FlexContent>
                                            <FlexInnerTitle>
                                                <span>Site</span> <span> : </span>
                                            </FlexInnerTitle>
                                            <Values>
                                                {props.viewDetails.site?.name || "-"}
                                            </Values>
                                        </FlexContent>
                                        <FlexContent>
                                            <FlexInnerTitle>
                                                <span>Staff</span> <span> : </span>
                                            </FlexInnerTitle>
                                            <Values>{[props.viewDetails.staff?.first_name, props.viewDetails.staff?.last_name].filter(Boolean).join(' ')}</Values>
                                        </FlexContent>
                                        <FlexContent>
                                            <FlexInnerTitle>
                                                <span>Total</span> <span> : </span>
                                            </FlexInnerTitle>
                                            <Values>${props.viewDetails.total || "-"}</Values>
                                        </FlexContent>
                                        <FlexContent>
                                            <FlexInnerTitle>
                                                <span>Order Created Date</span> <span> : </span>
                                            </FlexInnerTitle>
                                            <Values>
                                                {props.viewDetails.ls_created_at_readable || "-"}
                                            </Values>
                                        </FlexContent>
                                    </Grid>

                                    {/* Left Side */}

                                    {/* Right Side */}
                                    <Grid item xs={12} md={6}>

                                    </Grid>
                                    {/* Right Side */}


                                </Grid>
                            </Box>
                        </TabPanel>

                        <TabPanel value={value} index={1} dir={theme.direction}>
                            <Box display={"flex"} justifyContent={"flex-end"} sx={{marginBottom:"10px"}}>
                                {/* <h3>Point Transection Details</h3> */}
                                <span><strong>Transaction Date:</strong> {moment(props.viewDetails.ls_created_at).format("ddd, DD MMM YYYY, h:mm A")} </span>
                            </Box>

                            <BasicTable
                                columns={linesColumns}
                                rows={props.viewDetails.lines}
                            />

                            <Box display={"flex"} justifyContent={"space-between"} sx={{ marginTop: "10px" }}>
                        
                                <Grid item xs={12} md={6}></Grid>

                                <Grid item xs={12} md={6} style={{ borderLeft: "1px solid #999", height: "100%" }}>
                                    <Box pl={2}>
                                        <FlexContent>
                                            <FlexInnerTitle1>
                                                <span>Total Amount</span> <span> : </span>
                                            </FlexInnerTitle1>
                                            <Values>${props.viewDetails.reports?.total_amount}</Values>
                                        </FlexContent>
                                        <FlexContent>
                                            <FlexInnerTitle1>
                                                <span>Total Order Discount</span> <span> : </span>
                                            </FlexInnerTitle1>
                                            <Values>${props.viewDetails.reports?.order_discount}</Values>
                                        </FlexContent>
                                        <FlexContent>
                                            <FlexInnerTitle1>
                                                <span>Total Paid Amount</span> <span> : </span>
                                            </FlexInnerTitle1>
                                            <Values>${props.viewDetails.reports?.paid_amount}</Values>
                                        </FlexContent>
                                        <FlexContent>
                                            <FlexInnerTitle1>
                                                <span>Total Gain Point</span> <span> : </span>
                                            </FlexInnerTitle1>
                                            <Values>{props.viewDetails.reports?.gain_point}</Values>
                                        </FlexContent>
                                        <FlexContent>
                                            <FlexInnerTitle1>
                                                <span>Redeemed Point</span> <span> : </span>
                                            </FlexInnerTitle1>
                                            <Values>{props.viewDetails.reports?.redeemed_point}</Values>
                                        </FlexContent>
                                        <FlexContent>
                                            <FlexInnerTitle1>
                                                <span>Current Point</span> <span> : </span>
                                            </FlexInnerTitle1>
                                            <Values>{props.viewDetails.reports?.current_point}</Values>
                                        </FlexContent>
                                        <FlexContent>
                                            <FlexInnerTitle1>
                                                <span>Product Redemption</span> <span> : </span>
                                            </FlexInnerTitle1>
                                            <Values>{props.viewDetails.reports?.product_redeemptions.map((p) => p).join(', ')}</Values>
                                        </FlexContent>
                                    </Box>
                                </Grid>
                            </Box>

                        </TabPanel>
                        <TabPanel value={value} index={2} dir={theme.direction}>
                            <Box>
                                {props.viewDetails.customer ? (
                                    <Grid container>
                                        <Grid item xs={12} md={6}>
                                            <FlexContent>
                                                <FlexInnerTitle>
                                                    <span>Customer ID</span> <span> : </span>
                                                </FlexInnerTitle>
                                                <Values>{props.viewDetails.customer?.id || "-"}</Values>
                                            </FlexContent>
                                            <FlexContent>
                                                <FlexInnerTitle>
                                                    <span>Company ID</span> <span> : </span>
                                                </FlexInnerTitle>
                                                <Values>{props.viewDetails.customer?.company_id || "-"}</Values>
                                            </FlexContent>
                                            <FlexContent>
                                                <FlexInnerTitle>
                                                    <span>LiteCard Member ID</span> <span> : </span>
                                                </FlexInnerTitle>
                                                <Values>{props.viewDetails.customer?.litecard_member_id || "-"}</Values>
                                            </FlexContent>
                                            <FlexContent>
                                                <FlexInnerTitle>
                                                    <span>Customer Name</span> <span> : </span>
                                                </FlexInnerTitle>
                                                <Values>{[props.viewDetails.customer?.first_name, props.viewDetails.customer?.last_name].filter(Boolean).join(' ')}</Values>
                                            </FlexContent>

                                            <FlexContent>
                                                <FlexInnerTitle>
                                                    <span>Customer Primary Address</span> <span> : </span>
                                                </FlexInnerTitle>
                                                <Values>{[props.viewDetails.customer?.primary_address, props.viewDetails.customer?.primary_city, props.viewDetails.customer?.primary_state, props.viewDetails.customer?.primary_postal_code, props.viewDetails.customer?.primary_country].filter(Boolean).join(', ')}</Values>
                                            </FlexContent>

                                            <FlexContent>
                                                <FlexInnerTitle>
                                                    <span>Customer Shipping Address</span> <span> : </span>
                                                </FlexInnerTitle>
                                                <Values>{[props.viewDetails.customer?.shipping_address, props.viewDetails.customer?.shipping_city, props.viewDetails.customer?.shipping_state, props.viewDetails.customer?.shipping_postal_code, props.viewDetails.customer?.shipping_country].filter(Boolean).join(', ')}</Values>
                                            </FlexContent>

                                            <FlexContent>
                                                <FlexInnerTitle>
                                                    <span>Primary Email Address</span> <span> : </span>
                                                </FlexInnerTitle>
                                                <Values>{props.viewDetails.customer?.email || "-"}</Values>
                                            </FlexContent>

                                            <FlexContent>
                                                <FlexInnerTitle>
                                                    <span>Phone</span> <span> : </span>
                                                </FlexInnerTitle>
                                                <Values>
                                                    {props.viewDetails.customer?.phone || "-"}
                                                </Values>
                                            </FlexContent>

                                        </Grid>
                                        <Grid item xs={12} md={6}>
                                            {/* <FlexContent>
                                            <FlexInnerTitle>
                                                <span>Tags</span> <span> : </span>
                                            </FlexInnerTitle>
                                            <Values>{JSON.parse(props.viewDetails.tags) || "-"}</Values>
                                        </FlexContent> */}
                                            <FlexContent>
                                            <FlexInnerTitle>
                                                <span>Loyalty Customer</span> <span> : </span>
                                            </FlexInnerTitle>
                                            <Values>
                                                {props.viewDetails.customer?.isLoyalty === 1 ? "Yes" : "No"}
                                            </Values>
                                        </FlexContent>

                                            <FlexContent>
                                            <FlexInnerTitle>
                                                <span>Current Points</span> <span> : </span>
                                            </FlexInnerTitle>
                                            <Values>
                                                {props.viewDetails.customer?.currentPoints || "-"}
                                            </Values>
                                        </FlexContent>
                                        <FlexContent>
                                            <FlexInnerTitle>
                                                <span>Points Redemption</span> <span> : </span>
                                            </FlexInnerTitle>
                                            <Values>
                                                {props.viewDetails.customer?.pointsRedemption || "-"}
                                            </Values>
                                        </FlexContent>
                                            <FlexContent>
                                                <FlexInnerTitle>
                                                    <span>Date Of Birth</span> <span> : </span>
                                                </FlexInnerTitle>
                                                <Values>
                                                    {props.viewDetails.customer?.dob
                                                        ? moment(props.viewDetails.customer.dob).format("ddd, DD MMM YYYY, h:mm A")
                                                        : "-"}
                                                </Values>
                                            </FlexContent>

                                            <FlexContent>
                                                <FlexInnerTitle>
                                                    <span>Customer Created Date</span> <span> : </span>
                                                </FlexInnerTitle>
                                                <Values>{moment(props.viewDetails.customer?.ls_created_at).format("ddd, DD MMM YYYY, h:mm A")}</Values>
                                            </FlexContent>

                                            <FlexContent>
                                                <FlexInnerTitle>
                                                    <span>Customer Updated Date</span> <span> : </span>
                                                </FlexInnerTitle>
                                                <Values>{moment(props.viewDetails.customer?.ls_updated_at).format("ddd, DD MMM YYYY, h:mm A")}</Values>
                                            </FlexContent>
                                        </Grid>
                                    </Grid>
                                ) : <strong>No Customer Details Available</strong>}
                            </Box>

                        </TabPanel>


                    </DialogContent>
                )}
                <DialogActions>
                    <Button onClick={handleClose} variant="outlined" color="primary">
                        Close
                    </Button>
                </DialogActions>
            </Dialog>

            {/* {openPolicyDialog && (
        <ViewPolicyDialog
          singleLoading={singleLoading}
          viewDetails={viewDetails}
          sendDetails={sendDetails}
        />
      )} */}

            <Snackbar
                autoHideDuration={3000}
                anchorOrigin={{ vertical: "top", horizontal: "right" }}
                open={open}
                onClose={handleCloseSnack}
            >
                <Alert
                    onClose={handleCloseSnack}
                    severity={messageState}
                    sx={{ width: "100%" }}
                >
                    {message}
                </Alert>
            </Snackbar>
        </div>
    );
};

export default ViewOrderDetail;
