import * as React from 'react';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableContainer from '@mui/material/TableContainer';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import Paper from '@mui/material/Paper';
import { Box, Button, IconButton, Menu, MenuItem } from '@mui/material';
import { Check, Clear, MoreVert } from '@mui/icons-material';
import moment from 'moment';


export default function BasicTable(props) {
    const [anchorEl, setAnchorEl] = React.useState(null);
    const [selectedRow, setSelectedRow] = React.useState(null);

    const handleMenuClick = (event, row) => {
        setAnchorEl(event.currentTarget);
        setSelectedRow(row);
    };

    const handleMenuClose = () => {
        setAnchorEl(null);
        setSelectedRow(null);
    };

    const handleViewSOH = () => {
        if (props.handleView && selectedRow) {
            props.handleView(selectedRow);
        }
        handleMenuClose();
    };

    const handleMapping = () => {
        if (props.handleMapping && selectedRow) {
            props.handleMapping(selectedRow);
        }
        handleMenuClose();
    };

    return (
        <TableContainer component={Paper} style={{ overflowY: "auto", minHeight: "180px" }}>
            <Table sx={{ minWidth: 650 }} aria-label="simple table">
                <TableHead>
                    <TableRow>
                        {props.columns.map(col => (
                            <TableCell style={{ fontWeight: "700" }}>{col.name}</TableCell>
                        ))}
                    </TableRow>
                </TableHead>
                {props.rows?.length > 0 ?
                    <TableBody>
                        {props.rows.map((row, index1) => {
                            // var allStockTotal = 0;

                            // if (row.stock_on_hand && row.stock_on_hand[5]) {
                            //     var newStockArr = Object.values(row.stock_on_hand);
                            //     newStockArr.map((stock, index) => {
                            //         return allStockTotal = allStockTotal + stock.actualSOH
                            //     });
                            // }
                            return (
                                <TableRow
                                    key={row.name}
                                    onClick={() => (props.handleView && row.isRedeemedTransection === 1) ? props.handleView(row) : ""}
                                    sx={{
                                        cursor: (props.handleView && row.isRedeemedTransection === 1) ? "pointer" : "default", '&:last-child td, &:last-child th': { border: 0 },
                                        background: row.isRedeemedTransection === 1 ? "#d1ffbd" : ""
                                    }}
                                >
                                    {props.columns.map((col, index) => (
                                        col.id === "reason" ?
                                            <TableCell component="th" scope="row" style={{ width: "100%", display: "flex" }}>
                                                {row.reportType !== "exceptionalOrder" ? <Check color='primary' fontSize='small' /> : <Clear color='error' fontSize='small' />}
                                                {row[col.id]}
                                            </TableCell>
                                            :
                                            col.id === "image" ? (

                                                <TableCell component="th" scope="row">
                                                    <img
                                                        src={row.image}
                                                        style={{
                                                            width: "100px",
                                                            height: "100px",
                                                            objectFit: "cover",
                                                        }}
                                                        alt={row.firstname}
                                                    />
                                                </TableCell>
                                            ) :
                                                <TableCell component="th" scope="row">
                                                    {col.id === "sn" ? (index1 + 1) :
                                                        col.id === "erply_variation" ? row[col.id]?.name :
                                                            col.id === "erply_product" ? (row.erply_product ? row.erply_product.name : row.erply_variation?.name) :
                                                                col.id === "assign_to_branch" ? row[col.id][0]?.branch?.name :
                                                                    col.id === "erpBranchName" ? row.branch_details ? row.branch_details?.branchName : row.erpBranch?.branchName :
                                                                        col.id === "erpAccountNameNetSuite" ? row.customer?.erpAccountName :
                                                                            col.id === "stockName" ? (row.stock.stockName || row.stock.ciStockName || row.stock.stockNameDefault) :
                                                                                col.id === "actualSOH" ? Math.max(row.actualSOH, 0) :
                                                                                    col.id === "LOCNO" ? row.LOCATION.LOCNO :
                                                                                        col.id === "LNAME" ? row.LOCATION.LNAME :
                                                                                            col.id === "stockCode" ? (row.stockCode || row.ciStockCode || row.stock.stockCode) :
                                                                                                col.id === "stockNameDefault" ? (row.stockNameDefault || row.ciStockName) :
                                                                                                    col.id === "erpAccountCustomerID" ? row.erpAccountCustomerID.erpAccountCustomerID :
                                                                                                        col.id === "erpAccountCustomerIDNet" ? row.customer.erpAccountCustomerID :
                                                                                                            col.id === "erpAccountName" ? row.erpAccountCustomerID.erpAccountName :
                                                                                                                col.id === "erpAccountNameNet" ? row.customer.erpAccountName :
                                                                                                                    col.id === "warehouse_details" ? row.warehouse_details.name :
                                                                                                                        col.id === "shopify_updated_at" ? moment(row.shopify_updated_at).format("ddd, DD MMM YYYY, h:mm A") :
                                                                                                                            col.id === "updated_at" ? moment(row.updated_at).format("ddd, DD MMM YYYY, h:mm A") :
                                                                                                                                col.id === "created_at" ? moment(row.created_at).format("ddd, DD MMM YYYY, h:mm A") :
                                                                                                                                    col.id === "order_created_at" ? moment(row.order_created_at).format("ddd, DD MMM YYYY, h:mm A") :
                                                                                                                                        col.id === "ls_created_at" ? moment(row.ls_created_at).format("ddd, DD MMM YYYY, h:mm A") :
                                                                                                                                            col.id === "applied_time" ? moment(row.applied_time).format("ddd, DD MMM YYYY, h:mm A") :
                                                                                                                                                col.id === "endDate" ? moment(row.endDate).format("ddd, DD MMM YYYY, h:mm:ss a") :
                                                                                                                                                    col.id === "redeemed_date" ? (row.redeemed_date === "" ? "-" : moment(row.redeemed_date).format("ddd, DD MMM YYYY, h:mm:ss a")) :
                                                                                                                                                        col.id === "amount" ? <span style={{ color: row.reportType !== "exceptionalOrder" ? 'inherit' : 'red' }}>{parseInt(row.amount)}</span> :
                                                                                                                                                            col.id === "unit_price" ? <span style={{ color: row.reportType !== "exceptionalOrder" ? 'inherit' : 'red' }}>{("$" + row.unit_price)}</span> :
                                                                                                                                                                col.id === "total_price" ? <span style={{ color: row.reportType !== "exceptionalOrder" ? 'inherit' : 'red' }}>{("$" + row.total_price)}</span> :
                                                                                                                                                                    col.id === "finalPriceWithVAT" ? "$" + parseFloat(row.finalPriceWithVAT).toFixed(2) :
                                                                                                                                                                        col.id === "price" ? ("$" + parseFloat(row.price).toFixed(2)) :
                                                                                                                                                                            col.id === "cost_price_ex_tax" ? ("$" + parseFloat(row.cost_price_ex_tax).toFixed(2)) :
                                                                                                                                                                                col.id === "cost_price" ? ("$" + parseFloat(row.cost_price).toFixed(2)) :
                                                                                                                                                                                    col.id === "price1" ? ("$" + parseFloat(row.price).toFixed(2)) :
                                                                                                                                                                                        col.id === "total" ? ("$" + parseFloat(row.total).toFixed(2)) :
                                                                                                                                                                                            col.id === "paid_amount" ? ("$" + parseFloat(row.paid_amount).toFixed(2)) :
                                                                                                                                                                                                col.id === "item_price" ? ("$" + parseFloat(row.item_price).toFixed(2)) :
                                                                                                                                                                                                    col.id === "paid" ? ("$" + parseFloat(row.total).toFixed(2)) :
                                                                                                                                                                                                        col.id === "latestCost" ? ("$" + (row.latestCost.toFixed(2))) :
                                                                                                                                                                                                            col.id === "averageCost" ? ("$" + (row.averageCost.toFixed(2))) :
                                                                                                                                                                                                                col.id === "supplierCost" ? ("$" + (row.supplierCost.toFixed(2))) :
                                                                                                                                                                                                                    col.id === "Cost" ? ("$" + parseFloat(row.Cost).toFixed(2)) :
                                                                                                                                                                                                                        col.id === "Unit_Price" ? ("$" + parseFloat(row.Unit_Price).toFixed(2)) :
                                                                                                                                                                                                                            col.id === "Published_Price" ? ("$" + parseFloat(row.Published_Price).toFixed(2)) :
                                                                                                                                                                                                                                col.id === "Discount_Amount" ? ("$" + parseFloat(row.Discount_Amount).toFixed(2)) :
                                                                                                                                                                                                                                    col.id === "active" ? (row.stock.Active === "Y" ? <Check fontSize='small' color="primary" /> : <Clear fontSize='small' color='error' />) :
                                                                                                                                                                                                                                        col.id === "is_redeemed" ? (row.is_redeemed === 1 ? <Check fontSize='small' color="primary" /> : <Clear fontSize='small' color='error' />) :
                                                                                                                                                                                                                                            col.id === "actions" ? (
                                                                                                                                                                                                                                                <>
                                                                                                                                                                                                                                                    <IconButton onClick={(e) => handleMenuClick(e, row)}>
                                                                                                                                                                                                                                                        <MoreVert />
                                                                                                                                                                                                                                                    </IconButton>
                                                                                                                                                                                                                                                    <Menu
                                                                                                                                                                                                                                                        anchorEl={anchorEl}
                                                                                                                                                                                                                                                        open={Boolean(anchorEl)}
                                                                                                                                                                                                                                                        onClose={handleMenuClose}
                                                                                                                                                                                                                                                        PaperProps={{
                                                                                                                                                                                                                                                            elevation: 1, // reduce shadow or use 0 to remove
                                                                                                                                                                                                                                                            sx: {
                                                                                                                                                                                                                                                                boxShadow: '0px 2px 4px rgba(0, 0, 0, 0.1)', // or use a subtle shadow like '0px 2px 4px rgba(0, 0, 0, 0.1)'
                                                                                                                                                                                                                                                            },
                                                                                                                                                                                                                                                        }}
                                                                                                                                                                                                                                                    >
                                                                                                                                                                                                                                                        <MenuItem onClick={handleViewSOH}>View SOH</MenuItem>
                                                                                                                                                                                                                                                        {props.isMappingEnabled === 1 && (
                                                                                                                                                                                                                                                            <MenuItem onClick={handleMapping}>Mapping</MenuItem>
                                                                                                                                                                                                                                                        )}
                                                                                                                                                                                                                                                    </Menu>
                                                                                                                                                                                                                                                </>
                                                                                                                                                                                                                                            ) :
                                                                                                                                                                                                                                                col.id === "viewSoh" ? <Button variant="outlined" onClick={() => props.handleView(row)}> View SOH </Button> :
                                                                                                                                                                                                                                                    col.id === "mapping" && props.isMappingEnabled === 1 ? <Button variant="outlined" onClick={() => props.handleMapping(row)}> Mapping </Button> :
                                                                                                                                                                                                                                                        row[col.id]
                                                    }
                                                </TableCell>

                                    ))}
                                </TableRow>
                            )
                        })}
                    </TableBody>
                    :
                    <TableBody>
                        <TableRow style={{ position: "relative", width: "100%" }}>
                            <Box textAlign="center" style={{ position: "absolute", right: "50%" }} mt={3}>
                                No Data Found
                            </Box>
                        </TableRow>
                    </TableBody>
                }
            </Table>
        </TableContainer>
    );
}
