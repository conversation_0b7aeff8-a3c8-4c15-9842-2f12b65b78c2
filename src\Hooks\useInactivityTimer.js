import { useState, useCallback, useEffect, useRef } from 'react';

const useInactivityTimer = (timeout, onInactive, props) => {
  const [lastActivity, setLastActivity] = useState(Date.now());
  const resetTimer = useRef(() => setLastActivity(Date.now()));

  const handleActivity = useCallback(() => {
    // Only update state if enough time has passed to prevent excessive re-renders
    if (Date.now() - lastActivity > 1000) {
      setLastActivity(Date.now());
    }
  }, [lastActivity]);

  useEffect(() => {
    if (props === "/login") return;

    const events = ['mousemove', 'keydown', 'mousedown', 'touchstart', 'scroll', 'keypress', 'click', 'dblclick'];
    events.forEach(event => window.addEventListener(event, handleActivity));

    return () => {
      events.forEach(event => window.removeEventListener(event, handleActivity));
    };
  }, [handleActivity, props]);

  useEffect(() => {
    if (props === "/login") return;

    const interval = setInterval(() => {
      if (Date.now() - lastActivity >= timeout) {
        onInactive();
      }
    }, 1000);

    return () => clearInterval(interval);
  }, [lastActivity, timeout, onInactive, props]);

  return resetTimer.current;
};

export default useInactivityTimer;

