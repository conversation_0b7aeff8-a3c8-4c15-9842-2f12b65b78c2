import {
    AppBar,
    Box,
    Button,
    Checkbox,
    CircularProgress,
    Dialog,
    DialogActions,
    DialogContent,
    DialogTitle,
    FormControl,
    FormHelperText,
    Grid,
    InputLabel,
    MenuItem,
    Select,
    styled,
    TextField,
    Typography,
    useTheme
} from "@mui/material";
import moment from "moment/moment";
import { useEffect, useState } from "react";
import Tabs from "@mui/material/Tabs";
import Tab from "@mui/material/Tab";
import * as React from 'react';
import useTokenRefresh from "../../../Hooks/useTokenRefresh";
import httpclient from "../../../Utils";
import PropTypes from "prop-types";
import TableComponent from "../../../Components/TableComponent";

const StyledHeaderTitle = styled(DialogTitle)(({ theme }) => ({
    background: theme.palette.primary.main,
    color: "#fff",
    position: "relative",
    "& button": {
        position: "absolute",
        right: "15px",
        top: "15px",
        color: "#fff",
    },
}));

const SearchBox = styled(Box)(({ theme }) => ({
    "& input": {
        padding: "10px",
    },
}));

const FlexContent = styled("div")(({ theme }) => ({
    display: "flex",
    flexDirection: "row",
    fontSize: "17px",
    marginBottom: "10px",
    alignItems: "flex-start",
}));

const FlexInnerTitle = styled("div")(({ theme }) => ({
    display: "flex",
    alignItems: "center",
    justifyContent: "space-between",
    minWidth: "270px",
    maxWidth: "270px",
    fontWeight: "600",
}));


const Values = styled("div")(({ theme }) => ({
    marginLeft: "15px",
    fontWeight: "500",
    color: theme.palette.primary.dark,
}));

const AppBarTabs = styled(AppBar)(({ theme }) => ({
    background: "#fff",
    color: theme.palette.primary.dark,
}));

function TabPanel(props) {
    const { children, value, index, ...other } = props;

    return (
        <div
            role="tabpanel"
            hidden={value !== index}
            id={`full-width-tabpanel-${index}`}
            aria-labelledby={`full-width-tab-${index}`}
            {...other}
        >
            {value === index && (
                <Box sx={{ p: 3 }}>
                    <Typography>{children}</Typography>
                </Box>
            )}
        </div>
    );
}

TabPanel.propTypes = {
    children: PropTypes.node,
    index: PropTypes.number.isRequired,
    value: PropTypes.number.isRequired,
};

function a11yProps(index) {
    return {
        id: `full-width-tab-${index}`,
        "aria-controls": `full-width-tabpanel-${index}`,
    };
}

const columns = [
    { id: "sn", name: "SN" },
    { id: "lite_card_member_id", name: "LiteCard Member ID" },
    { id: "name", name: "Name" },
    { id: "email", name: "Email" },
    { id: "store_name", name: "Store Name" },

];

const configRowPerPage = JSON.parse(localStorage.getItem("configRowPerPage"));

const ViewPushNotification = (props) => {
    const { getTokenRefreshed: refresh, open: tokenOpen, overlay: overlay, setOpen: setTokenOpen, message: tokenMessage, messageState: tokenMessageState } = useTokenRefresh();

    const [dialogDetails, setDialogDetails] = useState({
        open: true,
        success: false,
    });
    const theme = useTheme();
    const [value, setValue] = useState(0);
    const [open, setOpen] = useState(false);
    const [message, setMessage] = useState("");
    const [messageState, setMessageState] = useState("");
    const [loading, setLoading] = useState(false);
    const [direction, setDirection] = useState(false);
    const [currentColumn, setCurrentColumn] = useState("");
    const [page, setPage] = useState(1);
    const [from, setFrom] = useState(1);
    const [to, setTo] = useState(
        configRowPerPage && configRowPerPage
            ? configRowPerPage && configRowPerPage
            : 20
    );

    const [rowsPerPage, setRowsPerPage] = useState(
        configRowPerPage && configRowPerPage
            ? configRowPerPage && configRowPerPage
            : 20
    );
    const [total, setTotal] = useState("");
    const [rows, setRows] = useState([]);
    const [storeList, setStoreList] = useState([]);
    const [loadingSearch, setLoadingSearch] = useState(false);
    const [loadingSearchEmail, setLoadingSearchEmail] = useState(false);
    const [loadingSearchStore, setLoadingSearchStore] = useState(false);
    const [searchMember, setSearchMember] = useState("");
    const [searchMemberEmail, setSearchMemberEmail] = useState("");
    const [searchStores, setSearchStores] = useState([]);

    const handleChange = (event, newValue) => {
        setValue(newValue);
    };
    const handleChangeStore = (e) => {
        const { name, value } = e.target;
        setSearchStores(value);
    };

    console.log("search stores", searchStores)

    useEffect(() => {
        getAllPushNotification(searchMember, searchMemberEmail, searchStores);
    }, [searchMember, searchMemberEmail, searchStores]);

    useEffect(() => {
        getAllPushNotification();
        getStoreList();
    }, []);

    const getStoreList = () => {
        setLoadingSearchStore(true);
        httpclient.get(`request-response?requestName=lightspeed/loyalty/store-list`).then(({ data }) => {
            if (data.status === 200) {
                setStoreList(data.data);
                setLoadingSearchStore(false);
            } else {
                setLoadingSearchStore(false);
            }
        })
    };




    const getAllPushNotification = (searchMember, searchMemberEmail, searchStores) => {
        { searchMember ? setLoadingSearch(true) : searchMemberEmail ? setLoadingSearchEmail(true) : searchStores?.length > 0 ? setLoadingSearchStore(true) : setLoading(true) };
        httpclient.get(`request-response?requestName=lightspeed/push-notification/member-list/${props.viewDetails.id}&pagination=${rowsPerPage
            }${(searchStores?.length > 0) ? `&store_id=${searchStores}` : ""}
            ${(searchMember !== "" && searchMember !== undefined) ? `&lite_card_member_id=${searchMember}` : ""}
            ${(searchMemberEmail !== "" && searchMemberEmail !== undefined) ? `&email=${searchMemberEmail}` : ""}`).then(({ data }) => {
                if (data.status === 200) {
                    setRows(data.data);
                    setTotal(data.meta.total);
                    setRowsPerPage(parseInt(data.meta.per_page));
                    setPage(data.meta.current_page);
                    setFrom(data.meta.from);
                    setTo(data.meta.to);
                    { searchMember ? setLoadingSearch(false) : searchMemberEmail ? setLoadingSearchEmail(false) : searchStores?.length > 0 ? setLoadingSearchStore(false) : setLoading(false) };
                } else {
                    setOpen(true);
                    setMessage(data.message);
                    setMessageState("error");
                    { searchMember ? setLoadingSearch(false) : searchMemberEmail ? setLoadingSearchEmail(false) : searchStores?.length > 0 ? setLoadingSearchStore(false) : setLoading(false) };
                }

            }).catch((err) => {
                if (err.response.status === 401) {
                    refresh();
                    setOpen(tokenOpen);
                    setMessage(tokenMessage);
                    setMessageState("error");
                } else if (err.response.status === 422) {
                    const errorMessages = Object.values(err.response.data.errors).flat();
                    setOpen(true);
                    setMessage(errorMessages);
                    setMessageState("error");
                    setLoading(false);
                } else if (err.response.status === 400) {
                    const errorMessages = Object.values(err.response.data.errors).flat();
                    setOpen(true);
                    setMessage(errorMessages);
                    setMessageState("error");
                    setLoading(false);

                } else {
                    setOpen(true);
                    setMessage(err.response.data.message);
                    setMessageState("error");
                    setLoading(false);
                }
            })
    }


    const handleSort = (column) => {
        setDirection((prevDirection) => !prevDirection);
        setCurrentColumn(column);
        setLoading(true);
        httpclient
            .get(
                `request-response?requestName=lightspeed/push-notification/member-list/${props.viewDetails.id}&sort[0]=${column}:${!direction ? "asc" : "desc"
                }&pagination=${rowsPerPage}`
            )
            .then(({ data }) => {
                if (data.status === 200) {
                    setRows(data.data);
                    setTotal(data.meta.total);
                    setRowsPerPage(parseInt(data.meta.per_page));
                    setPage(data.meta.current_page);
                    setFrom(data.meta.from);
                    setTo(data.meta.to);
                    setLoading(false);
                } else {
                    setOpen(true);
                    setMessage(data.message);
                    setMessageState("error");
                    setLoading(false);
                }

            }).catch((err) => {
                if (err.response.status === 401) {
                    refresh();
                    setOpen(tokenOpen);
                    setMessage(tokenMessage);
                    setMessageState("error");
                } else if (err.response.status === 422) {
                    const errorMessages = Object.values(err.response.data.errors).flat();
                    setOpen(true);
                    setMessage(errorMessages);
                    setMessageState("error");
                    setLoading(false);
                } else if (err.response.status === 400) {
                    const errorMessages = Object.values(err.response.data.errors).flat();
                    setOpen(true);
                    setMessage(errorMessages);
                    setMessageState("error");
                    setLoading(false);

                } else {
                    setOpen(true);
                    setMessage(err.response.data.message);
                    setMessageState("error");
                    setLoading(false);
                }
            })
    };

    const handleChangePage = (e, page) => {
        setLoading(true);
        httpclient
            .get(
                `request-response?requestName=lightspeed/push-notification/member-list/${props.viewDetails.id}&pagination=${rowsPerPage}&page=${page}`
            )
            .then(({ data }) => {
                if (data.status === 200) {
                    setRows(data.data);
                    setTotal(data.meta.total);
                    setRowsPerPage(parseInt(data.meta.per_page));
                    setPage(data.meta.current_page);
                    setFrom(data.meta.from);
                    setTo(data.meta.to);
                    setLoading(false);
                } else {
                    setOpen(true);
                    setMessage(data.message);
                    setMessageState("error");
                    setLoading(false);
                }

            }).catch((err) => {
                if (err.response.status === 401) {
                    refresh();
                    setOpen(tokenOpen);
                    setMessage(tokenMessage);
                    setMessageState("error");
                } else if (err.response.status === 422) {
                    const errorMessages = Object.values(err.response.data.errors).flat();
                    setOpen(true);
                    setMessage(errorMessages);
                    setMessageState("error");
                    setLoading(false);
                } else if (err.response.status === 400) {
                    const errorMessages = Object.values(err.response.data.errors).flat();
                    setOpen(true);
                    setMessage(errorMessages);
                    setMessageState("error");
                    setLoading(false);

                } else {
                    setOpen(true);
                    setMessage(err.response.data.message);
                    setMessageState("error");
                    setLoading(false);
                }
            })
    };

    const handleChangeRowsPerPage = (event) => {
        setRowsPerPage(+event.target.value);
        setLoading(true);

        localStorage.setItem("configRowPerPage", event.target.value);

        httpclient
            .get(
                `request-response?requestName=lightspeed/push-notification/member-list/${props.viewDetails.id}&pagination=${+event
                    .target.value}&page=${1}`
            )
            .then(({ data }) => {
                setLoading(true);
                if (data.status === 200) {
                    setRows(data.data);
                    setTotal(data.meta.total);
                    setRowsPerPage(parseInt(data.meta.per_page));
                    setFrom(data.meta.from);
                    setTo(data.meta.to);
                    setPage(data.meta.current_page);
                    setLoading(false);
                } else {
                    setOpen(true);
                    setMessage(data.message);
                    setMessageState("error");
                    setLoading(false);
                }

            }).catch((err) => {
                if (err.response.status === 401) {
                    refresh();
                    setOpen(tokenOpen);
                    setMessage(tokenMessage);
                    setMessageState("error");
                } else if (err.response.status === 422) {
                    const errorMessages = Object.values(err.response.data.errors).flat();
                    setOpen(true);
                    setMessage(errorMessages);
                    setMessageState("error");
                    setLoading(false);
                } else if (err.response.status === 400) {
                    const errorMessages = Object.values(err.response.data.errors).flat();
                    setOpen(true);
                    setMessage(errorMessages);
                    setMessageState("error");
                    setLoading(false);

                } else {
                    setOpen(true);
                    setMessage(err.response.data.message);
                    setMessageState("error");
                    setLoading(false);
                }
            })
    };

    const handleClose = () => {
        setDialogDetails({
            ...dialogDetails,
            open: false,
        });
        props.handleClosePushNotification();
    };

    return (
        <div>
            <Dialog
                open={dialogDetails.open}
                onClose={handleClose}
                maxWidth="xl"
                fullWidth
                aria-labelledby="alert-dialog-title"
                aria-describedby="alert-dialog-description"
            >
                <StyledHeaderTitle id="alert-dialog-title">
                    {`View Push Notification Detail (${props.viewDetails.title})`}
                </StyledHeaderTitle>
                <DialogContent sx={{ padding: "0" }}>
                    <AppBarTabs position="static">
                        <Tabs
                            value={value}
                            onChange={handleChange}
                            indicatorColor="secondary"
                            textColor="inherit"
                            variant="fullWidth"
                            aria-label="full width tabs example"
                        >
                            <Tab label="Notification Details" {...a11yProps(0)} />
                            <Tab label="Member Details" {...a11yProps(1)} />

                        </Tabs>
                    </AppBarTabs>

                    <TabPanel value={value} index={0} dir={theme.direction}>
                        <Box p={3}>
                            <Grid container spacing={2}>
                                {/* Left Side */}
                                <Grid item xs={12} md={6}>
                                    <FlexContent>
                                        <FlexInnerTitle>
                                            <span>Title</span> <span> : </span>
                                        </FlexInnerTitle>
                                        <Values>{props.viewDetails.title || "-"}</Values>
                                    </FlexContent>

                                    <FlexContent>
                                        <FlexInnerTitle>
                                            <span>Push To LiteCard?</span> <span> : </span>
                                        </FlexInnerTitle>
                                        <Values>{props.viewDetails.is_created_schedule === 1 ? "Yes" : "No"}</Values>
                                    </FlexContent>



                                    <FlexContent>
                                        <FlexInnerTitle>
                                            <span>Message Send By Type</span> <span> : </span>
                                        </FlexInnerTitle>
                                        <Values>{props.viewDetails.message_send_by_type || "-"}</Values>
                                    </FlexContent>

                                    <FlexContent>
                                        <FlexInnerTitle>
                                            <span>{props.viewDetails.message_send_by_type.charAt(0).toUpperCase() + props.viewDetails.message_send_by_type.slice(1)} Lists</span> <span> : </span>
                                        </FlexInnerTitle>
                                        <Values>
                                            {props.viewDetails.sendNotificationTypeList
                                                .map(item => item.value)
                                                .join(", ")}
                                        </Values>
                                    </FlexContent>

                                    <FlexContent>
                                        <FlexInnerTitle>
                                            <span>Message</span> <span> : </span>
                                        </FlexInnerTitle>
                                        <Values>{props.viewDetails.message || "-"}</Values>
                                    </FlexContent>

                                    <FlexContent>
                                        <FlexInnerTitle>
                                            <span>Scheduled Date</span> <span> : </span>
                                        </FlexInnerTitle>
                                        <Values>{moment(props.viewDetails.schedule_date).format("ddd, DD MMM YYYY, h:mm A")}</Values>
                                    </FlexContent>


                                </Grid>
                                <Grid item xs={12} md={6}>
                                    {props.viewDetails.is_created_schedule === 1 &&
                                        <>
                                            <FlexContent>
                                                <FlexInnerTitle>
                                                    <span>Push To LiteCard By</span> <span> : </span>
                                                </FlexInnerTitle>
                                                <Values>{props.viewDetails.push_to_lite_card_by || "-"}</Values>
                                            </FlexContent>

                                            <FlexContent>
                                                <FlexInnerTitle>
                                                    <span>Push To LiteCard Date</span> <span> : </span>
                                                </FlexInnerTitle>
                                                <Values>{moment(props.viewDetails.push_to_lite_card_date).format("ddd, DD MMM YYYY, h:mm A")}</Values>
                                            </FlexContent>
                                        </>
                                    }

                                    {props.viewDetails.notification_created_by &&
                                        <>
                                            <FlexContent>
                                                <FlexInnerTitle>
                                                    <span>Notification Created By</span> <span> : </span>
                                                </FlexInnerTitle>
                                                <Values>{props.viewDetails.notification_created_by || "-"}</Values>
                                            </FlexContent>

                                            <FlexContent>
                                                <FlexInnerTitle>
                                                    <span>Notification Created Date</span> <span> : </span>
                                                </FlexInnerTitle>
                                                <Values>{moment(props.viewDetails.notification_created_date).format("ddd, DD MMM YYYY, h:mm A")}</Values>
                                            </FlexContent>
                                        </>
                                    }

                                    {props.viewDetails.notification_updated_by &&
                                        <>
                                            <FlexContent>
                                                <FlexInnerTitle>
                                                    <span>Notification Updated By</span> <span> : </span>
                                                </FlexInnerTitle>
                                                <Values>{props.viewDetails.notification_updated_by || "-"}</Values>
                                            </FlexContent>

                                            <FlexContent>
                                                <FlexInnerTitle>
                                                    <span>Notification Updated Date</span> <span> : </span>
                                                </FlexInnerTitle>
                                                <Values>{moment(props.viewDetails.notification_updated_date).format("ddd, DD MMM YYYY, h:mm A")}</Values>
                                            </FlexContent>
                                        </>
                                    }

                                </Grid>
                            </Grid>
                        </Box>
                    </TabPanel>
                    <TabPanel value={value} index={1} dir={theme.direction}>
                        <Box
                            pb={3}
                            display={"flex"}
                            alignItems={"center"}
                            flexDirection={"row"}
                            justifyContent={"space-between"}
                        >
                            <Box flex={"1"}>
                                <InputLabel>Search By LiteCard Member ID</InputLabel>
                                <SearchBox display={"flex"}>
                                    <TextField
                                        type="search"
                                        variant="outlined"
                                        name="lite_card_member_id"
                                        value={searchMember}
                                        onChange={(e) => setSearchMember(e.target.value)}
                                        helperText={(searchMember === "" || searchMember === undefined) ? "Press any key to search." : ""}
                                        sx={{ width: "300px", marginRight: "10px" }}
                                    />
                                    {loadingSearch ? <CircularProgress size={30} /> : ""}
                                </SearchBox>


                            </Box>
                            <Box flex={"1"}>
                                <InputLabel>Search By Email</InputLabel>
                                <SearchBox display={"flex"}>
                                    <TextField
                                        type="search"
                                        variant="outlined"
                                        name="email"
                                        value={searchMemberEmail}
                                        onChange={(e) => setSearchMemberEmail(e.target.value)}
                                        helperText={(searchMemberEmail === "" || searchMemberEmail === undefined) ? "Press any key to search." : ""}
                                        sx={{ width: "300px", marginRight: "10px" }}
                                    />
                                    {loadingSearchEmail ? <CircularProgress size={30} /> : ""}
                                </SearchBox>


                            </Box>
                            <Box flex={"1"}>
                                <InputLabel>Search By Stores</InputLabel>
                                <FormControl required fullWidth >

                                    <Select
                                        multiple
                                        value={searchStores || []}
                                        //label={loadingSearchStore ? "Please Wait.." : `Select Stores`}
                                        name="send_type_id"
                                        onChange={handleChangeStore}
                                        renderValue={(selected) =>
                                            storeList
                                                ?.filter((item) => selected.includes(item.id))
                                                .map((item) => item.name)
                                                .join(", ") || `Select Stores`
                                        }
                                    >
                                        <MenuItem value="">
                                            <em>Select Stores</em>
                                        </MenuItem>
                                        {storeList &&
                                            storeList.map((point) => (
                                                <MenuItem key={point.id} value={point.id}>
                                                    <Checkbox checked={searchStores.includes(point.id)} />
                                                    {point.name}
                                                </MenuItem>
                                            ))}
                                    </Select>

                                    {loadingSearchStore && (
                                        <CircularProgress
                                            size={24}
                                            style={{
                                                position: "absolute",
                                                top: "50%",
                                                right: 16,
                                                marginTop: -12,
                                                marginRight: 10,
                                            }}
                                        />
                                    )}
                                    <FormHelperText>{searchStores?.length === 0 ? "Choose any store to search." : ""}</FormHelperText>

                                </FormControl>

                            </Box>

                        </Box>
                        <Grid item xs={12}>
                            <TableComponent
                                columns={columns}
                                rows={rows}
                                sort={true}
                                //handleView={handleView}
                                handleSort={handleSort}
                                loading={loading}
                                handleChangeRowsPerPage={handleChangeRowsPerPage}
                                handleChangePage={handleChangePage}
                                direction={direction}
                                currentColumn={currentColumn}
                                page={page}
                                total={total && total}
                                fromTable={from}
                                toTable={to}
                                rowsPerPage={rowsPerPage}
                            />
                        </Grid>
                    </TabPanel>

                </DialogContent>
                <DialogActions styled={{ margin: "5px 10px" }}>
                    <Button onClick={handleClose} color="error" variant="contained" autoFocus>
                        Close
                    </Button>
                </DialogActions>
            </Dialog>

        </div>
    );
};

export default ViewPushNotification;
